version = 1
revision = 1
requires-python = ">=3.12"

[[package]]
name = "agenttools"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "netifaces" },
]

[package.metadata]
requires-dist = [{ name = "netifaces", specifier = ">=0.11.0" }]

[[package]]
name = "netifaces"
version = "0.11.0"
source = { registry = "https://mirrors.aliyun.com/pypi/simple/" }
sdist = { url = "https://mirrors.aliyun.com/pypi/packages/a6/91/86a6eac449ddfae239e93ffc1918cf33fd9bab35c04d1e963b311e347a73/netifaces-0.11.0.tar.gz", hash = "sha256:043a79146eb2907edf439899f262b3dfe41717d34124298ed281139a8b93ca32" }
