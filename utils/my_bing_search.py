from langchain_community.utilities.bing_search import BingSearchAPIWrapper

class MyBingSearchAPIWrapper(BingSearchAPIWrapper):
    def results(self, query: str, num_results: int):
        metadata_results = []
        results = self._bing_search_results(query, count=num_results)
        if len(results) == 0:
            return [{"Result": "No good Bing Search Result was found"}]
        for result in results:
            date_str = result.get("datePublished", None)
            if date_str:
                date_str = date_str[:10]
            metadata_result = {
                "snippet": result["snippet"],
                "title": result["name"],
                "link": result["url"],
                "date": date_str,
            }
            metadata_results.append(metadata_result)
        return metadata_results


search = MyBingSearchAPIWrapper(bing_subscription_key='********************************',
                              bing_search_url='https://api.bing.microsoft.com/v7.0/search')