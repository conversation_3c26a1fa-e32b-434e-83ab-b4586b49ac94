import mimetypes

from minio import Minio
from minio.error import S3Error

# 初始化 Minio 客户端
client = Minio("172.16.20.233:9001",
               access_key="admin",
               secret_key="jzzx123456",
               secure=False
               )


# 列出所有存储桶的方法
def list_buckets():
    try:
        buckets = client.list_buckets()
        for bucket in buckets:
            print(bucket.name, bucket.creation_date)
    except S3Error as err:
        print("S3 错误:", err)


# 上传文件的方法
def upload_object_file(bucket_name, object_name, data):
    try:
        # 获取文件流的长度
        data.seek(0, 2)  # 移动到文件流的末尾
        length = data.tell()  # 获取文件流的当前位置，这就是长度
        data.seek(0)  # 重置文件流位置到开头

        # 获取文件的 MIME 类型
        content_type, _ = mimetypes.guess_type(object_name)
        if content_type is None:
            content_type = 'application/octet-stream'  # 默认 MIME 类型
        client.put_object(bucket_name, object_name, data, length, content_type)
        print(f"文件 '{object_name}' 成功上传为 '{bucket_name}' 到存储桶中。")
    except S3Error as err:
        print("S3 错误:", err)


# 下载文件的方法
def download_object_file(bucket_name, object_name, file_path):
    try:
        client.fget_object(bucket_name, object_name, file_path)
        print(f"从存储桶 '{bucket_name}' 下载对象 '{object_name}' 成功到 '{file_path}'。")
    except S3Error as err:
        print("S3 错误:", err)


# 删除文件的方法
def delete_object_file(bucket_name, object_name):
    try:
        client.remove_object(bucket_name, object_name)
        print(f"对象 '{object_name}' 成功从存储桶 '{bucket_name}' 中删除。")
    except S3Error as err:
        print("S3 错误:", err)


# 示例使用方法.
if __name__ == "__main__":
    # list_buckets()

    # 使用with open打开目标文件
    with open("../uploaded.pdf", "rb") as file_data:
        # 替换为实际的存储桶名称、对象名称和文件路径
        bucket_name = "ai-agent-bucket"
        upload_object_file(bucket_name, "public/uploaded11.pdf", file_data)
        # download_object_file(bucket_name, "uploaded.pdf", "downloaded.pdf")
        # delete_file(bucket_name, "3-回购报告书导出模板沪主板科创板.docx")
