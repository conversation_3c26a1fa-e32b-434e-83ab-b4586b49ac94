import os
import tempfile

import markdown2
from xhtml2pdf import pisa
from xhtml2pdf.default import DEFAULT_FONT

from utils.email_utils import send_email

from reportlab.pdfbase.ttfonts import TTFont

from reportlab.pdfbase import pdfmetrics


def md_to_pdf(markdown_text: str):
    # 转换为HTML
    markdown_text = markdown_text.replace("\\n", "\n")
    html_text = markdown2.markdown(markdown_text, extras=["tables"])
    html_content = f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                }}
                th, td {{
                    border: 1px solid black;
                    padding: 8px;
                    text-align: center;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
            </style>
        </head>
        <body>
            {html_text}
        </body>
        </html> 
        '''
    # 将HTML转换为PDF，并保存到文件
    # 创建临时PDF文件
    temp_fd, temp_pdf_path = tempfile.mkstemp(suffix=".pdf", dir="static/temp")

    try:
        # 将HTML转换为PDF
        with open(temp_pdf_path, "w+b") as pdf_file:
            pdfmetrics.registerFont(TTFont('yh', 'static/fonts/msyh.ttf'))
            DEFAULT_FONT['helvetica'] = 'yh'
            pisa_status = pisa.CreatePDF(html_content, dest=pdf_file)
            if pisa_status.err:
                print("PDF生成失败")
                return

        # 发送邮件
        to_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]  # 替换为实际收件人邮箱
        subject = "年报解析"  # 邮件标题
        body = html_content  # 邮件内容
        file_name = "年报解析.pdf"  # 附件名称
        att_list = [{
            'file_path': temp_pdf_path,
            'file_name': file_name,
        }]

        send_email(to_emails, subject, body, att_list=att_list)

    finally:
        # 关闭文件描述符
        os.close(temp_fd)
        # 删除临时文件
        os.remove(temp_pdf_path)


if __name__ == '__main__':
    md_to_pdf("# 广西柳工机械股份有限公司 2023年年报深度解析 \n*本解析基于公开信息，不构成投资建议* \n\n## 一、公司概况 \n\n### （一）基本信息 \n- **成立日期**：1958年 \n- **注册资本**：7.51亿元 \n- **实际控制人**：广西壮族自治区国资委，持股51.48%。 \n- **股权结构**： \n - 控股股东：广西柳工集团，51.48%。 \n - 其他主要股东：无其他主要股东。 \n\n### （二）主营业务 \n- **核心产品/服务**：工程机械及关键零部件，占比90%；融资租赁，占比10%。 \n- **收入构成**： \n - 按产品：土石方机械：57.81%，其他工程机械及零部件：32.01%； \n - 按区域：境内：58.35%，境外：41.65%。 \n\n### （三）行业地位 \n- **市场份额**：第3名，市场占有率15%。 \n- **主要竞争对手**：徐工集团、三一重工。 \n\n---\n\n## 二、财务数据（近三年及最新一期） \n\n| 指标 | 2021年 | 2022年 | 2023年 | \n|--------------|---------------|----------------|---------------| \n| 营业收入 | 25,427亿元 | 26,491亿元 | 27,519亿元 | \n| 净利润 | 498亿元 | 599亿元 | 867亿元 | \n| 毛利率 | 18.56% | 18.99% | 19.91% | \n| 研发费用占比 | 3.50% | 3.30% | 3.80% | \n\n**关键趋势**： \n- 计算营收的年均增长率：+3.93%。 \n- 分析净利率的变化趋势：2022年2.26% → 2023年3.15%。 \n\n---\n\n## 三、业务与技术 \n\n### （一）核心技术 \n- **专利数量**：2882项，其中发明专利113项。 \n- **认证资质**：ISO9001、国家土方机械工程技术研究中心。 \n- **产研能力**： \n - 参与制定国家标准：211项。 \n - 建有实验室：柳工研究院。 \n\n### （二）供应链 \n- **前五大客户占比**：7.10%，最大客户1.98%。 \n- **前五大供应商占比**：未提供具体数据。 \n\n### （三）产能情况 \n| 生产基地 | 2022年产能利用率 | 2023年产能利用率 | \n|----------|------------------|------------------| \n| 柳州基地 | 90% | 95% | \n| 科特迪瓦 | 85% | 90% | \n\n---\n\n## 四、风险因素 \n\n### （一）市场风险 \n- 行业周期下行风险 \n- 国际紧张局势风险 \n\n### （二）经营风险 \n- 原材料价格波动，钢材占成本60%； \n- 加盟商管理风险，加盟店占比20%。 \n\n### （三）财务风险 \n- 应收账款周转天数：60天，行业平均45天。 \n\n---\n\n## 五、募投项目（如适用） \n\n| 项目名称 | 投资金额 | 建设周期 | 预期收益 | \n|-----------------------------------|------------|------------|----------| \n| 柳工挖掘机智慧工厂项目 | 10亿元 | 2023-2025 | +40%产能 | \n| 柳工装载机智能化改造项目 | 8亿元 | 2023-2024 | 提效20% | \n\n---\n\n## 六、总结与展望 \n\n✅ **核心优势**： \n- 研发能力：拥有2882项专利和211项国家标准制定权。 \n\n⚠ **潜在挑战**： \n- 国际市场不确定性及行业周期性波动。 \n\n📈 **增长点**： \n- 电动新产品及智能化解决方案，有望推动未来收入增长。")
