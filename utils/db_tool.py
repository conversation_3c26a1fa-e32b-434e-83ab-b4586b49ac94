# MySQL 数据库连接配置

import os
from contextlib import asynccontextmanager, contextmanager
from typing import Optional, AsyncIterator, Iterator, Self, List, Dict

import aiomysql
import mysql.connector
import mysql.connector
import pymysql
from langgraph.checkpoint.mysql.aio import AIOMySQLSaver
from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver
from langgraph.checkpoint.serde.base import SerializerProtocol
from langgraph.checkpoint.serde.types import TASKS

# MySQL 数据库连接配置
AGENT_DB_CONFIG = {
    'user': os.getenv('MYSQL_USER_AGENT'),
    'password': os.getenv('MYSQL_PASSWORD_AGENT'),
    'host': os.getenv('MYSQL_HOST_AGENT'),
    'port': os.getenv('MYSQL_PORT_AGENT'),
    'database': os.getenv('MYSQL_DB_AGENT'),
}

CLOUD_DB_CONFIG = {
    'user': os.getenv('MYSQL_USER_CLOUD'),
    'password': os.getenv('MYSQL_PASSWORD_CLOUD'),
    'host': os.getenv('MYSQL_HOST_CLOUD'),
    'port': os.getenv('MYSQL_PORT_CLOUD'),
    'database': os.getenv('MYSQL_DB_CLOUD'),
}

agent_checkpoint_db_config = {
    'user': os.getenv('MYSQL_USER_AGENT_CHECKPOINT'),
    'password': os.getenv('MYSQL_PASSWORD_AGENT_CHECKPOINT'),
    'host': os.getenv('MYSQL_HOST_AGENT_CHECKPOINT'),
    'port': int(os.getenv('MYSQL_PORT_AGENT_CHECKPOINT')),
    'database': os.getenv('MYSQL_DB_AGENT_CHECKPOINT'),
}


def get_db_connection(db):
    if db == 'AGENT':
        return mysql.connector.connect(**AGENT_DB_CONFIG)
    elif db == 'CLOUD':
        return mysql.connector.connect(**CLOUD_DB_CONFIG)
    elif db == 'AGENT_CHECKPOINT':
        return mysql.connector.connect(**agent_checkpoint_db_config)


def execute_query(db, query, dictionary=True):
    """
    执行查询语句
    @param db: 数据库类型，可选值：AGENT, CLOUD, CMS, CAPITAL_TDB, PLT
    @param query: 查询语句
    @param dictionary: 是否以字典形式返回结果，默认为 True
    """
    connection = get_db_connection(db)
    cursor = connection.cursor(dictionary=dictionary)
    cursor.execute(query)
    result = cursor.fetchall()
    cursor.close()
    connection.close()
    return result


SELECT_SQL = f"""
select thread_id,
       checkpoint,
       checkpoint_ns,
       checkpoint_id,
       parent_checkpoint_id,
       metadata,
       (select json_arrayagg(json_array(bl.channel, bl.type, bl.blob))
        from (select channel,
                     json_unquote(
                             json_extract(checkpoint, concat('$.channel_versions.', '"', channel, '"'))
                     ) as version
              from json_table(
                           json_keys(checkpoint, '$.channel_versions'),
                           '$[*]' columns (channel VARCHAR(150) PATH '$')
                   ) as channels) as channel_versions
                 inner join checkpoint_blobs bl on bl.channel = channel_versions.channel
            and bl.version = channel_versions.version
        where bl.thread_id = checkpoints.thread_id
          and bl.checkpoint_ns = checkpoints.checkpoint_ns) as channel_values,
       (select json_arrayagg(json_array(cw.task_id, cw.channel, cw.type, cw.blob, cw.idx))
        from checkpoint_writes cw
        where cw.thread_id = checkpoints.thread_id
          and cw.checkpoint_ns = checkpoints.checkpoint_ns
          and cw.checkpoint_id = checkpoints.checkpoint_id) as pending_writes,
       (select json_arrayagg(json_array(cw.task_id, cw.type, cw.blob, cw.idx))
        from checkpoint_writes cw
        where cw.thread_id = checkpoints.thread_id
          and cw.checkpoint_ns = checkpoints.checkpoint_ns
          and cw.checkpoint_id = checkpoints.parent_checkpoint_id
          and cw.channel = '{TASKS}')                       as pending_sends
from checkpoints """


class MyPyMySQLSaver(PyMySQLSaver):
    @classmethod
    @contextmanager
    def from_conn_string(cls, conn_string: str) -> Iterator[Self]:
        with pymysql.connect(
                **cls.parse_conn_string(conn_string),
                charset="utf8mb4",
                autocommit=True,
        ) as conn:
            yield cls(conn)


agent_checkpoint_db_uri = f"mysql://{agent_checkpoint_db_config['user']}:{agent_checkpoint_db_config['password']}@{agent_checkpoint_db_config['host']}:{agent_checkpoint_db_config['port']}/{agent_checkpoint_db_config['database']}?"

# 初始化
with MyPyMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
    with checkpointer._cursor() as cur:
        flag = cur.execute("SHOW TABLES LIKE 'checkpoints';")
        if not flag:
            checkpointer.setup()


class MyAIOMySQLSaver(AIOMySQLSaver):
    SELECT_SQL = SELECT_SQL

    @classmethod
    @asynccontextmanager
    async def from_conn_string(
            cls,
            conn_string: str,
            *,
            serde: Optional[SerializerProtocol] = None,
    ) -> AsyncIterator["MyAIOMySQLSaver"]:
        async with aiomysql.connect(
                **cls.parse_conn_string(conn_string),
                charset="utf8mb4",
                autocommit=True,
        ) as conn:
            # This seems necessary until https://github.com/PyMySQL/PyMySQL/pull/1119
            # is merged into aiomysql.
            # if os.getenv('env')=='prod':
            #     await conn.cursor().execute("SET collation_connection = 'utf8mb4_general_ci';")
            # await conn.cursor().execute("SET collation_connection = 'utf8mb4_general_ci';")
            yield cls(conn=conn, serde=serde)


def get_plate_by_company_code(company_code):
    sql = f"""
    select belongs_plate
    from sa_company
    where company_code = '{company_code}'
    """
    result = execute_query("CLOUD", sql)
    return result[0]['belongs_plate'] if result else ""


def get_case_company_by_condition(plate=None, industry3=None, industry2=None, industry1=None,
                                  market_value=None, trade_date=None, province_code=None, city_code=None,
                                  area_code=None, orgform_code=None,
                                  company_codes=None):
    sql = f"""
    SELECT
        sc.company_code,
        rc.repurstartdate,
        rc.repurenddate,
        CASE
            WHEN sc.belongs_plate = '00' THEN '深交所主板'
            WHEN sc.belongs_plate = '01' THEN '深交所中小板'
            WHEN sc.belongs_plate = '02' THEN '深交所创业板'
            WHEN sc.belongs_plate = '04' THEN '上交所主板'
            WHEN sc.belongs_plate = '07' THEN '上交所科创板'
            WHEN sc.belongs_plate = '09' THEN '北交所'
            WHEN sc.belongs_plate = '05' THEN '新三板'
            WHEN sc.belongs_plate = '06' THEN '非以上'
            ELSE ''
        END AS '所属板块',
        concat(p.area_name, ' ', p1.area_name, ' ', p2.area_name) as '地区',
        concat(maa.label_name, ' ', maa1.label_name, ' ', maa2.label_name) as '申万行业',
        sce.code_name as '企业性质',
        concat(sc.register_capital, '万元') as '注册资本'
    FROM sa_company sc
    LEFT JOIN sa_code sce ON sc.orgform = sce.code_value AND sce.code_no = 'ORGFORM'
    LEFT JOIN vc_trad_sk_daily vtsd on sc.company_code = vtsd.SECURITY_CD and vtsd.TRADE_DATE = date('{trade_date}')
    LEFT JOIN provinces p on p.area_no = sc.province and p.`status` = '1'
	LEFT JOIN provinces p1 on p1.area_no = sc.city and p.`status` = '1'
	LEFT JOIN provinces p2 on p2.area_no = sc.area and p.`status` = '1'
	LEFT JOIN maa_conf_label maa on sc.industry_sw_code_2021 = maa.label_value AND maa.label_code = 'INDUSTRY_SW_2021'
	LEFT JOIN maa_conf_label maa1 on maa.p_id = maa1.id
	LEFT JOIN maa_conf_label maa2 on maa.b_id = maa2.id
    LEFT JOIN rep_case rc ON sc.company_code = rc.companycode
    WHERE sc.status = 'U'
    AND sc.stock_type = '1'
    AND sc.liststate = '0'
    AND sc.ipo_flag = '1'
    AND sc.zh_sort_name NOT LIKE '%ST%'
    AND rc.status = '1'
    AND rc.release_flag = '1'
    AND rc.repurprogress = '006'
    """

    if plate:
        sql += f" AND sc.belongs_plate = '{plate}'"
    if industry3:
        sql += f" AND maa.label_value = '{industry3}'"
    if industry2:
        sql += f" AND maa.id = '{industry2}'"
    if industry1:
        sql += f" AND maa.id = '{industry1}'"
    if orgform_code:
        sql += f" AND sc.orgform = '{orgform_code}'"
    if province_code:
        sql += f" AND sc.province = '{province_code}'"
    if city_code:
        sql += f" AND sc.city = '{city_code}'"
    if area_code:
        sql += f" AND sc.city = '{area_code}'"
    if market_value:
        if market_value <= 3000000000:
            sql += " AND vtsd.ZSZ <= '3000000000'"
        elif 3000000000 < market_value <= 5000000000:
            sql += " AND vtsd.ZSZ > '3000000000' AND vtsd.ZSZ <= '5000000000'"
        elif 5000000000 < market_value <= 10000000000:
            sql += " AND vtsd.ZSZ > '5000000000' AND vtsd.ZSZ <= '10000000000'"
        elif 10000000000 < market_value <= 50000000000:
            sql += " AND vtsd.ZSZ > '10000000000' AND vtsd.ZSZ <= '50000000000'"
        elif 50000000000 < market_value <= 100000000000:
            sql += " AND vtsd.ZSZ > '50000000000' AND vtsd.ZSZ <= '100000000000'"
        elif market_value > 100000000000:
            sql += " AND vtsd.ZSZ > '100000000000'"
    if company_codes:
        company_code = ", ".join([f"'{company_code}'" for company_code in company_codes])
        sql += f" AND sc.company_code NOT IN ({company_code})"
    sql += " ORDER BY rc.repurenddate DESC LIMIT 10"
    result = execute_query("CLOUD", sql)
    return result if result else []


def get_law_name_by_id(law_id):
    sql = f"""
    SELECT
        id,
        laws_name
    FROM laws_manage
    where id = {law_id}
    """
    result = execute_query("CLOUD", sql)
    return result[0] if result else {}


def get_law_text_name_by_law_text(law_text):
    sql = f"""
    SELECT
        id,
        law_id,
        content,
        level,
        parent_id
    FROM law_xml_structure_detail
    WHERE
        id in ({", ".join([f"'{law_text_id}'" for law_text_id in law_text.split(',')])})
        OR parent_id in ({", ".join([f"'{law_text_id}'" for law_text_id in law_text.split(',')])})
    """
    result = execute_query("CLOUD", sql)
    return result if result else {}


def get_company_data(company_code, date):
    sql = f"""
    SELECT
        sc.belongs_plate as '所属板块编码',
        sc.province as '省级编码',
        sc.city as '市级编码',
        sc.area as '地区编码',
        sc.orgform as '企业性质编码',
        maa.b_id as '申万一级行业编码',
        maa.p_id as '申万二级行业编码',
        sc.industry_sw_code_2021 as '申万三级行业编码',
        sc.company_code as '证券代码',
        sc.zh_sort_name as '公司名称',
        sc.ipo_date as '上市日期',
        CASE
            WHEN sc.belongs_plate = '00' THEN '深交所主板'
            WHEN sc.belongs_plate = '01' THEN '深交所中小板'
            WHEN sc.belongs_plate = '02' THEN '深交所创业板'
            WHEN sc.belongs_plate = '04' THEN '上交所主板'
            WHEN sc.belongs_plate = '07' THEN '上交所科创板'
            WHEN sc.belongs_plate = '09' THEN '北交所'
            WHEN sc.belongs_plate = '05' THEN '新三板'
            WHEN sc.belongs_plate = '06' THEN '非以上'
            ELSE ''
        END AS '所属板块',
        concat(p.area_name, ' ', p1.area_name) as '地区',
        sce.code_name as '企业性质',
        concat(sc.register_capital, '万元') as '注册资本',
        sc.industry_sw_2021 as '申万行业',
        concat(vc.zsz, '元') as '总市值',
        concat(cf.sumasset, '元') as '总资产',
        concat(cf.sumlasset, '元') as '流动资产合计',
        concat(cf.bps, '元') as '最近一期每股净资产',
        concat(ROUND((vc.new * (
            SELECT MAX(vcd.NEWHFQJ)
            FROM vc_trad_sk_daily vcd
            WHERE vcd.SECURITY_CD = sc.company_code
              AND vcd.TRADE_DATE BETWEEN DATE_SUB(date('{date}') , INTERVAL 1 YEAR) AND date('{date}')
        ) / vc.NEWHFQJ), 4), '元') as '最近一年最高收盘价',
        concat(vc.totalshares, '股') as '总股本',
        concat(vc.ASHARER, '股') as '有限售条件流通股份',
        concat(vc.CSHARE, '股') as '无限售条件流通股份',
        concat(cf.SUMPARENTEQUITY, '元') as '归属于上市公司股东的净资产',
        concat(cf.PARENTNETPROFIT, '元') as '归属于上市公司股东的净利润'
    FROM
        sa_company sc
    LEFT JOIN sa_code sce ON sc.orgform = sce.code_value AND sce.code_no = 'ORGFORM'
    LEFT JOIN provinces p on p.area_no = sc.province and p.`status` = '1'
	LEFT JOIN provinces p1 on p1.area_no = sc.city and p.`status` = '1'
    LEFT JOIN maa_conf_label maa on sc.industry_sw_code_2021 = maa.label_value AND maa.label_code = 'INDUSTRY_SW_2021'
    LEFT JOIN vc_trad_sk_daily vc ON vc.SECURITY_CD = sc.company_code AND vc.TRADE_DATE = date('{date}')
    LEFT JOIN company_finance cf ON cf.COMPANYCODE = sc.rela_code
    AND cf.REPORTDATE = (
        SELECT MAX(cf_inner.REPORTDATE)
        FROM company_finance cf_inner
        WHERE cf_inner.COMPANYCODE = sc.rela_code
          AND cf_inner.REPORTDATE <= date('{date}')
    )
    WHERE
        sc.company_code = '{company_code}'
        AND sc.ipo_flag = '1'
        AND sc.stock_type = '1'
        AND sc.`status` = 'U'
        AND sc.liststate = '0'
        AND sc.zh_sort_name NOT LIKE '%ST%'
    """
    result = execute_query("CLOUD", sql)
    return result[0] if result else {}


def get_company_trade_data(company_code, date):
    sql = f"""
    SELECT
      round((m.NEW * n.NEWHFQJ / m.NEWHFQJ), 4) as '收盘价',
      n.CHG as '涨跌幅',
      n.TRADE_DATE as '交易日'
    FROM
      (
        SELECT
          SECURITY_CD,
          NEW,
          NEWHFQJ
        FROM
          vc_trad_sk_daily
        WHERE
          SECURITY_CD = '{company_code}'
          AND TRADE_DATE = '{date}'
      ) m
      LEFT JOIN (
        SELECT
          SECURITY_CD,
          TRADE_DATE,
          NEWHFQJ,
          CHG,
          TOTALSHARES,
          ASHARER,
          CSHARE
        FROM
          vc_trad_sk_daily
        WHERE
          SECURITY_CD = '{company_code}'
          AND TRADE_DATE <= '{date}'
        ORDER BY
          TRADE_DATE desc
        LIMIT
          30
      ) n ON m.SECURITY_CD = n.SECURITY_CD
    """
    result = execute_query("CLOUD", sql)
    return result if result else []


def get_trad_date(date, flag):
    sql = f"""
    SELECT
        trade_date
    FROM
        trade_date
    WHERE
        band_type = 'A'
        AND valid_flag = '0'
    """
    if flag == 'before':
        sql += f" AND trade_date < date('{date}')"
        sql += " ORDER BY trade_date DESC"
        sql += " LIMIT 1"
    elif flag == 'after':
        sql += f" AND trade_date > date('{date}')"
        sql += " ORDER BY trade_date ASC"
        sql += " LIMIT 1"
    result = execute_query("CLOUD", sql)
    return result[0]['trade_date'] if result else ""


def get_rep_overview_data_by_company_code(company_code):
    sql = f"""
    SELECT
        rc.id AS case_id,
        rc.noticedate AS '公告日期',
        rc.repurobjective AS '回购目的',
        (SELECT group_concat(l.label_name) FROM maa_conf_label l where l.status='1' and l.label_code = 'REP_PURPOSE' and find_in_set(l.label_value, rc.objective_purpose)) AS '回购用途',
        (SELECT group_concat(l.label_name) FROM maa_conf_label l where l.status='1' and l.label_code = 'REP_SHARE_MODE' and find_in_set(l.label_value, rc.custom_share_mode)) AS '回购股份的方式',
        0 + CAST( rc.repurpricelower AS DECIMAL ( 18, 6 ) ) AS '回购股份的价格区间不低于（元/股）',
        0 + CAST( rc.repurpricecap AS DECIMAL ( 18, 6 ) ) AS '回购股份的价格区间不超过（元/股）',
        rc.repurprice_remark AS '回购股份的价格区间、定价原则',
        (SELECT group_concat(l.label_name) FROM maa_conf_label l where l.status='1' and l.label_code = 'REP_SHARE_TYPE' and find_in_set(l.label_value, rc.custom_share_type)) AS '回购股份的种类',
        0 + CAST( rc.repurnumlower AS DECIMAL ( 18, 6 ) ) AS '回购股份的数量不低于（万股）',
        0 + CAST( rc.repurnumcap AS DECIMAL ( 18, 6 ) ) AS '回购股份的数量不超过（万股）',
        0 + CAST( rc.rep_urnum_lower_ratio AS DECIMAL ( 18, 6 ) ) AS '回购股份占总股本比例不低于（%）',
        0 + CAST( rc.rep_urnum_cap_ratio AS DECIMAL ( 18, 6 ) ) AS '回购股份占总股本比例不超过（%）',
        rc.rep_urnum_remark AS '回购股份的种类、数量、占总股本比例',
        0 + CAST( rc.repuramountlower AS DECIMAL ( 18, 6 ) ) AS '回购股份的资金总额下限（万元）',
        0 + CAST( rc.repuramountlimit AS DECIMAL ( 18, 6 ) ) AS '回购股份的资金总额上限（万元）',
        (SELECT group_concat(l.label_name) FROM maa_conf_label l where l.status='1' and l.label_code = 'REP_URAMOUNT_SOURCE' and find_in_set(l.label_value, rc.repuramount_source)) AS '回购股份资金来源',
        rc.repuramount_remark AS '回购股份的资金总额及资金来源',
        rc.repurstartdate AS '回购股份的实施期限开始日期',
        rc.repurenddate AS '回购股份的实施期限结束日期',
        rc.implementation_term AS '回购股份的实施期限',
        (SELECT l.label_name FROM maa_conf_label l where l.status='1' and l.label_code = 'REP_CONS_TYPE' and l.label_value = rc.impl_cons_type) AS '回购股份的实施期限的开始事件节点',
        rc.suspension_occurs_remark AS '回购股份的实施期限描述'
    FROM
        rep_case rc
    WHERE
        rc.companycode = '{company_code}'
    ORDER BY rc.noticedate DESC LIMIT 1
    """
    result = execute_query("CLOUD", sql)
    return result[0] if result else {}


def get_rep_process_data_by_case_id(case_id):
    sql = f"""
    SELECT
        DATE_FORMAT( t1.publish_time, '%Y-%m-%d' ) as '公告日期',
        t2.label_name as '回购进程'
    FROM
        rep_progress_info t1,
        maa_conf_label t2
    WHERE
        t1.progress_type = t2.label_value
        AND t2.label_code = 'REP_PROGRESS'
        AND t1.case_id = '{case_id}'
        AND t1.table_sort = '1'
        AND DATE_FORMAT( t1.publish_time, '%Y-%m-%d' ) < DATE_FORMAT( now(), '%Y-%m-%d' )
    GROUP BY
        t1.pro_sort
    ORDER BY
        t1.pro_sort ASC,
        t1.rela_type DESC
    """
    result = execute_query("CLOUD", sql)
    return result if result else []


def get_rep_impl_detail_data_by_case_id(case_id):
    sql = f"""
    SELECT
        t.notice_date,
        t.share_num,
        t.share_price_low,
        t.share_price_top,
        t.share_ratio,
        t.payment_accout / 10000 AS payment_accout,
    CASE
            WHEN rc.implement_detail_currency = repuramountlimit_currency
            AND rc.repuramountlimit IS NOT NULL THEN
                t.payment_accout / 10000 / rc.repuramountlimit * 100 ELSE NULL
                END AS payment_accout_ratio
        FROM
            rep_implement_detail t
            LEFT JOIN rep_case rc ON rc.id = t.rep_case_id
        WHERE
            t.rep_case_id = '{case_id}'
    ORDER BY
        t.notice_date
    """
    result = execute_query("CLOUD", sql)
    return result if result else []


def get_case_browser_params_from_mysql(case_type):
    sql = f"""
    SELECT id,
           case_type,
           case_type_name,
           indicators_name,
           mapper,
           mapper_suffix,
           description,
           indicators_name_alias,
           data_type,
           optional_value,
           hot_score,
           is_required,
           sort,
           mapper_multi,
           mapper_fields,
           parent_name,
           css_type,
           path,
           tips_mapper
    FROM agent_knowledge_indicators
    WHERE case_type = '{case_type}'
    """
    connection = get_db_connection('CLOUD')
    cursor = connection.cursor(dictionary=True)
    cursor.execute(sql)
    case_browser_params = cursor.fetchall()
    cursor.close()
    connection.close()
    return case_browser_params


def query_ingredients_by_keywords(type_name: str, case_type: str, level: str = None) -> List[Dict]:
    """
    根据关键词直接查询成分
    :param type_name: 成分名称
    :param case_type: 案例类型
    :param level: 成分级别，可选参数
    :return: 成分列表
    """

    try:

        sql = f"""
            SELECT type_name, ingredients_name, label_path, code
            FROM agent_knowledge_ingredients
            WHERE case_type = '{case_type}'
            AND type_name = '{type_name}'
        """

        # 根据level参数添加不同的过滤条件
        if level is not None:
            sql += f" AND level = '{level}'"
        else:
            sql += " AND level != '0'"

        # 使用您的数据库连接方法
        connection = get_db_connection('CLOUD')
        cursor = connection.cursor(dictionary=True)

        # 执行查询
        cursor.execute(sql)
        result = cursor.fetchall()

        # 关闭游标和连接
        cursor.close()
        connection.close()

        return result

    except Exception as e:
        print(f"查询违规类型数据失败: {str(e)}")
        return []


def get_association_metrics_from_mysql(case_type, metrics):
    placeholders = ', '.join(['%s'] * len(metrics))

    conditions = []
    params = [case_type]

    for metric in metrics:
        conditions.append("related_params LIKE %s")
        params.append(f"%{metric}%")

    sql = f"""
        SELECT id, case_type, case_type_name, related_params
        FROM agent_knowledge_indicators_map
        WHERE case_type = %s
        AND ({' OR '.join(conditions)})
        """
    print(sql)
    connection = get_db_connection('CLOUD')
    cursor = connection.cursor(dictionary=True)

    cursor.execute(sql, params)
    association_metrics = cursor.fetchall()
    cursor.close()
    connection.close()
    return association_metrics


def get_compiled_mappers_from_mysql(case_type):
    sql = f"""
    SELECT indicators_name,
           mapper
    FROM agent_knowledge_indicators
    where case_type = '{case_type}'
    """
    connection = get_db_connection('CLOUD')
    cursor = connection.cursor(dictionary=True)
    cursor.execute(sql)
    case_browser_params = cursor.fetchall()
    cursor.close()
    connection.close()
    compiled_mappers = {item['mapper']: item['indicators_name'] for item in case_browser_params}
    return compiled_mappers
