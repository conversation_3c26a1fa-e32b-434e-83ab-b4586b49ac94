# es_tool.py
import ast
import os

from elasticsearch import Elasticsearch
from jinja2 import Template


def get_es_client():
    """
    获取 Elasticsearch 客户端实例
    :return: Elasticsearch 客户端实例
    """
    # 从环境变量中读取 Elasticsearch 配置
    es_hosts = ast.literal_eval(os.getenv("ES_LINK"))
    es_username = os.getenv("ES_USERNAME", None)
    es_password = os.getenv("ES_PASSWORD", None)

    if es_username and es_password:
        # 使用基本认证
        client = Elasticsearch(
            hosts=es_hosts,
            http_auth=(es_username, es_password)
        )
    else:
        # 不使用认证
        client = Elasticsearch(hosts=es_hosts)

    return client


def build_listed_announcement_query(condition, start_row, page_size):
    """
    构建查询语句
    :param condition: 查询条件
    :param start_row: 起始行
    :param page_size: 每页大小
    :return: 查询语句
    """
    template = Template('''
    {
        "track_total_hits": true,
        "from": {{ start_row }},
        "size": {{ page_size }},
        "_source": [
            "id",
            "declare_title_t",
            "declare_code_t",
            "declare_company_short_name_t",
            "declare_content_t",
            "paragraphs",
            "index_create_time_dt",
            "declare_infoUrl_t",
            "declare_publish_date_dt"
        ],
        "query": {
            "bool": {
                "must": [{
                    "bool": {
                        "must": [
                            { "match_all": {} }
                            {# 主键 #}
                            {% if condition.idList %}
                            ,{
                                "bool": {
                                    "should": [
                                        {% for item in condition.idList %}
                                        { "term": { "id": { "value": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 标题关键字 全部 #}
                            {% if condition.keyAndTitleList %}
                            ,{
                                "bool": {
                                    "must": [
                                        {% for item in condition.keyAndTitleList %}
                                        { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 标题关键字 任意 #}
                            {% if condition.keyTitleList %}
                            ,{
                                "bool": {
                                    "should": [
                                        {% for item in condition.keyTitleList %}
                                        { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 标题关键字 不包含 #}
                            {% if condition.keyNotTitleList %}
                            ,{
                                "bool": {
                                    "must_not": [{
                                        "bool": {
                                            "should": [
                                                {% for item in condition.keyNotTitleList %}
                                                { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                                {% endfor %}
                                            ]
                                        }
                                    }]
                                }
                            }
                            {% endif %}
                            {# 全文关键字 包含 #}
                            {% if condition.keyAndContentList %}
                            ,{
                                "bool": {
                                    "must": [{
                                        "bool": {
                                            "should": [
                                                {
                                                    "bool": {
                                                        "must": [
                                                            {% for item in condition.keyAndContentList %}
                                                            { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                                            {% endfor %}
                                                        ]
                                                    }
                                                }
                                                ,{
                                                    "bool": {
                                                        "must": [
                                                            {% for item in condition.keyAndContentList %}
                                                            { "match_phrase": { "declare_content_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                                            {% endfor %}
                                                        ]
                                                    }
                                                }
                                            ]
                                        }
                                    }]
                                }
                            }
                            {% endif %}
                            {# 全文关键字 任意 #}
                            {% if condition.keyContentList %}
                            ,{
                                "bool": {
                                    "should": [
                                        {% for item in condition.keyContentList %}
                                        { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } },
                                        { "match_phrase": { "declare_content_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 全文关键字 不包含 #}
                            {% if condition.keyNotContentList %}
                            ,{
                                "bool": {
                                    "must_not": [{
                                        "bool": {
                                            "should": [
                                                {% for item in condition.keyNotContentList %}
                                                { "match_phrase": { "declare_title_t": { "query": "{{ item }}" } } },
                                                { "match_phrase": { "declare_content_t": { "query": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                                {% endfor %}
                                            ]
                                        }
                                    }]
                                }
                            }
                            {% endif %}
                        ]
                    }
                }],
                "filter": [{
                    "bool": {
                        "must_not": [
                            {# 公司代码 #}
                            {% if condition.companyCodeNotLst %}
                            {
                                "terms": {
                                    "declare_code_t": [
                                        {% for item in condition.companyCodeNotLst %}
                                        "{{ item }}"{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                        ],
                        "must": [
                            { "match_all": {} }
                            {# 公司代码 #}
                            {% if condition.companyCodeLst %}
                            ,{
                                "terms": {
                                    "declare_code_txt": [
                                        {% for item in condition.companyCodeLst %}
                                        "{{ item }}"{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 公告id #}
                            {% if condition.announcmentIdList %}
                            ,{
                                "terms": {
                                    "id": [
                                        {% for item in condition.announcmentIdList %}
                                        "{{ item }}"{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ],
                                    "boost": 1
                                }
                            }
                            {% endif %}
                            {# 默认板块 #}
                            {% if condition.parentPlate %}
                            ,{
                                "terms": {
                                    "declare_parent_plate_txt": [
                                        "{{ condition.parentPlate }}"
                                    ]
                                }
                            }
                            {% endif %}
                            {# 公告新分类 for 市值分析 #}
                            {% if condition.declareNewTypeList %}
                            ,{
                                "bool": {
                                    "should": [
                                        {% for item in condition.declareNewTypeList %}
                                        { "prefix": { "declare_new_types_txt": { "value": "{{ item }}" } } }{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 左侧新分类树 #}
                            {% if condition.declareNewType %}
                            ,{
                                "bool": {
                                    "should": [
                                        { "terms": { "declare_new_types_txt": [ "{{ condition.declareNewType }}" ] } }
                                    ]
                                }
                            }
                            {% endif %}
                            {# ipo 多个文件类型List #}
                            {% if condition.ipoFileTypeSearchList %}
                            ,{
                                "terms": {
                                    "declare_ipo_file_type_txt": [
                                        {% for item in condition.ipoFileTypeSearchList %}
                                        "{{ item }}"{% if not loop.last %},{% endif %}
                                        {% endfor %}
                                    ]
                                }
                            }
                            {% endif %}
                            {# 创建时间 #}
                            {% if condition.indexCreateTimeFrom %}
                            ,{
                                "constant_score": {
                                    "filter": {
                                        "range": {
                                            "index_create_time_dt": {
                                                "from": "{{ condition.indexCreateTimeFrom }}",
                                                "to": "{{ condition.indexCreateTimeFrom }}",
                                                "include_lower": true,
                                                "include_upper": true
                                            }
                                        }
                                    }
                                }
                            }
                            {% endif %}
                            {# 创建时间 #}
                            {% if condition.publishDateFrom and condition.publishDateTo %}
                            ,{
                                "constant_score": {
                                    "filter": {
                                        "range": {
                                            "index_create_time_dt": {
                                                "from": "{{ condition.publishDateFrom }}",
                                                "to": "{{ condition.publishDateTo }}",
                                                "include_lower": true,
                                                "include_upper": true                                        
                                            }
                                        }
                                    }
                                }
                            {% endif %}
                        ]
                    }
                }]
            }
        },
        "aggs": {}
    }
    ''')
    return template.render(condition=condition, start_row=start_row, page_size=page_size)


# 示例用法
if __name__ == "__main__":
    # 获取 Elasticsearch 客户端
    # es_client = get_es_client()

    # 构建查询条件
    condition = {
        # "idList": ["1", "2"],
        # "keyAndTitleList": ["keyword1", "keyword2"],
        "keyTitleList": ["回购报告书"],
        # "keyNotTitleList": ["keyword4"],
        # "keyAndContentList": ["keyword5"],
        # "keyContentList": ["keyword6"],
        # "keyNotContentList": ["keyword7"],
        # "companyCodeNotLst": ["code1", "code2"],
        "companyCodeLst": ["000001"],
        # "announcmentIdList": ["3", "4"],
        # "parentPlate": "parent1",
        # "declareNewTypeList": ["24181902543262320"],
        # "declareNewType": "type2",
        # "ipoFileTypeSearchList": ["filetype1"],
        # "indexCreateTimeFrom": "2021-01-01",
        # "publishDateFrom": "2021-01-01",
        # "publishDateTo": "2021-12-31"
    }

    # 构建查询语句
    start_row = 0
    page_size = 10
    query = build_listed_announcement_query(condition, start_row, page_size)
    print(query)

    # 执行搜索查询并打印结果
    # index_name = "your_index_name"
    # result = es_client.search(index=index_name, body=query)
    # print(result)
