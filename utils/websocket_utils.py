import asyncio
import logging
import traceback
from typing import List

import websockets.exceptions
from fastapi import WebSocket

logger = logging.getLogger(__name__)


class ConnectionManager:
    def __init__(self):
        # 存放激活的 WebSocket 连接对象
        self.active_connections: List[WebSocket] = []
        # 使用异步锁保护连接列表，防止并发修改
        self.lock = asyncio.Lock()

    async def connect(self, ws: WebSocket):
        # 等待客户端连接
        await ws.accept()
        # 获取锁，确保线程安全
        async with self.lock:
            # 存储 WebSocket 连接对象
            self.active_connections.append(ws)

    async def disconnect(self, ws: WebSocket, code=1000, reason="主动断开连接"):
        """
        断开 WebSocket 连接，并通知客户端

        Args:
            ws: WebSocket 连接对象
            code: 关闭连接的状态码，默认为 1000 (正常关闭)
            reason: 关闭连接的原因，默认为 "主动断开连接"
        """
        async with self.lock:
            if ws in self.active_connections:
                try:
                    # 发送关闭连接通知给客户端
                    await ws.close(code=code, reason=reason)
                    logger.info(f"WebSocket 连接已关闭，状态码：{code}，原因：{reason}")
                except websockets.exceptions.ConnectionClosedOK:
                    logger.warning("WebSocket 连接已关闭。")
                except Exception as e:
                    logger.error(f"关闭 WebSocket 连接时发生错误: {e}")
                    logger.error(traceback.format_exc())
                finally:
                    # 从连接列表中移除 WebSocket 连接对象
                    self.active_connections.remove(ws)

    async def send_personal_message(self, message: str, ws: WebSocket):
        """
        向指定的 WebSocket 连接发送消息

        Args:
            message: 要发送的消息内容
            ws: WebSocket 连接对象
        """
        try:
            # 尝试发送个人消息
            await ws.send_text(message)
        except websockets.exceptions.ConnectionClosedOK:
            logger.warning("WebSocket 连接已关闭。尝试移除连接。")
            await self.disconnect(ws, code=1001, reason="连接已关闭")  # 使用1001状态码表示“离开”
        except websockets.exceptions.ConnectionClosedError as e:
            logger.warning(f"WebSocket 连接异常关闭: {e}。尝试移除连接。")
            await self.disconnect(ws, code=1006, reason="连接异常关闭")  # 使用1006状态码表示异常关闭
        except Exception as e:
            # 捕获其他可能的异常
            logger.error(f"发送消息时发生错误: {e}")
            logger.error(traceback.format_exc())
            await self.disconnect(ws, code=1011, reason="发送消息错误")  # 使用1011状态码表示内部错误

    async def broadcast(self, message: str):
        """
        向所有活动的 WebSocket 连接广播消息

        Args:
            message: 要广播的消息内容
        """
        # 创建一个连接列表的副本，避免迭代过程中修改原始列表
        connections_to_send = list(self.active_connections)
        for connection in connections_to_send:
            await self.send_personal_message(message, connection)
            # try:
            #     await connection.send_text(message)
            # except websockets.exceptions.ConnectionClosed:
            #     # 如果连接已关闭，则断开连接并移除
            #     await self.disconnect(connection, code=1001, reason="连接已关闭")
            # except Exception as e:
            #     logger.error(f"广播消息时发生错误: {e}")
            #     logger.error(traceback.format_exc())
            #     await self.disconnect(connection, code=1011, reason="发送消息错误")
