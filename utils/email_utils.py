import smtplib
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from utils.load_env import logger

SEND_MAIL_CONFIG = {
    "address": "<EMAIL>",
    "password": "zJwxgCgsFAF3Pmeq"
}


def send_email(to_emails: list[str],
               subject: str = "测试邮件",
               body: str = "这是一封测试邮件",
               att_list: list[dict[str, str]] = None):
    """
    发送带附件的邮件
    :param to_emails: 收件人邮箱
    :param subject: 邮件主题
    :param body: 邮件内容
    :param att_list: 附件文件列表
    :return: None
    """
    if not to_emails:
        raise ValueError("收件人邮箱为空")

    # 设置SMTP服务器
    server = smtplib.SMTP_SSL('smtp.valueonline.cn', 465)  # 替换为您的SMTP服务器和端口
    try:
        server.login(SEND_MAIL_CONFIG['address'], SEND_MAIL_CONFIG['password'])  # 登录
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = SEND_MAIL_CONFIG['address']
        msg['To'] = ','.join(to_emails)
        msg['Subject'] = subject

        # 添加邮件正文
        msg.attach(MIMEText(body, 'html', 'utf-8'))

        # 添加附件
        if att_list:
            for att_dict in att_list:
                file_path = att_dict.get('file_path')
                file_name = att_dict.get('file_name')
                if file_path:
                    # 打开附件文件
                    with open(file_path, 'rb') as attachment:
                        # 创建MIMEBase对象
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())
                        # 编码附件
                        encoders.encode_base64(part)
                        if not file_name:
                            file_name = "test.txt"
                        # 添加头部信息
                        part.add_header('Content-Disposition',
                                        'attachment',
                                        filename=("utf-8", "", file_name))
                        # 将附件附加到邮件消息中
                        msg.attach(part)
        # 发送邮件
        server.send_message(msg)
        logger.info(f"Email sent successfully, content: {msg.as_string()}")
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")
    finally:
        server.quit()  # 关闭连接
