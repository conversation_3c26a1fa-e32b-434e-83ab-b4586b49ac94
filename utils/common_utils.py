import base64
import json
from typing import List, Union, Tuple, Callable

from utils.load_env import logger

logger.info("Loading File")
from utils.db_tool import execute_query
from utils.redis_tool import get_redis_connect
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# 微服解密密钥
ENCRYPT_KEY = "hNkYmsBUvrTd3C3o"
# 微服解密偏移量
IV = "mLZT7OIx1qOHZaPX"


def not_null(value):
    """
        判断入参是否为空
        :param value 传入的值
        :return: 结果
    """
    if isinstance(value, list):
        return len(value) > 0
    return value is not None and value != ''


def get_code_map(r_key: str, code_no_list: List[str], db_name: str = "CLOUD"):
    """
        获取代码字典
        :param r_key redis缓存键
        :param code_no_list 查询的代码分类的列表
        :param db_name 查询的数据库，默认为云端库
        :return: 结果
    """
    if not not_null(r_key):
        raise 'r_key is null'
    r = get_redis_connect()

    if r.exists(r_key):
        r_code_list = r.get(r_key)
        results = json.loads(r_code_list)
    else:
        logger.info(f'get_code_map r_key: {r_key}')
        sql = f"""
            SELECT label_code  AS code_no,
                   label_value AS code_value,
                   label_name  AS code_name
            FROM maa_conf_label
            WHERE label_code IN {tuple(code_no_list)}
            UNION ALL
            SELECT code_no, code_value, code_name
            FROM sa_code
            WHERE code_no = 'STOCK_BOARD'
            """
        results = execute_query(db_name, sql)
        r.set(r_key, json.dumps(results, ensure_ascii=False), ex=60 * 30)
    r.close()
    return results


def name_to_code(code_names: Union[List, str], code_no: str, r_key: str, code_no_list: List[str],
                 db_name: str = "CLOUD") -> Tuple[List, str]:
    """
        根据代码名称获取代码取值
        :param code_names: 代码名称列表
        :param code_no: 代码分类
        :param r_key redis缓存键
        :param code_no_list 查询的代码分类的列表
        :param db_name 查询的数据库，默认为云端库
        :return: 结果
    """
    all_code_list = get_code_map(r_key, code_no_list, db_name)
    if isinstance(code_names, str):
        code_names = code_names.split(',')
    filtered_list = {item['code_name']: item['code_value'] for item in all_code_list if item.get('code_no') == code_no}
    found = {k: v for k, v in filtered_list.items() if k in code_names}
    not_found = [i for i in code_names if i not in found]
    note = f"{','.join(not_found)} 没有找到正确的码值，参考取值范围:{','.join(filtered_list.keys())}" if not_found else ''
    return list(found.values()), note


def read_file_text(file_path: str) -> str:
    """
        读取文件文本
        :param file_path: 文件路径
        :return: 文本内容
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        return content


def handle_tool_error(
        e: Exception,
        *,
        flag: Union[
            bool,
            str,
            Callable[..., str],
            tuple[type[Exception], ...],
        ],
) -> str:
    if isinstance(flag, (bool, tuple)):
        content = "Error: {error}\n Please fix your mistakes.".format(error=repr(e))
    elif isinstance(flag, str):
        content = flag
    elif callable(flag):
        content = flag(e)
    else:
        raise ValueError(
            f"Got unexpected type of `handle_tool_error`. Expected bool, str "
            f"or callable. Received: {flag}"
        )
    return content


def decrypt_data(encrypt_data: str) -> str:
    """
        解密数据
        :param encrypt_data: 加密数据
        :return: 解密数据
    """
    # 将密钥和偏移量进行 Base64 编码
    iv = base64.b64encode(IV.encode('utf-8')).decode('utf-8')
    encrypt_key = base64.b64encode(ENCRYPT_KEY.encode('utf-8')).decode('utf-8')
    key = ENCRYPT_KEY + IV
    rtn = encrypt_data.replace(key, "")
    try:
        key = base64.b64decode(encrypt_key)
        iv_bytes = base64.b64decode(iv)
        cipher = AES.new(key, AES.MODE_CBC, iv_bytes)
        encrypted = base64.b64decode(rtn)
        decrypted = cipher.decrypt(encrypted)
        original = unpad(decrypted, AES.block_size)
        return original.decode('utf-8')
    except Exception as e:
        print(f"AES decrypt error, caused by: {e}")
        return ''
