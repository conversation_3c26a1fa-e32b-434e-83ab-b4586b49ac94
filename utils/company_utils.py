import concurrent
import json
from concurrent.futures import ThreadPoolExecutor

from utils.load_env import logger

logger.info("Loading File")
from utils import db_tool


def confirm_company(code_or_name: str, _cursor=None):
    """
        符合条件的公司
        :param code_or_name 公司代码或名称
        :param _cursor 数据库实例
        :return: 公司列表
    """
    connection = None
    if not _cursor:
        connection = db_tool.get_db_connection('CLOUD')
        cursor = connection.cursor(dictionary=True)
    else:
        cursor = _cursor
    sql = f"""SELECT
                t.company_code,
                t.zh_name,
                t.zh_sort_name,
                t.license_no,
                t.belongs_plate,
                cst.code_name belongs_plate_name
            FROM sa_company t 
	        LEFT JOIN sa_code cst ON cst.code_no = 'STOCK_BOARD' AND cst.code_value = t.belongs_plate AND cst.valid_flag = '1'
              WHERE t.company_code LIKE '%{code_or_name}%'
                 OR t.zh_sort_name LIKE '%{code_or_name}%'
                 OR t.zh_name LIKE '%{code_or_name}%'
              ORDER BY IF(t.ipo_flag = '1', 1, 0) DESC, IF(t.stock_type = '1', 1, 0) DESC
            """
    cursor.execute(sql)
    result = cursor.fetchall()
    if not _cursor and connection:
        cursor.close()
        connection.close()
    return result


company_data = json.load(open('data/company_codes.json', 'r', encoding='utf-8'))
company_data_split = [dict(list(company_data.items())[i:i + len(company_data) // 16]) for i in
                      range(0, len(company_data), len(company_data) // 16)]


def match_company(query, company_data_):
    return [company_code for company_code, company_name in company_data_.items() if company_name in query]


def parallel_get_company_code(question):
    # 封装并行处理逻辑的方法

    with ThreadPoolExecutor(max_workers=16) as executor:
        futures = []
        # 分割数据，创建任务

        # 提交每个任务
        for data_split in company_data_split:
            futures.append(executor.submit(match_company, question, data_split))

        # 获取结果
        all_results = []
        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            all_results.extend(result)

        return all_results


if __name__ == '__main__':
    # get_all_company()
    # 调用封装的方法
    matched_company = parallel_get_company_code("平安银行回购进展")
    print(matched_company)
