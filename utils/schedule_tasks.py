# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：schedule_tasks.py 
@Date    ：2025/1/14 上午11:23 
<AUTHOR>
@Desc    ：
"""
import json
import re
import time

from utils.load_env import logger

import requests
import schedule
from bs4 import BeautifulSoup

from utils.common_utils import decrypt_data
from utils.db_tool import execute_query

from utils.redis_tool import remove_redis_info
from sub_agents.rep.utils.web_utils import get_access_token


# 定义定时任务
def get_all_company():
    """
    从数据库里获取所有的公司名称和简称
    定时更新
    """

    def clean_company_name(text):
        # 去除中文和英文字符之外的其他字符
        pattern = re.compile(r'[^a-zA-Z\u4e00-\u9fa5]', re.UNICODE)
        return re.sub(pattern, '', text)

    sql = """select company_code, zh_sort_name from sa_company"""
    result = execute_query("CLOUD", sql)
    result = {line['company_code']: clean_company_name(line['zh_sort_name']) for line in result}
    if 'base001' in result:
        result.pop('base001')  # 噪声数据删除
    json.dump(result,
              open('data/company_codes.json', 'w+', encoding='utf-8'),
              indent=2,
              ensure_ascii=False)


def save_process_guide():
    """
    从接口获取回购流程指引
    每6小时更新1次
    """

    def http_call_info(item_id: str) -> list:
        # 获取事项流程列表
        token_redis_key_name = 'REP_AGENT_ACCESS_TOKEN'
        # 调用接口
        access_token = get_access_token(token_redis_key_name)
        data = {
            "itemId": item_id
        }
        response = requests.post(
            f"https://services.easy-board.com.cn/declare/thinkTank/getInfoList?access_token={access_token}",
            json=data)
        if response.status_code == 200:
            try:
                res_results_str = decrypt_data(response.json())
                res_results = json.loads(res_results_str)
                return res_results.get('result').get('itemTaskList')
            except Exception as e:
                logger.error(f"save_process_guide解析报错：{str(e)}")

        else:
            # access_token = get_access_token()将此键放到了redis
            remove_redis_info(token_redis_key_name)
            time.sleep(1)
            return http_call_info(item_id)

    def http_call_task_info(task_id: str) -> dict:
        # 获取任务详情信息
        token_redis_key_name = 'REP_AGENT_ACCESS_TOKEN'
        # 调用接口
        access_token = get_access_token(token_redis_key_name)
        data = {
            "taskId": task_id
        }
        response = requests.post(
            f"https://services.easy-board.com.cn/declare/thinkTank/getTaskDetail?access_token={access_token}",
            json=data)
        if response.status_code == 200:
            try:
                res_results_str = decrypt_data(response.json())
                res_results = json.loads(res_results_str)
                return res_results.get('result').get('taskDetail')
            except Exception as e:
                logger.error(f"save_process_guide解析报错：{str(e)}")

        else:
            # access_token = get_access_token()将此键放到了redis
            remove_redis_info(token_redis_key_name)
            time.sleep(1)
            return http_call_task_info(task_id)

    def trans_item_list(item_task_list: list) -> list:
        # 转换事项列表
        final_result = []
        # 遍历任务列表
        if item_task_list:
            for item_task in item_task_list:
                # 将对应键值替换为中文
                trans_task = {
                    '步骤序号': f'第{item_task.get("sort") + 1}步',
                    '任务名称': item_task.get('itemTaskName'),
                    '时间要求': item_task.get('dateRequest'),
                }
                # 获取任务详情内容
                task_info = http_call_task_info(item_task.get('id'))
                if task_info:
                    # 处理【法律法规】
                    if task_info.get('lawRuleList'):
                        law_rule_list = []
                        for law_rule_item in task_info.get('lawRuleList'):
                            if law_rule_item.get('flag') == '0':
                                law_rule_dict = {
                                    '法规名称': law_rule_item.get('name'),
                                    '发布时间': law_rule_item.get('published'),
                                    # '发布部门': law_rule_item.get('sourceDepartment'),
                                    # '法规链接': f'https://www.valueonline.cn/laws/lawView/{law_rule_item.get("id")}/20/20/ed.html',
                                }
                                law_rule_list.append(law_rule_dict)
                        if law_rule_list:
                            trans_task['相关法规依据'] = law_rule_list
                    # 处理【文件模板】
                    # if task_info.get('taskFileList'):
                    #     task_file_list = []
                    #     for task_file_item in task_info.get('taskFileList'):
                    #         if task_file_item.get('attId'):
                    #             task_file_dict = {
                    #                 '文件名称': task_file_item.get('fileName'),
                    #                 '文件链接': f"https://services.easy-board.com.cn/declare/filedownload?fileId={task_file_item.get('attId')}",
                    #             }
                    #             task_file_list.append(task_file_dict)
                    #     if task_file_list:
                    #         trans_task['相关文件模板'] = task_file_list
                    # 处理【注意事项】
                    if task_info.get('noticeItem'):
                        soup = BeautifulSoup(task_info.get('noticeItem'), 'lxml')
                        # 提取所有文字内容
                        if soup.get_text():
                            trans_task['任务注意事项'] = soup.get_text().replace('\u00A0', ' ')
                final_result.append(trans_task)
        return final_result

    results = {}
    pg_sector_range_map = {
        "创业板": "745777672747927850",
        "科创板": "746761292302161905",
        "深主板": "745777672747927802",
        "沪主板": "745777672747927862",
        # 竞价回购 459097900968660231 要约回购 459097900976212871 定价回购 459097900977049186
        "北交所": "459097900968660231,459097900976212871,459097900977049186",
    }
    for sector_name, sector_ids in pg_sector_range_map.items():
        start_time = time.time()
        logger.info(f"处理{sector_name}开始...")
        results[sector_name] = {}
        if "," in sector_ids:
            sector_id_arr = sector_ids.split(",")
            for sector_id in sector_id_arr:
                sector_key = ''
                if sector_id == '459097900968660231':
                    sector_key = '竞价回购'
                elif sector_id == '459097900976212871':
                    sector_key = '要约回购'
                elif sector_id == '459097900977049186':
                    sector_key = '定价回购'
                if sector_key:
                    # 遍历任务列表
                    results[sector_name][sector_key] = trans_item_list(http_call_info(sector_id))
        else:
            results[sector_name] = trans_item_list(http_call_info(sector_ids))
        end_time = time.time()
        logger.info(f"处理{sector_name}结束，用时{(end_time - start_time) * 1000}毫秒")
    json.dump(results,
              open('data/process_guide.json', 'w+', encoding='utf-8'),
              indent=2,
              ensure_ascii=False)


# 创建定时任务调度函数
def run_schedule():
    # 定义任务
    schedule.every(1).days.do(get_all_company)  # 每天执行一次更新公司列表
    schedule.every(6).hours.do(save_process_guide)  # 每6小时从接口获取回购流程指引

    # 运行调度任务
    while True:
        schedule.run_pending()  # 执行所有待执行的任务
        time.sleep(300)  # 每5分钟检查一次任务
