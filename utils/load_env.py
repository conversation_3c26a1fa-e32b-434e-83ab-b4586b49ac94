# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：load_env.py 
@Date    ：2024/12/16 下午3:48 
@Desc    ：
"""
import json
import os
import socket

import consul
# 移除 netifaces 导入

from dotenv import load_dotenv

load_dotenv(
    dotenv_path={'dev': 'env/.env.dev', 'test': 'env/.env.test', 'prod': 'env/.env.prod',
                 'ztcloud': 'env/.env.ztcloud'}.get(os.getenv('env', 'dev')),
    override=True)

from log.logstash_entity import Logger


def get_local_ip():
    """获取本机IP地址，优先使用环境变量中的IP"""
    # 首先检查环境变量中是否有指定的IP
    host_ip = os.getenv('HOST_IP')
    if host_ip:
        return host_ip

    # 如果环境变量中没有指定IP，则尝试获取容器IP
    try:
        # 尝试解析host.docker.internal
        host_ip = socket.gethostbyname('host.docker.internal')
        return host_ip
    except socket.gaierror:
        # 如果解析失败，回退到常规方法
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            s.connect(('*******', 1))
            local_ip = s.getsockname()[0]
        except Exception:
            local_ip = '127.0.0.1'
        finally:
            s.close()
        return local_ip


def register_service_to_consul(profile):
    """
    向Consul注册服务
    """
    # 使用固定IP
    local_ip = get_local_ip()
    # 使用环境变量中的端口，如果没有则默认为9898
    service_port = int(os.getenv('SERVICE_PORT', '9898'))

    if profile == 'dev':
        consul_host = '************'
        consul_port = 8500
        token = '8970be09-01e7-ba07-349e-f897adebf589'
    elif profile == 'test':
        consul_host = '************'
        consul_port = 8500
        token = '850cbbf8-bf6a-57a8-6ca5-8b3614a70553'
    else:
        #  生产环境配置
        consul_host = 'nodes.public.consul.ztcloud'
        consul_port = 8500
        token = '69f35108-bc66-ef9b-a790-3f345c8bc1cc'

    # 初始化 Consul 客户端
    client = consul.Consul(host=consul_host, port=consul_port, token=token)

    # 注册服务
    client.agent.service.register(
        name='agent-tools',  # 服务名称
        service_id=f'agent-tools-{local_ip.replace(".", "-")}-{service_port}',  # 服务唯一 ID
        address=local_ip,  # 服务地址（固定IP）
        port=service_port,  # 服务端口
    )

    return local_ip


config_params = register_service_to_consul(os.getenv('env', 'dev'))

logger = Logger("agents_platform")
