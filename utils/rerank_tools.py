import os
from typing import List, Dict, Optional

import aiohttp


class ReRankService:
    def __init__(self, rerank_url: str):
        self.rerank_url = rerank_url

    async def rerank(self, query: str, documents: List[str]) -> Optional[List[Dict]]:
        """
        异步调用 rerank 服务对文档列表进行重新排序。

        Args:
            query: 查询语句。
            documents: 待排序的文档列表。

        Returns:
            如果请求成功，返回排序后的文档列表，每个文档是一个字典，包含文档内容和相关性得分。
            如果请求失败，返回 None。
            例如：
            [
                {'index': 0, 'relevance_score': 0.95, 'document': '...'},
                {'index': 1, 'relevance_score': 0.85, 'document': '...'},
                ...
            ]
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=self.rerank_url,
                    json={
                        "query": query,
                        "model": "bge-reranker-v2-minicpm-layerwise",
                        "documents": documents,
                    },
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get("results")
                    else:
                        print(f"Reranking request failed with status code: {response.status}")
                        return None
        except Exception as e:
            print(f"Reranking request failed: {e}")
            return None


rerank_service_host = os.getenv('RERANK_SERVICE_HOST')


# 使用示例
async def main():
    reranker = ReRankService(rerank_service_host)

    query = "上市公司股份回购的目的"
    documents = [
        "上市公司回购股份是指公司收购本公司的股份。",
        "本规则所称上市公司回购股份，是指上市公司因下列情形之一收购本公司股份的行为：（一）减少公司注册资本；（二）与持有本公司股份的其他公司合并；（三）将股份用于员工持股计划或者股权激励；（四）股东因对股东大会作出的公司合并、分立决议持异议，要求公司收购其股份；（五）将股份用于转换上市公司发行的可转换为股票的公司债券；（六）上市公司为维护公司价值及股东权益所必需。其中，因前款第（三）项、第（五）项、第（六）项情形回购股份的，应当通过公开的集中交易方式进行。",
        "回购是指公司买回自己发行的股票。",
    ]

    ranked_documents = await reranker.rerank(query, documents)

    if ranked_documents:
        for doc in ranked_documents:
            print(
                f"Index: {doc['index']}, Score: {doc['relevance_score']}, Document: {doc['document']}"
            )


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())