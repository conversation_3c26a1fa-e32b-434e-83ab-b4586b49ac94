# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：llm_utils.py 
@Date    ：2024/12/18 上午9:16 
@Desc    ：
"""
import os
from typing import Literal

from langchain_openai import ChatOpenAI

LLM_HUB = os.getenv('MODEL_HUB_HOST')


def get_a_llm(
        llm_type: Literal[
            '4o', 'gpt-4o', 'gpt-4o-mini', 'jzzx-qwen2.5-32b',
            'deepseek-chat', 'deepseek-reasoner', 'hs-deepseek-v3', 'hs-deepseek-v3-0324', 'hs-deepseek-r1'],
        temperature: float = 0, parallel_tool_calls=False):
    """
    @param llm_type: llm类型，目前支持4o,gpt-4o,gpt-4o-mini,jzzx-qwen2.5-32b,deepseek-chat,deepseek-reasoner
    @param temperature: 温度默认0，严谨，每次输出一致
    @param parallel_tool_calls: function call场景下，是否支持并行工具调用，默认False
                               非 function call agent|chain 不支持 parallel_tool_calls参数，传 None 进来即可
    """
    if llm_type in ['4o', 'gpt-4o', 'gpt-4o-mini']:
        if parallel_tool_calls is None:
            llm = ChatOpenAI(
                model=llm_type,
                temperature=temperature,
                max_tokens=None,
                timeout=300,
                api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
                base_url=LLM_HUB,
            )
        else:
            llm = ChatOpenAI(
                model=llm_type,
                temperature=temperature,
                max_tokens=None,
                timeout=300,
                api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
                base_url=LLM_HUB,
                model_kwargs={"parallel_tool_calls": parallel_tool_calls}
            )
    elif llm_type in ['jzzx-qwen2.5-32b', 'deepseek-chat', 'deepseek-reasoner', 'hs-deepseek-v3', 'hs-deepseek-v3-0324', 'hs-deepseek-r1']:
        llm = ChatOpenAI(
            model_name=llm_type,
            temperature=temperature,
            request_timeout=300,
            model_kwargs={"parallel_tool_calls": parallel_tool_calls},
            openai_api_base=LLM_HUB,
            openai_api_key='sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320')
    else:
        raise Exception(
            f"Unknown llm type: {llm_type}, the llm_type should be in ['gpt-4o','gpt-4o-mini','qwen2']")
    return llm
