import json
import os
import re

import requests

milvus_host = os.getenv('MILVUS_HOST')

class MilvusClient:
    def __init__(self, base_url):
        """
        初始化 Milvus 客户端
        :param base_url: 基础 URL 地址
        """
        self.base_url = base_url

    def insert_vector(self, database_name, collection_name, fields, partition_name="", embed_model="BCEMBEDDING",
                      callback=None):
        """
        插入向量
        :param database_name: 数据库名称
        :param collection_name: 集合名称
        :param fields: 字段信息，格式为 [{ "name": 字段名称, "value": 字段值 }]
        :param partition_name: 分区名称（可选）
        :param embed_model: 向量化模型，默认 "BCEMBEDDING"
        :param callback: 回调信息（可选）
        :return: JSON 响应
        """
        url = f"{self.base_url}/vector/insert"
        payload = {
            "databaseName": database_name,
            "collectionName": collection_name,
            "fields": fields,
            "partitionName": partition_name,
            "embedModel": embed_model,
            "callback": callback
        }
        response = requests.put(url, json=payload)
        return response.json()

    def update_vector(self, database_name, collection_name, fields, partition_name="", embed_model="BCEMBEDDING",
                      callback=None):
        """
        更新向量
        :param database_name: 数据库名称
        :param collection_name: 集合名称
        :param fields: 字段信息，格式为 [{ "name": 字段名称, "value": 字段值 }]
        :param partition_name: 分区名称（可选）
        :param embed_model: 向量化模型，默认 "BCEMBEDDING"
        :param callback: 回调信息（可选）
        :return: JSON 响应
        """
        url = f"{self.base_url}/vector/upsert"
        payload = {
            "databaseName": database_name,
            "collectionName": collection_name,
            "fields": fields,
            "partitionName": partition_name,
            "embedModel": embed_model,
            "callback": callback
        }
        response = requests.post(url, json=payload)
        return response.json()

    def search_vector(self, collection_name, vectors, metric_type="COSINE", top_k=1, expr="",
                      out_fields=None, partition_names=None, embed_model="BCEMBEDDING", round_decimal=-1,
                      params="{}", consistency_level=None, ignore_growing=False):
        """
        搜索向量
        :param collection_name: 集合名称
        :param vectors: 要搜索的向量
        :param metric_type: 度量类型，默认 "COSINE"
        :param top_k: 查询数量，默认 1
        :param expr: 查询表达式（可选）
        :param out_fields: 查询的字段列表（可选）
        :param partition_names: 分区名称列表（可选）
        :param embed_model: 向量化模型，默认 "BCEMBEDDING"
        :param round_decimal: 返回结果的小数位，默认 -1
        :param params: JSON 格式的额外参数，默认 "{}"
        :param consistency_level: 一致性级别（可选）
        :param ignore_growing: 是否忽略增长，默认 False
        :return: JSON 响应
        """
        url = f"{self.base_url}/vector/search"
        payload = {
            "collectionName": collection_name,
            "vectors": vectors,
            "metricType": metric_type,
            "vectorFieldName": "vector",
            "topK": top_k,
            "expr": expr,
            "outFields": out_fields or [],
            "partitionNames": partition_names or [],
            "embedModel": embed_model,
            "roundDecimal": round_decimal,
            "params": params,
            "consistencyLevel": consistency_level,
            "ignoreGrowing": ignore_growing
        }
        response = requests.post(url, json=payload)
        return response.json()

    def query_vector(self, collection_name, expr, out_fields=None, partition_names=None,
                     consistency_level=None, offset=0, limit=0, ignore_growing=False):
        """
        查询向量
        :param collection_name: 集合名称
        :param expr: 过滤条件表达式
        :param out_fields: 查询的字段列表（可选）
        :param partition_names: 分区名称列表（可选）
        :param consistency_level: 一致性级别（可选）
        :param offset: 偏移量，默认 0
        :param limit: 限制数量，默认 0
        :param ignore_growing: 是否忽略未完成的向量，默认 False
        :return: JSON 响应
        """
        url = f"{self.base_url}/vector/query"
        payload = {
            "collectionName": collection_name,
            "expr": expr,
            "outFields": out_fields or [],
            "partitionNames": partition_names or [],
            "consistencyLevel": consistency_level,
            "offset": offset,
            "limit": limit,
            "ignoreGrowing": ignore_growing
        }
        response = requests.post(url, json=payload)
        return response.json()

    def create_partition(self, collection_name, partition_name):
        """
        创建分区
        :param collection_name: 集合名称
        :param partition_name: 分区名称
        :return: JSON 响应
        """
        url = f"{self.base_url}/partition/create"
        payload = {
            "collectionName": collection_name,
            "partitionName": partition_name
        }
        response = requests.post(url, json=payload)
        return response.json()

    def has_partition(self, collection_name, partition_name):
        url = f"{self.base_url}/partition/has?collectionName={collection_name}&partitionName={partition_name}"
        response = requests.get(url).json().get('result')
        return response

    def has_collection(self, collection_name):
        url = f"{self.base_url}/collection/has?collectionName={collection_name}"
        response = requests.get(url).json().get('result')
        return response

    def create_collection(self, collection_name, field_types, shards_num=0, description=None,
                          partitions_num=0, consistency_level="BOUNDED", database_name=None,
                          enable_dynamic_field=False):
        """
        创建集合
        :param collection_name: 集合名称
        :param field_types: 字段类型列表，格式为 [{ "name": 字段名称, "dataType": 数据类型, ... }]
        :param shards_num: 分片数量，默认 0
        :param description: 集合描述（可选）
        :param partitions_num: 分区数量，默认 0
        :param consistency_level: 一致性等级，默认 "BOUNDED"
        :param database_name: 数据库名称（可选）
        :param enable_dynamic_field: 是否启用动态字段，默认 False
        :return: JSON 响应
        """
        url = f"{self.base_url}/collection/create"
        payload = {
            "collectionName": collection_name,
            "shardsNum": shards_num,
            "description": description,
            "fieldTypes": field_types,
            "partitionsNum": partitions_num,
            "consistencyLevel": consistency_level,
            "databaseName": database_name,
            "enableDynamicField": enable_dynamic_field
        }
        response = requests.post(url, json=payload)
        return response.json()

    def create_index(self, collection_name, field_name, index_name, index_type, extra_param,
                     metric_type, sync_mode=True, sync_waiting_interval=500, sync_waiting_timeout=600):
        """
        创建索引
        :param collection_name: 集合名称
        :param field_name: 字段名称
        :param index_name: 索引名称
        :param index_type: 索引类型
        :param extra_param: 额外参数，JSON 字符串格式
        :param metric_type: 度量类型
        :param sync_mode: 同步模式，默认 True
        :param sync_waiting_interval: 同步等待间隔，默认 500
        :param sync_waiting_timeout: 同步等待超时，默认 600
        :return: JSON 响应
        """
        url = f"{self.base_url}/index/create"
        payload = {
            "collectionName": collection_name,
            "fieldName": field_name,
            "indexName": index_name,
            "indexType": index_type,
            "extraParam": extra_param,
            "metricType": metric_type,
            "syncMode": sync_mode,
            "syncWaitingInterval": sync_waiting_interval,
            "syncWaitingTimeout": sync_waiting_timeout
        }
        response = requests.post(url, json=payload)
        return response.json()

    def delete_vector(self, collection_name, expr, partition_name=""):
        """
        删除向量
        :param collection_name: 集合名称
        :param expr: 表达式
        :param partition_name: 分区名称（可选）
        :return: JSON 响应
        """
        url = f"{self.base_url}/vector/delete"
        payload = {
            "collectionName": collection_name,
            "expr": expr,
            "partitionName": partition_name
        }
        response = requests.delete(url, json=payload)
        return response.json()

    def drop_partition(self, collection_name, partition_name):
        """
        删除分区
        :param collection_name: 集合名称
        :param partition_name: 分区名称
        :return: JSON 响应
        """
        url = f"{self.base_url}/partition/drop"
        payload = {
            "collectionName": collection_name,
            "partitionName": partition_name
        }
        response = requests.delete(url, json=payload)
        return response.json()

    def drop_index(self, collection_name, index_name):
        """
        删除索引
        :param collection_name: 集合名称
        :param index_name: 索引名称
        :return: JSON 响应
        """
        url = f"{self.base_url}/index/drop"
        payload = {
            "collectionName": collection_name,
            "indexName": index_name
        }
        response = requests.delete(url, json=payload)
        return response.json()

    def drop_collection(self, collection_name, database_name=None):
        """
        删除集合
        :param collection_name: 集合名称
        :param database_name: 数据库名称（可选）
        :return: JSON 响应
        """
        url = f"{self.base_url}/collection/drop"
        payload = {
            "collectionName": collection_name,
            "databaseName": database_name
        }
        response = requests.delete(url, json=payload)
        return response.json()

    def load_collection(self, collection_name, database_name=None, sync_load=True, sync_load_waiting_interval=500,
                        sync_load_waiting_timeout=60, replica_number=1, refresh=False, resource_groups=None):
        """
        加载集合到内存中
        :param collection_name: 集合名称
        :param database_name: 数据库名称（可选）
        :param sync_load: 是否同步加载，默认 True
        :param sync_load_waiting_interval: 同步加载等待间隔，默认 500
        :param sync_load_waiting_timeout: 同步加载等待超时，默认 60
        :param replica_number: 副本数量，默认 1
        :param refresh: 是否刷新，默认 False
        :param resource_groups: 资源组列表（可选）
        :return: JSON 响应
        """
        url = f"{self.base_url}/collection/load"
        payload = {
            "collectionName": collection_name,
            "databaseName": database_name,
            "syncLoad": sync_load,
            "syncLoadWaitingInterval": sync_load_waiting_interval,
            "syncLoadWaitingTimeout": sync_load_waiting_timeout,
            "replicaNumber": replica_number,
            "refresh": refresh,
            "resourceGroups": resource_groups or []
        }
        response = requests.post(url, json=payload)
        return response.json()


def text_embeddings(text):
    url = 'http://*************:6006/v1/embeddings'
    data = {
        "model": "bce-embedding-base_v1",
        "input": text,
        "encoding_format": "float"
    }
    response = requests.post(url, json=data)
    return json.loads(response.content)['data'][0]['embedding']


def create_crm_knowledge_base():
    # 初始化 Milvus 客户端
    client = MilvusClient(milvus_host)

    # 创建集合
    response_create_collection = client.create_collection(
        collection_name="crm_knowledge_base",
        description="crm问答知识库",
        field_types=[
            {
                "name": "id",
                "primaryKey": True,
                "autoID": True,
                "dataType": "Int64",
                "description": "主键"
            },
            {
                "name": "content_type",
                "dataType": "VarChar",
                "maxLength": 128,
                "description": "文本类型"
            },
            {
                "name": "content",
                "dataType": "VarChar",
                "maxLength": 20480,
                "description": "文本"
            },
            {
                "name": "vector",
                "dataType": "FloatVector",
                "dimension": 768,
                "description": "向量文本"
            }
        ]
    )
    print("创建集合响应:", response_create_collection)

    # 创建索引
    response_create_index = client.create_index(
        collection_name="crm_knowledge_base",
        field_name="vector",
        index_name="vector_index",
        index_type="HNSW",
        extra_param='{"M": 32, "efConstruction": 256}',
        metric_type="COSINE"
    )
    print("创建索引响应:", response_create_index)

    # 加载集合到内存中
    response_load_collection = client.load_collection(
        collection_name="crm_knowledge_base"
    )
    print("加载集合响应:", response_load_collection)


def read_markdown(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()


def split_content(markdown_content):
    # 使用正则表达式按 '## ' 分割内容
    sections = re.split(r'\n## ', markdown_content.strip())
    # 恢复每个 section 的 '## ' 前缀
    sections = ['## ' + section if not section.startswith('## ') else section for section in sections]
    return sections


def insert_data(content):
    vector = text_embeddings(content)
    client = MilvusClient(milvus_host)
    response = client.insert_vector(
        database_name="",
        collection_name="crm_knowledge_base",
        fields=[
            [
                {"name": "content_type", "value": ""},
                {"name": "content", "value": content},
                {"name": "vector", "value": vector}
            ],

        ]
    )
    print(response)


if __name__ == '__main__':
    # 创建crm知识库
    # create_crm_knowledge_base()
    # 插入数据
    # 读取 Markdown 文件
    markdown_content = read_markdown('knowledge_base.md')

    # 拆分内容
    sections = split_content(markdown_content)

    # 插入每个段落到向量库
    for section in sections:
        insert_data(section)

    # 初始化 Milvus 客户端
    # client = MilvusClient(milvus_host)
    # # 定义删除表达式
    # expr = "id == 451334609020416809"  # 替换为实际的 id 值
    #
    # # 删除数据
    # response_delete = client.delete_vector(
    #     collection_name="crm_knowledge_base",
    #     expr=expr
    # )
    # print("删除数据响应:", response_delete)
