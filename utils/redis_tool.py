import ast
import os

from redis.sentinel import Sentinel

# REDIS哨兵配置
REDIS_SENTINEL_CONFIG = {
    'hosts_and_ports': os.getenv('REDIS_SENTINEL_HOSTS_AND_PORTS'),
    'master_name': os.getenv('REDIS_SENTINEL_MASTER_NAME'),
    'password': os.getenv('REDIS_SENTINEL_PASSWORD'),
}


def get_redis_connect():
    # 哨兵节点地址列表
    sentinel_hosts = ast.literal_eval(REDIS_SENTINEL_CONFIG['hosts_and_ports'])
    # 创建哨兵对象
    sentinel = Sentinel(sentinel_hosts, socket_timeout=3)
    # 主节点名称
    master_name = REDIS_SENTINEL_CONFIG['master_name']
    # 使用哨兵对象获取主节点连接
    return sentinel.master_for(master_name, password=REDIS_SENTINEL_CONFIG['password'])


def remove_redis_info(key_name: str):
    r = get_redis_connect()
    if r.exists(key_name):
        r.delete(key_name)
    r.close()
