import os

from colorama import Fore, Style

from utils.load_env import logger

logger.info(f"""\nAgent API initialization begins
{Fore.WHITE}
 █████╗  ██████╗ ███████╗███╗   ██╗████████╗    ████████╗ ██████╗  ██████╗ ██╗     ███████╗     █████╗ ██████╗ ██╗
██╔══██╗██╔════╝ ██╔════╝████╗  ██║╚══██╔══╝    ╚══██╔══╝██╔═══██╗██╔═══██╗██║     ██╔════╝    ██╔══██╗██╔══██╗██║
███████║██║  ███╗█████╗  ██╔██╗ ██║   ██║          ██║   ██║   ██║██║   ██║██║     ███████╗    ███████║██████╔╝██║
██╔══██║██║   ██║██╔══╝  ██║╚██╗██║   ██║          ██║   ██║   ██║██║   ██║██║     ╚════██║    ██╔══██║██╔═══╝ ██║
██║  ██║╚██████╔╝███████╗██║ ╚████║   ██║          ██║   ╚██████╔╝╚██████╔╝███████╗███████║    ██║  ██║██║     ██║
╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝   ╚═╝          ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝╚══════╝    ╚═╝  ╚═╝╚═╝     ╚═╝   
{Style.RESET_ALL}
""")

os.environ['AGENT_TOOLS_API_INITED'] = 'AGENT_TOOLS_API_INITED'

import threading

from fastapi import FastAPI

from api import allotment_api, asset_restructuring_api, convertible_bond_api, \
    nonpublic_offering_api, public_offering_data_api, ipo_api, target_asset_api, violation_api, rep_api, \
    refinancing_laws_api, business_opportunity_api, email_api
from utils.schedule_tasks import run_schedule

from api.mcp_api import mcp, mcp_app

# 创建FastAPI应用
app = FastAPI(lifespan=mcp_app.lifespan)

# 添加所有路由
app.include_router(ipo_api.router)
app.include_router(allotment_api.router)
app.include_router(asset_restructuring_api.router)
app.include_router(convertible_bond_api.router)
app.include_router(nonpublic_offering_api.router)
app.include_router(public_offering_data_api.router)
app.include_router(target_asset_api.router)
app.include_router(violation_api.router)
app.include_router(rep_api.router)
app.include_router(refinancing_laws_api.router)
app.include_router(business_opportunity_api.router)

# 挂载MCP应用
app.mount("/mcp-server", mcp_app)

app.include_router(email_api.router)

@app.get('/info')
async def info():
    return {"IPO案例检索AGENT": {"http": "/ipo/case", "websocket": "/ipo/ws"},
            "配股发行案例检索AGENT": {"http": "/allotment/case", "websocket": "/allotment/ws"},
            "资产重组案例检索AGENT": {"http": "/asset_restructuring/case", "websocket": "/asset_restructuring/ws"},
            "可转债案例检索AGENT": {"http": "/convertible_bond/case", "websocket": "/convertible_bond/ws"},
            "问询函件检索AGENT": {"http": "/inquery_letter/case", "websocket": "/inquery_letter/ws"},
            "非公开发行案例检索AGENT": {"http": "/nonpublic_offering/case", "websocket": "/nonpublic_offering/ws"},
            "公开发行案例检索AGENT": {"http": "/public_offering_data/case", "websocket": "/public_offering_data/ws"},
            "重组标的检索": {"http": "/target_asset/case", "websocket": "/target_asset/ws"},
            "违规案例检索AGENT": {"http": "/violation/case", "websocket": "/violation/ws"},
            "非公开发行法规AGENT": {"websocket": "/refinancing_laws/ws"},
            }


def start_schedule_in_thread():
    schedule_thread = threading.Thread(target=run_schedule)
    schedule_thread.daemon = True  # 设置为守护线程，主程序退出时自动退出
    schedule_thread.start()


# 启动 FastAPI 服务器并运行定时任务
if __name__ == "__main__":
    start_schedule_in_thread()  # 启动定时任务
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
