# 违规案例MCP调用文档

## 概述

采用 MCP Streamable HTTP 传输协议。该工具专门用于查询和分析违规案例数据，支持自然语言查询，能够智能检索相关的违规案例信息并提供详细的分析结果。

## 功能特性

- **智能查询**: 支持自然语言输入，无需复杂的查询语法
- **全面覆盖**: 可查询各类违规案例，包括信息披露违规、内幕交易、市场操纵等
- **详细分析**: 提供违规案例的详细信息、处罚情况和案例分析接口信息

## 服务端点

- **MCP 服务地址**: https://agenttools.valueonline.cn/mcp-server/mcp/
- **传输协议**: Streamable HTTP
- **工具名称**: query_violation_cases_tool

## 认证方式

该服务需要Bearer Token认证，请求头中必须包含authorization字段。

### 获取访问令牌

通过OAuth2客户端凭证模式获取访问令牌：

- **请求地址**: https://services.valueonline.cn/oauth/token
- **请求方法**: GET
- **请求参数**:
  - grant_type: client_credentials
  - response_type: token
  - client_id: 610abd52fac84f60
  - client_secret: 4ea53ec5610abd52fac84f60a02bcf4c

### 请求头设置

在调用MCP服务时，需要在请求头中添加：

- **authorization**: Bearer {access_token}
- **Content-Type**: application/json

## 工具参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| user_input | string | 是 | 用户的查询问题，支持自然语言描述 |

## 返回结果

| 字段名 | 类型 | 描述 |
|--------|------|------|
| summary | string | 完整的查询结果和分析内容 |
| ai_agent_result | string | AI代理的具体查询结果数据 |
| user_input | string | 原始用户输入 |
| user_id | string | 系统生成的会话ID |

## 调用流程

1. **获取访问令牌**: 向OAuth服务端点发送GET请求，获取access_token
2. **构建请求**: 使用MCP JSON-RPC 2.0协议格式构建请求体
3. **设置认证**: 在请求头中添加Bearer Token认证信息
4. **发送请求**: 向MCP服务地址发送POST请求
5. **处理响应**: 解析返回的JSON-RPC响应结果

## 注意事项

- 服务地址必须以斜杠(/)结尾
- access_token有效期有限，建议在每次调用前重新获取
- 支持自然语言查询，可以直接描述要查找的违规案例类型
- 返回结果包含详细的案例分析和相关信息
- 请求体需要符合MCP JSON-RPC 2.0协议规范
