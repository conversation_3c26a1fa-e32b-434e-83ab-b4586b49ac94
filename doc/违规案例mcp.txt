概述
采用 MCP Streamable HTTP 传输协议。该工具专门用于查询和分析违规案例数据，支持自然语言查询，能够智能检索相关的违规案例信息并提供详细的分析结果。
功能特性
•	智能查询: 支持自然语言输入，无需复杂的查询语法
•	全面覆盖: 可查询各类违规案例，包括信息披露违规、内幕交易、市场操纵等
•	详细分析: 提供违规案例的详细信息、处罚情况和案例分析接口信息
服务端点
•	MCP 服务地址: https://agenttools.valueonline.cn/mcp-server/mcp/
•	传输协议: Streamable HTTP
•	工具名称: query_violation_cases_tool

认证方式
该服务需要Bearer Token认证，请求头中必须包含authorization字段。

获取访问令牌
使用以下方式获取access_token：

```python
import requests

resp = requests.get(
    'https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=610abd52fac84f60&client_secret=4ea53ec5610abd52fac84f60a02bcf4c'
)
access_token = resp.json().get('access_token')
```

请求头设置
在调用MCP服务时，需要在请求头中添加：
•	authorization: Bearer {access_token}
•	Content-Type: application/json
工具参数
参数名	类型	必填	描述
user_input	string	是	用户的查询问题，支持自然语言描述
返回结果
字段名	类型	描述
summary	string	完整的查询结果和分析内容
ai_agent_result	string	AI代理的具体查询结果数据
user_input	string	原始用户输入
user_id	string	系统生成的会话ID

调用示例
以下是完整的调用示例：

```python
import requests

# 1. 获取访问令牌
resp = requests.get(
    'https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=610abd52fac84f60&client_secret=4ea53ec5610abd52fac84f60a02bcf4c'
)
access_token = resp.json().get('access_token')

# 2. 调用MCP服务
headers = {
    'authorization': f'Bearer {access_token}',
    'Content-Type': 'application/json'
}

# MCP调用请求体
mcp_request = {
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
        "name": "query_violation_cases_tool",
        "arguments": {
            "user_input": "查询财务造假相关的违规案例"
        }
    }
}

# 发送请求
response = requests.post(
    'https://agenttools.valueonline.cn/mcp-server/mcp/',
    headers=headers,
    json=mcp_request
)

result = response.json()
print(result)
```

注意事项
•	服务地址必须以斜杠(/)结尾
•	access_token有效期有限，建议在每次调用前重新获取
•	支持自然语言查询，可以直接描述要查找的违规案例类型
•	返回结果包含详细的案例分析和相关信息

