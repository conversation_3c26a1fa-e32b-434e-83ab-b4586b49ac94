import pdfplumber
from io import StringIO
import numpy as np
import cv2
import traceback


def compute_spans_from_bbox(pdf_path, page_numbers):
    """提取表格并计算单元格的跨行跨列信息，使用多种方法结合"""
    cells = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page_num in page_numbers:
                if page_num - 1 >= len(pdf.pages):
                    print(f"Page {page_num} is out of range.")
                    continue

                page = pdf.pages[page_num - 1]

                # 提取表格
                tables = page.extract_tables()
                if not tables:
                    print(f"No tables found on page {page_num}.")
                    continue

                # 获取页面上的所有文本及其边界框
                words = page.extract_words(keep_blank_chars=True)

                # 获取页面上的线条
                h_lines = page.horizontal_edges
                v_lines = page.vertical_edges

                for table_idx, table in enumerate(tables):
                    if not table:
                        continue

                    # 构建初始网格
                    max_row = len(table)
                    max_col = max(len(row) for row in table) if table else 0
                    grid = [[None for _ in range(max_col)] for _ in range(max_row)]

                    # 填充网格并收集单元格信息
                    for row_idx, row in enumerate(table):
                        for col_idx, cell_text in enumerate(row):
                            if col_idx < len(row):
                                grid[row_idx][col_idx] = cell_text

                                # 收集单元格信息，不再尝试匹配边界框
                                cells.append({
                                    'row': row_idx,
                                    'col': col_idx,
                                    'text': cell_text,
                                    'page': page_num,
                                    'table_idx': table_idx
                                })

                    # 使用多种方法检测合并单元格
                    rowspan, colspan = detect_merged_cells_combined(grid, cells, h_lines, v_lines)

                    # 返回第一个表格的结果（如果需要处理多个表格，可以修改此逻辑）
                    return grid, rowspan, colspan

    except Exception as e:
        print(f"Error processing PDF: {e}")
        traceback.print_exc()
        return [], {}, {}

    # 如果没有找到任何表格
    return [], {}, {}


def detect_merged_cells_combined(grid, cells, h_lines, v_lines):
    """结合多种方法检测合并单元格"""
    max_row = len(grid)
    max_col = max(len(row) for row in grid) if grid else 0

    # 初始化结果
    rowspan = {}
    colspan = {}

    # 方法1: 基于内容的启发式方法
    rowspan_content, colspan_content = detect_by_content(grid, max_row, max_col)

    # 方法2: 基于空单元格模式的检测
    rowspan_empty, colspan_empty = detect_by_empty_cells(grid, max_row, max_col)

    # 方法3: 基于线条的检测
    rowspan_lines, colspan_lines = detect_by_lines(h_lines, v_lines, max_row, max_col)

    # 合并结果，优先级: 线条 > 空单元格 > 内容
    # 合并 rowspan
    for key in set(list(rowspan_lines.keys()) + list(rowspan_empty.keys()) + list(rowspan_content.keys())):
        if key in rowspan_lines:
            rowspan[key] = rowspan_lines[key]
        elif key in rowspan_empty:
            rowspan[key] = rowspan_empty[key]
        elif key in rowspan_content:
            rowspan[key] = rowspan_content[key]

    # 合并 colspan
    for key in set(list(colspan_lines.keys()) + list(colspan_empty.keys()) + list(colspan_content.keys())):
        if key in colspan_lines:
            colspan[key] = colspan_lines[key]
        elif key in colspan_empty:
            colspan[key] = colspan_empty[key]
        elif key in colspan_content:
            colspan[key] = colspan_content[key]

    # 确保每个非空单元格都有默认值
    for row in range(max_row):
        for col in range(max_col):
            if row < len(grid) and col < len(grid[row]) and grid[row][col] is not None:
                if (row, col) not in rowspan:
                    rowspan[(row, col)] = 1
                if (row, col) not in colspan:
                    colspan[(row, col)] = 1

    # 验证合并单元格的有效性
    validate_spans(rowspan, colspan, max_row, max_col)

    return rowspan, colspan


def detect_by_content(grid, max_row, max_col):
    """基于单元格内容相同性检测合并单元格"""
    rowspan = {}
    colspan = {}

    # 创建网格副本以避免修改原始网格
    grid_copy = [row[:] for row in grid]

    # 检测水平方向的合并
    for row in range(max_row):
        col = 0
        while col < max_col:
            if row < len(grid_copy) and col < len(grid_copy[row]) and grid_copy[row][col] is not None:
                start_col = col
                current_text = grid_copy[row][col]
                span = 1

                # 查找连续相同内容的单元格
                for next_col in range(col + 1, max_col):
                    if (next_col < len(grid_copy[row]) and
                            grid_copy[row][next_col] is not None and
                            grid_copy[row][next_col] == current_text):
                        span += 1
                    else:
                        break

                if span > 1:
                    colspan[(row, start_col)] = span
                    # 标记被合并的单元格
                    for c in range(start_col + 1, start_col + span):
                        if c < len(grid_copy[row]):
                            grid_copy[row][c] = None

                col += span
            else:
                col += 1

    # 检测垂直方向的合并
    for col in range(max_col):
        row = 0
        while row < max_row:
            if row < len(grid_copy) and col < len(grid_copy[row]) and grid_copy[row][col] is not None:
                start_row = row
                current_text = grid_copy[row][col]
                span = 1

                # 查找连续相同内容的单元格
                for next_row in range(row + 1, max_row):
                    if (next_row < len(grid_copy) and
                            col < len(grid_copy[next_row]) and
                            grid_copy[next_row][col] is not None and
                            grid_copy[next_row][col] == current_text):
                        span += 1
                    else:
                        break

                if span > 1:
                    rowspan[(start_row, col)] = span
                    # 标记被合并的单元格
                    for r in range(start_row + 1, start_row + span):
                        if r < len(grid_copy) and col < len(grid_copy[r]):
                            grid_copy[r][col] = None

                row += span
            else:
                row += 1

    return rowspan, colspan


def detect_by_empty_cells(grid, max_row, max_col):
    """基于空单元格模式检测合并单元格"""
    rowspan = {}
    colspan = {}

    # 创建网格副本以避免修改原始网格
    grid_copy = [row[:] for row in grid]

    # 检测水平方向的空单元格模式
    for row in range(max_row):
        col = 0
        while col < max_col:
            if row < len(grid_copy) and col < len(grid_copy[row]) and grid_copy[row][col] is not None:
                start_col = col
                span = 1

                # 查找后续的空单元格
                for next_col in range(col + 1, max_col):
                    if (next_col < len(grid_copy[row]) and
                            grid_copy[row][next_col] is None):
                        span += 1
                    else:
                        break

                if span > 1:
                    colspan[(row, start_col)] = span

                col += 1
            else:
                col += 1

    # 检测垂直方向的空单元格模式
    for col in range(max_col):
        row = 0
        while row < max_row:
            if row < len(grid_copy) and col < len(grid_copy[row]) and grid_copy[row][col] is not None:
                start_row = row
                span = 1

                # 查找后续的空单元格
                for next_row in range(row + 1, max_row):
                    if (next_row < len(grid_copy) and
                            col < len(grid_copy[next_row]) and
                            grid_copy[next_row][col] is None):
                        span += 1
                    else:
                        break

                if span > 1:
                    rowspan[(start_row, col)] = span

                row += 1
            else:
                row += 1

    return rowspan, colspan


def detect_by_lines(h_lines, v_lines, max_row, max_col):
    """基于表格线条检测合并单元格"""
    rowspan = {}
    colspan = {}

    # 如果没有线条信息，返回空结果
    if not h_lines or not v_lines:
        return {}, {}

    # 这里需要更复杂的算法来分析线条并检测合并单元格
    # 由于这需要更详细的实现，这里提供一个简化版本

    # 将线条转换为网格点
    h_points = set()
    v_points = set()

    for line in h_lines:
        x0, y0, x1, y1 = line['x0'], line['top'], line['x1'], line['top']
        for x in range(int(x0), int(x1) + 1, 10):  # 每10个像素取一个点
            h_points.add((x, int(y0)))

    for line in v_lines:
        x0, y0, x1, y1 = line['x0'], line['top'], line['x0'], line['bottom']
        for y in range(int(y0), int(y1) + 1, 10):  # 每10个像素取一个点
            v_points.add((int(x0), y))

    # 检测缺失的线条来识别合并单元格
    # 这里简化处理，实际应用中需要更复杂的算法

    return rowspan, colspan


def validate_spans(rowspan, colspan, max_row, max_col):
    """验证合并单元格的有效性，避免重叠和超出边界"""
    # 检查是否有超出表格边界的合并单元格
    invalid_keys = []
    for (row, col), span in rowspan.items():
        if row + span > max_row:
            invalid_keys.append((row, col))

    for key in invalid_keys:
        del rowspan[key]

    invalid_keys = []
    for (row, col), span in colspan.items():
        if col + span > max_col:
            invalid_keys.append((row, col))

    for key in invalid_keys:
        del colspan[key]

    # 检查是否有重叠的合并单元格
    # 这里简化处理，实际应用中需要更复杂的算法


def table_to_html(table, rowspan, colspan):
    """将表格转换为 HTML，应用 rowspan 和 colspan"""
    if not table or not any(row for row in table):
        return "<p>No tables found in the specified pages.</p>"

    html = StringIO()
    html.write("<table border='1' style='border-collapse: collapse; text-align: center;'>\n")

    max_row = len(table)
    max_col = max(len(row) for row in table) if max_row > 0 else 0

    # 创建一个标记矩阵，用于跟踪已经处理过的单元格
    processed = [[False for _ in range(max_col)] for _ in range(max_row)]

    for row_idx in range(max_row):
        html.write("  <tr>\n")
        for col_idx in range(max_col):
            # 跳过已处理的单元格（被合并的单元格）
            if processed[row_idx][col_idx]:
                continue

            if col_idx < len(table[row_idx]) and table[row_idx][col_idx] is not None:
                rs = rowspan.get((row_idx, col_idx), 1)
                cs = colspan.get((row_idx, col_idx), 1)
                cell_content = table[row_idx][col_idx] if table[row_idx][col_idx] else "&nbsp;"

                # 标记被此单元格合并的单元格
                for r in range(row_idx, row_idx + rs):
                    for c in range(col_idx, col_idx + cs):
                        if r < max_row and c < max_col:
                            processed[r][c] = True

                # 确定是否为表头单元格
                tag = "th" if row_idx < 2 else "td"  # 假设前两行是表头

                # 添加单元格
                html.write(f"    <{tag} rowspan='{rs}' colspan='{cs}'>{cell_content}</{tag}>\n")
            elif not processed[row_idx][col_idx]:
                # 处理空单元格
                html.write(f"    <td>&nbsp;</td>\n")
                processed[row_idx][col_idx] = True

        html.write("  </tr>\n")

    html.write("</table>")
    return html.getvalue()


def extract_table_to_html(pdf_path, page_numbers):
    """提取表格并转换为HTML"""
    # 提取表格并计算合并单元格
    grid, rowspan, colspan = compute_spans_from_bbox(pdf_path, page_numbers)

    # 如果没有找到表格，返回空结果
    if not grid:
        return "<p>No tables found in the specified pages.</p>"

    # 转换为HTML
    html_table = table_to_html(grid, rowspan, colspan)

    return html_table


# 使用示例
if __name__ == "__main__":
    pdf_path = "input.pdf"  # 替换为你的PDF文件路径
    page_numbers = [110, 111]  # 指定页码

    try:
        html_content = extract_table_to_html(pdf_path, page_numbers)

        # 保存为HTML文件
        with open("table.html", "w", encoding="utf-8") as f:
            f.write("""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>Extracted Table</title>
        <style>
            table { font-family: Arial, sans-serif; border-collapse: collapse; width: 100%; }
            td, th { border: 1px solid black; padding: 8px; text-align: center; }
            th { background-color: #f2f2f2; }
        </style>
    </head>
    <body>
        <h2>Extracted Table from PDF (Pages """ + ", ".join(map(str, page_numbers)) + """)</h2>
        <!-- HTML table content -->
        """ + html_content + """
    </body>
    </html>
            """)
        print("HTML table generated successfully. Open 'table.html' in a browser to view.")
    except Exception as e:
        print(f"Error generating HTML table: {e}")
        traceback.print_exc()