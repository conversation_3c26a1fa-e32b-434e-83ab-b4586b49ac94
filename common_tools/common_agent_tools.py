# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：common_agent_tools.py 
@Date    ：2024/12/16 下午2:38 
@Desc    ：
"""
from langchain_community.utilities import BingSearchAPIWrapper
from langchain_core.tools import tool

search = BingSearchAPIWrapper(bing_subscription_key='********************************',
                              bing_search_url='https://api.bing.microsoft.com/v7.0/search')


@tool
def bing_search(query1: str, query2: str, query3: str) -> str:
    """
    提供Bing搜索功能，用于处理用户提出的问题。生成三个不同的查询内容，以覆盖更全面的信息。
    在以下情况启用此功能：
    1.当其他工具查询失败或结果不适用时
    2.根据判断，其他工具不适合处理当前问题。

    Args:
        query1: 第一个搜索内容。
        query2: 第二个搜索内容。
        query3: 第三个搜索内容。

    Returns:
        返回Bing搜索的结果，包含三个查询的结果。
    """
    # 限制查询的域名
    site = " site:finance.eastmoney.com OR site:caam.org.cn OR site:stats.gov.cn OR site:cs.com.cn OR site:cls.cn OR site:csrc.gov.cn OR site:sse.com.cn OR site:szse.cn OR site:bse.cn OR site:pbc.gov.cn OR site:mof.gov.cn OR site:ndrc.gov.cn OR site:sasac.gov.cn OR site:neeq.com.cn OR site:finance.caixin.com OR site:cfi.cn OR site:cninfo.com.cn OR site:jjckb.xinhuanet.com"

    query1 += site
    query2 += site
    query3 += site

    results1 = search.results(query1, 3)  # 每个查询只取3个结果
    results2 = search.results(query2, 3)
    results3 = search.results(query3, 3)

    all_results = {
        "query1_results": str(results1),
        "query2_results": str(results2),
        "query3_results": str(results3),
    }

    return str(all_results)  # 返回一个包含所有查询结果的字典字符串


def bing_search_1(query: str) -> list:
    """
    Bing搜索

    不算做Agent工具，无需加入工具列表

    Args:
        query: 搜索内容。

    Returns:
        返回Bing搜索的结果，包含三个查询的结果。
    """
    # 限制查询的域名
    site = " site:finance.eastmoney.com OR site:caam.org.cn OR site:stats.gov.cn OR site:cs.com.cn OR site:cls.cn OR site:csrc.gov.cn OR site:sse.com.cn OR site:szse.cn OR site:bse.cn OR site:pbc.gov.cn OR site:mof.gov.cn OR site:ndrc.gov.cn OR site:sasac.gov.cn OR site:neeq.com.cn OR site:finance.caixin.com OR site:cfi.cn OR site:cninfo.com.cn OR site:jjckb.xinhuanet.com"

    query += site
    return search.results(query, 5)


agent_base_tools = [bing_search]
