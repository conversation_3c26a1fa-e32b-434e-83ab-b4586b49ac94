# -*- coding: utf-8 -*-
"""
分页查询会话数据模型
"""
from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel


class QuerySession(BaseModel):
    """查询会话模型"""
    id: Optional[int] = None
    session_id: str
    user_id: str
    principal_id: Optional[str] = None
    case_type: str
    original_query: str
    request_body: Dict[str, Any]
    request_url: str
    total_count: int = 0
    page_size: int = 20
    current_page: int = 1
    status: int = 1
    expires_at: datetime
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PaginationRequest(BaseModel):
    """分页请求模型"""
    session_id: str
    page: int = 1
    page_size: int = 20


class SessionInfo(BaseModel):
    """会话信息模型"""
    session_id: str
    case_type: str
    original_query: str
    total_count: int
    current_page: int
    page_size: int
    expires_at: datetime
    status: int
