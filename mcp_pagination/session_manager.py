# -*- coding: utf-8 -*-
"""
分页查询会话管理器
"""
import uuid
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

from sub_agents.sql_database_chain.violation_data_search.violation_utils import clean_dict, replace_keys
from utils.db_tool import get_compiled_mappers_from_mysql
from utils.load_env import logger
from .database import SessionDatabase
from .models import QuerySession


class SessionManager:
    """查询会话管理器"""

    @staticmethod
    def generate_session_id() -> str:
        """生成会话ID"""
        return f"sess_{uuid.uuid4().hex[:16]}"

    @staticmethod
    def create_session(user_id: str, principal_id: Optional[str], case_type: str,
                      original_query: str, request_body: Dict[str, Any],
                      request_url: str, ai_agent_result: Dict[str, Any]) -> Optional[str]:
        """
        创建查询会话
        
        Args:
            user_id: 用户ID
            principal_id: 用户身份ID
            case_type: 案例类型
            original_query: 原始查询
            request_body: 请求体
            request_url: 请求URL
            ai_agent_result: AI代理结果
            
        Returns:
            session_id: 会话ID，失败返回None
        """
        try:
            session_id = SessionManager.generate_session_id()

            # 从ai_agent_result中提取分页信息
            total_count = ai_agent_result.get('案例总数', 0)
            page_size = ai_agent_result.get('每页条数', 20)
            current_page = ai_agent_result.get('当前页码', 0) + 1  # API返回的是0基础，转换为1基础

            # 创建会话对象
            session = QuerySession(
                session_id=session_id,
                user_id=user_id,
                principal_id=principal_id,
                case_type=case_type,
                original_query=original_query,
                request_body=request_body,
                request_url=request_url,
                total_count=total_count,
                page_size=page_size,
                current_page=current_page,
                expires_at=datetime.now() + timedelta(hours=24)
            )

            # 保存到数据库
            if SessionDatabase.create_session(session):
                logger.info(f"创建查询会话成功: {session_id}, 案例类型: {case_type}, 总数: {total_count}")
                return session_id
            else:
                logger.error(f"保存查询会话失败: {session_id}")
                return None

        except Exception as e:
            logger.error(f"创建查询会话异常: {str(e)}")
            return None

    @staticmethod
    def get_paginated_data(session_id: str, page: int, page_size: int) -> Optional[Dict[str, Any]]:
        """
        获取分页数据
        
        Args:
            session_id: 会话ID
            page: 页码（1基础）
            page_size: 每页大小
            
        Returns:
            分页数据结果，失败返回None
        """
        try:
            # 获取会话信息
            session = SessionDatabase.get_session(session_id)
            if not session:
                logger.warning(f"会话不存在或已过期: {session_id}")
                return None

            # 修改URL中的分页参数
            parsed_url = urlparse(session.request_url)
            query_params = parse_qs(parsed_url.query)

            # 更新分页参数
            query_params['page'] = [str(page - 1)]  # 转换为0基础
            query_params['size'] = [str(page_size)]

            # 重新构建URL
            new_query = urlencode(query_params, doseq=True)
            modified_url = urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                new_query,
                parsed_url.fragment
            ))

            # 保持原始请求体不变
            request_body = session.request_body

            # 发送请求获取数据
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'MCP-Pagination-Service/1.0'
            }

            response = requests.post(modified_url, headers=headers, json=request_body, timeout=30)

            if response.status_code == 200:
                result = response.json()

                # 更新会话当前页码
                SessionDatabase.update_session_page(session_id, page)

                logger.info(f"分页查询成功: {session_id}, 第{page}页, 每页{page_size}条")
                # 处理空值和替换key
                result = {
                    "案例总数": result['total'],
                    "每页条数": result['size'],
                    "当前页码": result['page'] + 1,
                    "案例列表": [clean_dict(line) for line in result['result']],
                }
                # 调用替换函数
                compiled_mappers = get_compiled_mappers_from_mysql('7')
                result = replace_keys(result, compiled_mappers)

                return result
            else:
                logger.error(f"分页查询API请求失败: {response.status_code}, {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"分页查询网络请求失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"分页查询异常: {str(e)}")
            return None

    @staticmethod
    def extract_session_data_from_custom_messages(custom_messages: list) -> Tuple[Optional[Dict], Optional[str], Optional[Dict]]:
        """
        从custom_messages中提取会话数据
        
        Args:
            custom_messages: 自定义消息列表
            
        Returns:
            (request_body, request_url, ai_agent_result)
        """
        request_body = None
        request_url = None
        ai_agent_result = None

        try:
            for msg in custom_messages:
                if isinstance(msg, dict):
                    # 提取AI_AGENT_RESPONSE
                    if "AI_AGENT_RESPONSE" in msg:
                        agent_response = msg["AI_AGENT_RESPONSE"]
                        request_body = agent_response.get("AI_AGENT_BODY")
                        request_url = agent_response.get("AI_AGENT_URL")
                        ai_agent_result = agent_response.get("AI_AGENT_RESULT")
                        break

                    # 提取QUERY_RESULT（作为备选）
                    elif "QUERY_RESULT" in msg:
                        ai_agent_result = msg["QUERY_RESULT"]

            return request_body, request_url, ai_agent_result

        except Exception as e:
            logger.error(f"提取会话数据失败: {str(e)}")
            return None, None, None

    @staticmethod
    def cleanup_expired_sessions() -> int:
        """清理过期会话"""
        return SessionDatabase.cleanup_expired_sessions()

    @staticmethod
    def extend_session(session_id: str, hours: int = 24) -> bool:
        """延长会话有效期"""
        return SessionDatabase.extend_session_expiry(session_id, hours)
