# -*- coding: utf-8 -*-
"""
分页查询会话清理任务
"""
import asyncio
from datetime import datetime
from utils.load_env import logger
from .session_manager import SessionManager


class SessionCleanupTask:
    """会话清理任务类"""
    
    def __init__(self, interval_hours: int = 1):
        """
        初始化清理任务
        
        Args:
            interval_hours: 清理间隔（小时），默认1小时
        """
        self.interval_hours = interval_hours
        self.running = False
        
    async def start(self):
        """启动清理任务"""
        if self.running:
            logger.warning("会话清理任务已在运行")
            return
            
        self.running = True
        logger.info(f"启动会话清理任务，清理间隔: {self.interval_hours}小时")
        
        while self.running:
            try:
                # 执行清理
                cleaned_count = SessionManager.cleanup_expired_sessions()
                if cleaned_count > 0:
                    logger.info(f"清理过期会话完成，共清理 {cleaned_count} 个会话")
                
                # 等待下次清理
                await asyncio.sleep(self.interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"会话清理任务异常: {str(e)}")
                # 出错后等待较短时间再重试
                await asyncio.sleep(300)  # 5分钟后重试
                
    def stop(self):
        """停止清理任务"""
        self.running = False
        logger.info("会话清理任务已停止")


# 全局清理任务实例
cleanup_task = SessionCleanupTask()


async def start_cleanup_task():
    """启动清理任务的便捷函数"""
    await cleanup_task.start()


def stop_cleanup_task():
    """停止清理任务的便捷函数"""
    cleanup_task.stop()
