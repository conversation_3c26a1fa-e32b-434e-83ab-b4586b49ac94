# -*- coding: utf-8 -*-
"""
分页查询会话数据库操作
"""
import json
from datetime import datetime, timedelta
from typing import Optional, List
from utils.load_env import logger
from utils.db_tool import get_db_connection
from .models import QuerySession, SessionInfo


class SessionDatabase:
    """查询会话数据库操作类"""

    @staticmethod
    def create_session(session: QuerySession) -> bool:
        """创建查询会话"""
        sql = """
        INSERT INTO mcp_query_sessions 
        (session_id, user_id, principal_id, case_type, original_query, 
         request_body, request_url, total_count, page_size, current_page, expires_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        try:
            request_body_json = json.dumps(session.request_body, ensure_ascii=False)
            
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (
                session.session_id, session.user_id, session.principal_id,
                session.case_type, session.original_query, request_body_json,
                session.request_url, session.total_count, session.page_size,
                session.current_page, session.expires_at
            ))
            connection.commit()
            cursor.close()
            connection.close()
            
            logger.info(f"创建查询会话成功: {session.session_id}")
            return True
        except Exception as e:
            logger.error(f"创建查询会话失败: {str(e)}")
            return False

    @staticmethod
    def get_session(session_id: str) -> Optional[QuerySession]:
        """根据session_id获取查询会话"""
        sql = """
        SELECT id, session_id, user_id, principal_id, case_type, original_query,
               request_body, request_url, total_count, page_size, current_page,
               status, expires_at, created_at, updated_at
        FROM mcp_query_sessions
        WHERE session_id = %s AND status = 1 AND expires_at > NOW()
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor(dictionary=True)
            cursor.execute(sql, (session_id,))
            result = cursor.fetchall()
            cursor.close()
            connection.close()

            if not result:
                return None
            
            session_data = result[0]
            
            # 解析请求体
            try:
                request_body = json.loads(session_data['request_body']) if isinstance(session_data['request_body'], str) else session_data['request_body']
            except json.JSONDecodeError:
                logger.error(f"解析会话 {session_id} 的请求体失败: {session_data['request_body']}")
                request_body = {}
            
            return QuerySession(
                id=session_data['id'],
                session_id=session_data['session_id'],
                user_id=session_data['user_id'],
                principal_id=session_data['principal_id'],
                case_type=session_data['case_type'],
                original_query=session_data['original_query'],
                request_body=request_body,
                request_url=session_data['request_url'],
                total_count=session_data['total_count'],
                page_size=session_data['page_size'],
                current_page=session_data['current_page'],
                status=session_data['status'],
                expires_at=session_data['expires_at'],
                created_at=session_data['created_at'],
                updated_at=session_data['updated_at']
            )
        except Exception as e:
            logger.error(f"查询会话失败: {str(e)}")
            return None

    @staticmethod
    def update_session_page(session_id: str, page: int) -> bool:
        """更新会话当前页码"""
        sql = """
        UPDATE mcp_query_sessions 
        SET current_page = %s, updated_at = NOW()
        WHERE session_id = %s AND status = 1
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (page, session_id))
            connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            connection.close()
            
            if affected_rows > 0:
                logger.info(f"更新会话页码成功: {session_id} -> 第{page}页")
                return True
            else:
                logger.warning(f"更新会话页码失败，会话不存在或已过期: {session_id}")
                return False
        except Exception as e:
            logger.error(f"更新会话页码失败: {str(e)}")
            return False

    @staticmethod
    def extend_session_expiry(session_id: str, hours: int = 24) -> bool:
        """延长会话有效期"""
        sql = """
        UPDATE mcp_query_sessions 
        SET expires_at = DATE_ADD(NOW(), INTERVAL %s HOUR), updated_at = NOW()
        WHERE session_id = %s AND status = 1
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (hours, session_id))
            connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            connection.close()
            
            if affected_rows > 0:
                logger.info(f"延长会话有效期成功: {session_id} -> {hours}小时")
                return True
            else:
                logger.warning(f"延长会话有效期失败，会话不存在: {session_id}")
                return False
        except Exception as e:
            logger.error(f"延长会话有效期失败: {str(e)}")
            return False

    @staticmethod
    def cleanup_expired_sessions() -> int:
        """清理过期会话"""
        sql = """
        UPDATE mcp_query_sessions 
        SET status = 0, updated_at = NOW()
        WHERE expires_at <= NOW() AND status = 1
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql)
            connection.commit()
            affected_rows = cursor.rowcount
            cursor.close()
            connection.close()
            
            if affected_rows > 0:
                logger.info(f"清理过期会话成功，共清理 {affected_rows} 个会话")
            return affected_rows
        except Exception as e:
            logger.error(f"清理过期会话失败: {str(e)}")
            return 0

    @staticmethod
    def get_session_info(session_id: str) -> Optional[SessionInfo]:
        """获取会话基本信息"""
        sql = """
        SELECT session_id, case_type, original_query, total_count, 
               current_page, page_size, expires_at, status
        FROM mcp_query_sessions
        WHERE session_id = %s AND status = 1
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor(dictionary=True)
            cursor.execute(sql, (session_id,))
            result = cursor.fetchall()
            cursor.close()
            connection.close()

            if not result:
                return None
            
            session_data = result[0]
            return SessionInfo(
                session_id=session_data['session_id'],
                case_type=session_data['case_type'],
                original_query=session_data['original_query'],
                total_count=session_data['total_count'],
                current_page=session_data['current_page'],
                page_size=session_data['page_size'],
                expires_at=session_data['expires_at'],
                status=session_data['status']
            )
        except Exception as e:
            logger.error(f"获取会话信息失败: {str(e)}")
            return None
