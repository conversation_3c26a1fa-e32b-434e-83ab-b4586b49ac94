# -*- coding: utf-8 -*-
"""
分页查询工具函数
"""
from fastmcp.server.dependencies import get_http_headers
from mcp_permissions.database import PermissionDatabase
from utils.load_env import logger


def get_current_user_principal_id() -> str:
    """
    获取当前用户的principal_id
    
    Returns:
        str: 用户的principal_id，如果获取失败返回None
    """
    try:
        # 使用FastMCP推荐的方式获取HTTP请求头
        headers = get_http_headers()
        
        if not headers:
            return None
            
        # 从请求头获取X-Principal (支持大小写不敏感)
        x_principal = headers.get("X-Principal") or headers.get("x-principal")
        
        if not x_principal:
            return None
            
        return x_principal
        
    except Exception as e:
        logger.error(f"获取用户principal_id失败: {str(e)}")
        return None


def get_current_user_permission():
    """
    获取当前用户的权限信息
    
    Returns:
        UserPermission: 用户权限对象，如果获取失败返回None
    """
    try:
        principal_id = get_current_user_principal_id()
        if not principal_id:
            return None
            
        return PermissionDatabase.get_user_permission(principal_id)
        
    except Exception as e:
        logger.error(f"获取用户权限信息失败: {str(e)}")
        return None
