"""logstash定义类"""
import ast
import inspect
import logging
import os
from datetime import datetime

import logstash
from colorlog import ColoredFormatter
from log.custom_logstash_formatter import CustomLogstashFormatter

logstash_config = {
    'host': os.getenv('KIBANA_HOST'),
    'port': os.getenv('KIBANA_PORT'),
    'tags': ast.literal_eval(os.getenv('KIBANA_TAGS')),
    'version': int(os.getenv('KIBANA_VERSION'))
}

LogDict = {'INFO': logging.INFO}


class Logger:
    """日志类"""

    def __init__(self, logger_name):
        self.logger_level = 'DEBUG'  # 默认日志级别，小于这个级别不打印日志
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(self.logger_level)

        _config = logstash_config
        logstash_handler = logstash.TCPLogstashHandler(**_config)
        logstash_handler.setLevel(self.logger_level)
        # 使用自定义 formatter 避免 datetime 警告
        custom_formatter = CustomLogstashFormatter(
            message_type='python-logstash',
            tags=_config.get('tags', []),
            fqdn=False
        )
        logstash_handler.setFormatter(custom_formatter)

        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.logger_level)
        # 创建一个带颜色的格式化器
        if os.getenv('OPEN_CONSOLE_COLOR') == 'Y':
            formatter = ColoredFormatter(
                "%(log_color)s%(levelname)-8s%(reset)s %(blue)s%(message)s",
                datefmt=None,
                reset=True,
                log_colors={
                    'DEBUG': 'white',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                },
                secondary_log_colors={},
                style='%'
            )
        else:
            formatter = logging.Formatter(
                "%(levelname)-8s %(message)s",
                datefmt=None,
                style='%'
            )
        console_handler.setFormatter(formatter)

        self.logger.addHandler(logstash_handler)
        self.logger.addHandler(console_handler)

        self._extra = {'program': os.getenv('KIBANA_PROGRAM')}

    @staticmethod
    def __format_msg(msg) -> str:
        # 获取当前时间
        now = datetime.now()
        formatted_time = now.strftime("%Y-%m-%d %H:%M:%S") + f".{now.microsecond // 1000:03d}"
        # 获取调用者的帧信息
        stack = inspect.stack()
        caller_frame = stack[2]
        caller_filename = caller_frame.filename
        caller_method_name = caller_frame.function
        caller_line_no = caller_frame.lineno
        # 格式化日志信息
        caller_method_name_str = 'module' if caller_method_name == '<module>' else f'[{caller_method_name}]'
        return f"{formatted_time} [{caller_filename}]:{caller_method_name_str}:[{caller_line_no}] - {msg}"

    def debug(self, msg: str):
        """封装logger.debug方法
        :param msg:消息体
        :return:
        """
        self.logger.debug(self.__format_msg(msg), extra=self._extra)

    def info(self, msg: str):
        """封装logger.info方法
        :param msg:消息体
        :return:
        """
        self.logger.info(self.__format_msg(msg), extra=self._extra)

    def warning(self, msg: str):
        """封装logger.warning方法
        :param msg:消息体
        :return:
        """
        self.logger.warning(self.__format_msg(msg), extra=self._extra)

    def error(self, msg: str):
        """封装logger.error方法
        :param msg:消息体
        :return:
        """
        self.logger.error(self.__format_msg(msg), extra=self._extra)
