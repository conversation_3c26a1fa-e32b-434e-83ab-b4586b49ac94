"""自定义 logstash formatter 解决 datetime 警告"""
import datetime
import json
import socket
import sys
import traceback
from logstash.formatter import LogstashFormatterBase


class CustomLogstashFormatter(LogstashFormatterBase):
    """自定义 Logstash formatter，使用 timezone-aware datetime"""

    def __init__(self, message_type='logstash', tags=None, fqdn=False):
        super().__init__(message_type, tags, fqdn)

    def format(self, record):
        # 使用 timezone-aware datetime 替代 utcfromtimestamp
        if hasattr(record, 'created'):
            # 使用 datetime.fromtimestamp 和 UTC timezone
            tstamp = datetime.datetime.fromtimestamp(record.created, datetime.timezone.utc)
        else:
            tstamp = datetime.datetime.now(datetime.timezone.utc)
        
        # 创建基础消息结构
        message = {
            '@timestamp': tstamp.isoformat(),
            '@version': '1',
            'message': record.getMessage(),
            'host': socket.gethostname(),
            'path': record.pathname,
            'tags': self.tags,
            'type': self.message_type,
            'level': record.levelname,
            'logger_name': record.name,
        }
        
        # 添加额外字段
        if hasattr(record, 'funcName'):
            message['function'] = record.funcName
        if hasattr(record, 'lineno'):
            message['line'] = record.lineno
        if hasattr(record, 'module'):
            message['module'] = record.module
        if hasattr(record, 'process'):
            message['process'] = record.process
        if hasattr(record, 'thread'):
            message['thread'] = record.thread
            
        # 添加异常信息
        if record.exc_info:
            message['exception'] = {
                'class': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'stack_trace': ''.join(traceback.format_exception(*record.exc_info))
            }
            
        # 添加额外的字段
        if hasattr(record, '__dict__'):
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
                              'module', 'exc_info', 'exc_text', 'stack_info', 'lineno', 'funcName',
                              'created', 'msecs', 'relativeCreated', 'thread', 'threadName',
                              'processName', 'process', 'message', 'getMessage']:
                    if isinstance(value, (str, int, float, bool, list, dict)):
                        message[key] = value
                    else:
                        message[key] = str(value)
        
        return json.dumps(message, default=str, ensure_ascii=False).encode('utf-8')
