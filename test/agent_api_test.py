import concurrent.futures
import time

import requests


def get_company_info_test():
    # url = 'http://127.0.0.1:8000/ipo/case'
    url = 'http://************:9898/ipo/case'
    # url = 'http://************:8000/get_company_info'

    data = {
        "user_input": "合合信息的IPO案例",
        "user_id": "CaoJ<PERSON><PERSON>"
    }
    response = requests.post(url, params=data)

    if response.status_code == 200:
        print(response.json())
    else:
        print(f"Error: {response.status_code}, {response.text}")


def run_concurrent_tests(num_requests):
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(get_company_info_test) for _ in range(num_requests)]
        concurrent.futures.wait(futures)


if __name__ == '__main__':
    start_time = time.time()
    get_company_info_test()  # 比如并发10次请求
    print(time.time() - start_time)
