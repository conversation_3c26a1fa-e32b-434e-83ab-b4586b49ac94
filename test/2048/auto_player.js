// 2048高分AI算法 - 融合优化版
class Auto2048Player {
    constructor(game) {
        this.game = game;
        this.isRunning = false;
        this.moveDelay = 300;
        this.maxDepth = 6; // 增加搜索深度到6层
        this.lastBoardState = null;
        this.waitingForUpdate = false;
        this.fastMode = false; // 快速模式，禁用动画
        this.originalAnimationSettings = null; // 保存原始动画设置

        // 高级优化参数
        this.transpositionTable = new Map(); // 转置表缓存
        this.maxCacheSize = 100000; // 最大缓存条目数
        this.probabilityThreshold = 0.0001; // 概率剪枝阈值

        // 权重矩阵 - 角落策略
        this.weightMatrix = [
            [65536, 32768, 16384, 8192],
            [512,   1024,  2048,  4096],
            [256,   128,   64,    32],
            [16,    8,     4,     2]
        ];
    }

    start() {
        if (this.isRunning) return;
        this.isRunning = true;
        this.waitingForUpdate = false;
        this.lastBoardState = null;
        console.log('🤖 AI开始运行！');
        if (this.fastMode) {
            this.enableFastMode();
        }
        this.playNextMove();
    }

    stop() {
        this.isRunning = false;
        if (this.fastMode) {
            this.disableFastMode();
        }
        console.log('⏹️ AI已停止');
    }

    async playNextMove() {
        if (!this.isRunning || this.game.gameOver || this.game.victory) {
            this.stop();
            return;
        }

        if (this.isGameAnimating()) {
            setTimeout(() => this.isRunning && this.playNextMove(), 50);
            return;
        }

        if (this.waitingForUpdate && this.lastBoardState) {
            if (this.boardsEqual(this.game.board, this.lastBoardState)) {
                setTimeout(() => this.isRunning && this.playNextMove(), 50);
                return;
            } else {
                this.waitingForUpdate = false;
                this.lastBoardState = null;
            }
        }

        const bestMove = this.getBestMove();
        if (bestMove) {
            const result = this.simulateMove(this.game.board, bestMove);
            if (!result.changed) {
                setTimeout(() => this.isRunning && this.playNextMove(), this.moveDelay);
                return;
            }

            this.lastBoardState = this.copyBoard(this.game.board);
            this.waitingForUpdate = true;
            console.log(`🎯 ${bestMove}`);
            this.game.handleMove(bestMove);

            setTimeout(() => this.isRunning && this.playNextMove(), this.moveDelay);
        } else {
            this.stop();
        }
    }

    getBestMove() {
        const moves = ['left', 'down', 'right', 'up'];
        let bestMove = null;
        let bestScore = -Infinity;

        // 清理转置表缓存
        if (this.transpositionTable.size > this.maxCacheSize) {
            this.transpositionTable.clear();
        }

        // 并行评估所有可能的移动
        const moveScores = [];
        for (const move of moves) {
            const result = this.simulateMove(this.game.board, move);
            if (result.changed) {
                const score = this.expectimaxWithCache(result.board, this.maxDepth - 1, false, 1.0);
                moveScores.push({ move, score });
                if (score > bestScore) {
                    bestScore = score;
                    bestMove = move;
                }
            }
        }

        // 输出调试信息
        if (moveScores.length > 0) {
            const sortedMoves = moveScores.sort((a, b) => b.score - a.score);
            console.log(`🎯 移动评分: ${sortedMoves.map(m => `${m.move}:${m.score.toFixed(0)}`).join(', ')}`);
        }

        return bestMove;
    }

    // 带缓存和概率剪枝的expectimax算法
    expectimaxWithCache(board, depth, isPlayerTurn, probability = 1.0) {
        // 概率剪枝 - 如果概率太小，直接返回评估值
        if (probability < this.probabilityThreshold) {
            return this.evaluateBoard(board);
        }

        if (depth === 0) return this.evaluateBoard(board);

        // 生成棋盘哈希用于缓存
        const boardHash = this.getBoardHash(board, depth, isPlayerTurn);
        if (this.transpositionTable.has(boardHash)) {
            return this.transpositionTable.get(boardHash);
        }

        let result;
        if (isPlayerTurn) {
            let maxScore = -Infinity;
            for (const move of ['up', 'down', 'left', 'right']) {
                const moveResult = this.simulateMove(board, move);
                if (moveResult.changed) {
                    const score = this.expectimaxWithCache(moveResult.board, depth - 1, false, probability);
                    maxScore = Math.max(maxScore, score);
                }
            }
            result = maxScore === -Infinity ? this.evaluateBoard(board) : maxScore;
        } else {
            const emptyCells = this.getEmptyCells(board);
            if (emptyCells.length === 0) {
                result = this.evaluateBoard(board);
            } else {
                // 动态采样 - 根据深度和空格数量调整采样数量
                const maxSamples = Math.max(4, Math.min(emptyCells.length, 12 - depth));
                const sampleCells = emptyCells.length > maxSamples ?
                    this.selectBestEmptyCells(board, emptyCells, maxSamples) : emptyCells;

                let expectedScore = 0;
                const pEmpty = 1 / sampleCells.length;

                for (const [row, col] of sampleCells) {
                    for (const [value, prob] of [[2, 0.9], [4, 0.1]]) {
                        board[row][col] = value;
                        const newProbability = probability * prob * pEmpty;
                        expectedScore += prob * pEmpty * this.expectimaxWithCache(board, depth - 1, true, newProbability);
                        board[row][col] = 0;
                    }
                }
                result = expectedScore;
            }
        }

        // 缓存结果
        this.transpositionTable.set(boardHash, result);
        return result;
    }

    evaluateBoard(board) {
        let score = 0;
        const emptyCells = this.getEmptyCells(board).length;
        const log2 = v => v ? Math.log2(v) : 0;

        // 1. 权重矩阵评分 (角落策略) - 权重最高
        let weightScore = 0;
        for (let r = 0; r < 4; r++) {
            for (let c = 0; c < 4; c++) {
                weightScore += board[r][c] * this.weightMatrix[r][c];
            }
        }
        score += weightScore * 0.1;

        // 2. 空格权重 - 非常重要
        score += emptyCells * emptyCells * 47.0;

        // 3. 最大值位置奖励 - 鼓励最大值在角落
        const maxValue = Math.max(...board.flat());
        let maxInCorner = false;
        const corners = [[0,0], [0,3], [3,0], [3,3]];
        for (const [r, c] of corners) {
            if (board[r][c] === maxValue) {
                maxInCorner = true;
                break;
            }
        }
        if (maxInCorner) score += maxValue * 0.5;

        // 4. 单调性 - 改进的计算方法
        const mono = this.calculateMonotonicity(board);
        score += mono * 47.0;

        // 5. 平滑度 - 相邻瓦片的相似性
        const smooth = this.calculateSmoothness(board);
        score += smooth * 11.0;

        // 6. 合并潜力 - 可以合并的瓦片对数量
        const merges = this.calculateMergePotential(board);
        score += merges * 700.0;

        // 7. 边缘惩罚 - 避免大数字在边缘中间
        const edgePenalty = this.calculateEdgePenalty(board);
        score -= edgePenalty * 25.0;

        return score;
    }



    simulateMove(board, direction) {
        const size = 4;
        const rotate = (b, times) => {
            let res = b.map(r => r.slice());
            for (let i = 0; i < times; i++) {
                res = res[0].map((_, c) => res.map(r => r[c])).reverse();
            }
            return res;
        };

        const rotations = { left: 0, up: 1, right: 2, down: 3 };
        const rc = rotations[direction];
        let rb = rotate(board, rc);

        for (let r = 0; r < size; r++) {
            let row = rb[r].filter(v => v);
            for (let c = 0; c < row.length - 1; c++) {
                if (row[c] === row[c + 1]) {
                    row[c] *= 2;
                    row.splice(c + 1, 1);
                }
            }
            while (row.length < size) row.push(0);
            rb[r] = row;
        }

        const final = rotate(rb, (4 - rc) % 4);
        return {
            board: final,
            changed: !this.boardsEqual(board, final)
        };
    }



    // 计算单调性 - 数字排列的有序性
    calculateMonotonicity(board) {
        const log2 = v => v ? Math.log2(v) : 0;
        let mono = 0;

        // 水平单调性
        for (let r = 0; r < 4; r++) {
            let inc = 0, dec = 0;
            for (let c = 0; c < 3; c++) {
                const curr = log2(board[r][c]);
                const next = log2(board[r][c + 1]);
                if (curr > next) dec += curr - next;
                else if (next > curr) inc += next - curr;
            }
            mono += Math.max(inc, dec);
        }

        // 垂直单调性
        for (let c = 0; c < 4; c++) {
            let inc = 0, dec = 0;
            for (let r = 0; r < 3; r++) {
                const curr = log2(board[r][c]);
                const next = log2(board[r + 1][c]);
                if (curr > next) dec += curr - next;
                else if (next > curr) inc += next - curr;
            }
            mono += Math.max(inc, dec);
        }

        return mono;
    }

    // 计算平滑度 - 相邻瓦片的相似性
    calculateSmoothness(board) {
        const log2 = v => v ? Math.log2(v) : 0;
        let smoothness = 0;

        for (let r = 0; r < 4; r++) {
            for (let c = 0; c < 4; c++) {
                const value = log2(board[r][c]);
                if (value === 0) continue;

                // 检查右边
                if (c < 3) {
                    const rightValue = log2(board[r][c + 1]);
                    if (rightValue > 0) {
                        smoothness -= Math.abs(value - rightValue);
                    }
                }

                // 检查下面
                if (r < 3) {
                    const downValue = log2(board[r + 1][c]);
                    if (downValue > 0) {
                        smoothness -= Math.abs(value - downValue);
                    }
                }
            }
        }

        return smoothness;
    }

    // 计算合并潜力 - 可以合并的瓦片对数量
    calculateMergePotential(board) {
        let merges = 0;

        for (let r = 0; r < 4; r++) {
            for (let c = 0; c < 4; c++) {
                const value = board[r][c];
                if (value === 0) continue;

                // 检查右边
                if (c < 3 && board[r][c + 1] === value) merges++;

                // 检查下面
                if (r < 3 && board[r + 1][c] === value) merges++;
            }
        }

        return merges;
    }

    // 计算边缘惩罚 - 避免大数字在边缘中间
    calculateEdgePenalty(board) {
        let penalty = 0;
        const log2 = v => v ? Math.log2(v) : 0;

        // 检查边缘中间位置
        const edgeMiddlePositions = [
            [0, 1], [0, 2], // 上边缘中间
            [3, 1], [3, 2], // 下边缘中间
            [1, 0], [2, 0], // 左边缘中间
            [1, 3], [2, 3]  // 右边缘中间
        ];

        for (const [r, c] of edgeMiddlePositions) {
            const value = board[r][c];
            if (value > 0) {
                penalty += log2(value) * log2(value);
            }
        }

        return penalty;
    }

    copyBoard(board) { return board.map(row => [...row]); }

    boardsEqual(board1, board2) {
        return board1.every((row, i) => row.every((val, j) => val === board2[i][j]));
    }

    getEmptyCells(board) {
        const empty = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (board[row][col] === 0) empty.push([row, col]);
            }
        }
        return empty;
    }

    // 智能选择最佳空格位置进行采样
    selectBestEmptyCells(board, emptyCells, maxSamples) {
        if (emptyCells.length <= maxSamples) return emptyCells;

        // 根据位置权重对空格进行评分
        const scoredCells = emptyCells.map(([r, c]) => {
            let score = this.weightMatrix[r][c];

            // 考虑相邻瓦片的影响
            const neighbors = [
                [r-1, c], [r+1, c], [r, c-1], [r, c+1]
            ];

            for (const [nr, nc] of neighbors) {
                if (nr >= 0 && nr < 4 && nc >= 0 && nc < 4 && board[nr][nc] > 0) {
                    score += Math.log2(board[nr][nc]) * 10;
                }
            }

            return { pos: [r, c], score };
        });

        // 选择评分最高的位置
        return scoredCells
            .sort((a, b) => b.score - a.score)
            .slice(0, maxSamples)
            .map(item => item.pos);
    }

    // 生成棋盘哈希用于转置表
    getBoardHash(board, depth, isPlayerTurn) {
        let hash = '';
        for (let r = 0; r < 4; r++) {
            for (let c = 0; c < 4; c++) {
                hash += board[r][c].toString(36) + ',';
            }
        }
        hash += depth + ',' + (isPlayerTurn ? '1' : '0');
        return hash;
    }

    isGameAnimating() {
        if (this.fastMode) return false; // 快速模式下不等待动画
        return this.game.isAnimating ||
               (this.game.mergeAnimations && this.game.mergeAnimations.length > 0) ||
               (this.game.newTileAnimations && this.game.newTileAnimations.length > 0) ||
               (this.game.moveAnimations && this.game.moveAnimations.length > 0) ||
               (this.game.particles && this.game.particles.length > 0);
    }

    // 启用快速模式 - 禁用动画
    enableFastMode() {
        if (!this.game || this.originalAnimationSettings) return;

        // 保存原始设置
        this.originalAnimationSettings = {
            detectAnimations: this.game.detectAnimations,
            startAnimations: this.game.startAnimations,
            createMergeParticles: this.game.createMergeParticles
        };

        // 禁用动画检测
        this.game.detectAnimations = function() {
            this.mergeAnimations = [];
            this.newTileAnimations = [];
            this.moveAnimations = [];
        };

        // 禁用动画启动
        this.game.startAnimations = function() {
            this.isAnimating = false;
            this.render(); // 直接渲染，不播放动画
        };

        // 禁用粒子效果
        this.game.createMergeParticles = function() {
            // 不创建粒子
        };

        console.log('🚀 快速模式已启用 - 动画已禁用');
    }

    // 禁用快速模式 - 恢复动画
    disableFastMode() {
        if (!this.game || !this.originalAnimationSettings) return;

        // 恢复原始设置
        this.game.detectAnimations = this.originalAnimationSettings.detectAnimations;
        this.game.startAnimations = this.originalAnimationSettings.startAnimations;
        this.game.createMergeParticles = this.originalAnimationSettings.createMergeParticles;

        this.originalAnimationSettings = null;
        console.log('🎨 快速模式已禁用 - 动画已恢复');
    }

    // 切换快速模式
    toggleFastMode() {
        this.fastMode = !this.fastMode;
        if (this.isRunning) {
            if (this.fastMode) {
                this.enableFastMode();
            } else {
                this.disableFastMode();
            }
        }
        console.log(`${this.fastMode ? '🚀 快速模式已启用' : '🎨 快速模式已禁用'}`);
        return this.fastMode;
    }
}

// 使用方法：
// 创建控制面板
function createControlPanel() {
    if (document.getElementById('ai-control-panel')) return;

    const panel = document.createElement('div');
    panel.id = 'ai-control-panel';
    panel.innerHTML = `
        <div id="ai-header" style="padding:8px; background:#bbada0; color:#f9f6f2; cursor:move; font-weight:bold; text-align:center; border-radius:6px 6px 0 0;">
            2048 AI控制台
        </div>
        <div style="padding:10px; display:flex; flex-direction:column; gap:8px;">
            <button id="ai-start-btn" style="padding:8px; cursor:pointer; background:#8f7a66; color:#f9f6f2; border:none; border-radius:3px;">开始AI</button>
            <button id="ai-stop-btn" style="padding:8px; cursor:pointer; background:#8f7a66; color:#f9f6f2; border:none; border-radius:3px;">停止AI</button>
            <button id="ai-restart-btn" style="padding:8px; cursor:pointer; background:#8f7a66; color:#f9f6f2; border:none; border-radius:3px;">重新开始</button>
            <button id="ai-fast-mode-btn" style="padding:8px; cursor:pointer; background:#e67e22; color:#f9f6f2; border:none; border-radius:3px;">🚀 快速模式</button>
            <div style="display:flex; justify-content:space-between; align-items:center;">
                <button id="ai-slower-btn" style="padding:4px 8px; cursor:pointer; background:#8f7a66; color:#f9f6f2; border:none; border-radius:3px;">慢</button>
                <span id="ai-speed-display" style="font-size:12px; color:#776e65;"></span>
                <button id="ai-faster-btn" style="padding:4px 8px; cursor:pointer; background:#8f7a66; color:#f9f6f2; border:none; border-radius:3px;">快</button>
            </div>
            <div style="font-size:10px; color:#776e65; text-align:center; padding:2px;">深度:8层 | 缓存:${Math.floor(this.transpositionTable?.size || 0 / 1000)}K</div>
            <div id="ai-status" style="font-size:12px; color:#776e65; text-align:center; padding:4px;">就绪</div>
        </div>`;

    Object.assign(panel.style, {
        position: 'fixed', top: '20px', right: '20px', width: '180px',
        background: '#faf8ef', border: '2px solid #bbada0',
        borderRadius: '8px', zIndex: 10000,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        fontFamily: 'Clear Sans, Helvetica, Arial, sans-serif'
    });

    document.body.appendChild(panel);

    // 拖拽功能
    const header = document.getElementById('ai-header');
    let dragging = false, offsetX = 0, offsetY = 0;

    header.addEventListener('mousedown', e => {
        dragging = true;
        offsetX = e.clientX - panel.offsetLeft;
        offsetY = e.clientY - panel.offsetTop;
        panel.style.userSelect = 'none';
    });

    document.addEventListener('mousemove', e => {
        if (dragging) {
            panel.style.left = `${e.clientX - offsetX}px`;
            panel.style.top = `${e.clientY - offsetY}px`;
        }
    });

    document.addEventListener('mouseup', () => {
        dragging = false;
        panel.style.userSelect = 'auto';
    });
}

// 更新控制面板状态
function updateControlPanel(ai) {
    const startBtn = document.getElementById('ai-start-btn');
    const stopBtn = document.getElementById('ai-stop-btn');
    const fastModeBtn = document.getElementById('ai-fast-mode-btn');
    const speedDisplay = document.getElementById('ai-speed-display');
    const status = document.getElementById('ai-status');

    if (startBtn && stopBtn) {
        startBtn.disabled = ai.isRunning;
        stopBtn.disabled = !ai.isRunning;
        startBtn.style.opacity = ai.isRunning ? 0.6 : 1;
        stopBtn.style.opacity = !ai.isRunning ? 0.6 : 1;
    }

    if (fastModeBtn) {
        fastModeBtn.textContent = ai.fastMode ? '🎨 普通模式' : '🚀 快速模式';
        fastModeBtn.style.background = ai.fastMode ? '#27ae60' : '#e67e22';
    }

    if (speedDisplay) {
        speedDisplay.textContent = `${ai.moveDelay}ms`;
    }

    if (status) {
        const modeText = ai.fastMode ? ' (快速)' : '';
        status.textContent = ai.isRunning ? `运行中...${modeText}` : '已停止';
    }
}

// 全局AI实例
let globalAI = null;

// 初始化控制面板和AI
function initializeAI() {
    createControlPanel();

    // 等待游戏加载
    setTimeout(() => {
        if (window.canvasGame) {
            globalAI = new Auto2048Player(window.canvasGame);

            // 绑定按钮事件
            document.getElementById('ai-start-btn').addEventListener('click', () => {
                globalAI.start();
                updateControlPanel(globalAI);
            });

            document.getElementById('ai-stop-btn').addEventListener('click', () => {
                globalAI.stop();
                updateControlPanel(globalAI);
            });

            document.getElementById('ai-restart-btn').addEventListener('click', () => {
                if (globalAI.game && globalAI.game.newGame) {
                    globalAI.stop();
                    globalAI.game.newGame();
                    setTimeout(() => {
                        globalAI.start();
                        updateControlPanel(globalAI);
                    }, 500);
                }
            });

            document.getElementById('ai-fast-mode-btn').addEventListener('click', () => {
                globalAI.toggleFastMode();
                updateControlPanel(globalAI);
            });

            document.getElementById('ai-slower-btn').addEventListener('click', () => {
                globalAI.moveDelay += 50;
                updateControlPanel(globalAI);
            });

            document.getElementById('ai-faster-btn').addEventListener('click', () => {
                if (globalAI.moveDelay > 50) {
                    globalAI.moveDelay -= 50;
                }
                updateControlPanel(globalAI);
            });

            updateControlPanel(globalAI);
            console.log('🎮 2048超强AI已就绪！使用右上角控制面板操作');
        } else {
            console.log('⚠️ 未找到游戏实例，请刷新页面重试');
        }
    }, 1000);
}

// 自动初始化
initializeAI();

// 便捷的全局函数，可在浏览器控制台直接调用
window.aiStart = () => globalAI && globalAI.start();
window.aiStop = () => globalAI && globalAI.stop();
window.aiToggleFast = () => globalAI && globalAI.toggleFastMode();
window.aiSetSpeed = (delay) => {
    if (globalAI) {
        globalAI.moveDelay = Math.max(50, delay);
        updateControlPanel(globalAI);
        console.log(`AI速度设置为: ${globalAI.moveDelay}ms`);
    }
};

console.log('🎮 2048终极AI算法已加载！(20万分挑战版)');
console.log('🚀 新特性：');
console.log('   • 6层深度搜索 + 转置表缓存');
console.log('   • 概率剪枝 + 智能采样优化');
console.log('   • 角落权重矩阵 + 7维评估函数');
console.log('   • 单调性、平滑度、合并潜力分析');
console.log('🎯 目标：冲击20万分，稳定达到32768瓦片！');
console.log('');
console.log('💡 浏览器控制台快捷命令：');
console.log('   aiStart() - 开始AI');
console.log('   aiStop() - 停止AI');
console.log('   aiToggleFast() - 切换快速模式');
console.log('   aiSetSpeed(100) - 设置AI速度(毫秒)');
