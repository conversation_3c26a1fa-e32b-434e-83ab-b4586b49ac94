"""
FastMCP权限过滤中间件

提供基于用户权限的工具过滤功能，确保用户只能访问其有权限的工具。
"""

from fastmcp.server.middleware import Middleware, MiddlewareContext
from fastmcp.server.dependencies import get_http_headers
from fastmcp.exceptions import ToolError
from utils.load_env import logger
from .database import PermissionDatabase


# 工具权限映射表 - 简化版本，直接使用业务域名
TOOL_PERMISSION_MAP = {
    "query_violation_cases_tool": "violation",
    "query_ipo_cases_tool": "ipo",
    "query_allotment_cases_tool": "allotment",
    "query_asset_restructuring_cases_tool": "asset",
    "query_target_asset_cases_tool": "target_asset",
    "query_nonpublic_offering_cases_tool": "nonpublic",
    "query_convertible_bond_cases_tool": "convertible",
    # 分页查询工具 - 任何有查询权限的用户都可以使用
    "paginate_query_results_tool": "*",  # 特殊标记，表示需要动态权限验证
    "get_query_session_info_tool": "*",  # 特殊标记，表示需要动态权限验证
}


def filter_tools_by_user_permissions(user_scopes: list[str]) -> list[str]:
    """
    根据用户权限范围过滤可用工具

    Args:
        user_scopes: 用户权限范围列表，如 ["violation", "ipo", "allotment"]

    Returns:
        list[str]: 用户可访问的工具名称列表
    """
    available_tools = []
    user_scope_set = set(user_scopes)

    # 查询权限集合
    query_permissions = {"violation", "ipo", "allotment", "asset", "target_asset", "nonpublic", "convertible"}
    has_any_query_permission = any(perm in user_scope_set for perm in query_permissions)

    for tool_name, required_permission in TOOL_PERMISSION_MAP.items():
        if required_permission == "*":
            # 分页查询工具需要用户至少有一个查询权限
            if has_any_query_permission:
                available_tools.append(tool_name)
        elif required_permission in user_scope_set:
            available_tools.append(tool_name)

    return available_tools


class PermissionFilterMiddleware(Middleware):
    """FastMCP权限过滤中间件
    
    功能：
    1. 在工具列表请求时，根据用户权限过滤可见工具
    2. 在工具调用时，验证用户是否有权限调用该工具
    3. 记录权限使用日志
    """

    def _get_user_permission_from_context(self, context: MiddlewareContext):
        """从上下文获取用户权限信息"""
        try:
            # 使用FastMCP推荐的方式获取HTTP请求头
            headers = get_http_headers()

            # 打印headers
            logger.info(f"请求头: {headers}")

            if not headers:
                return None, "用户身份验证失败"

            # 从请求头获取X-Principal (支持大小写不敏感)
            # 注意：这是内部网关设置的用户标识，不暴露给用户
            x_principal = headers.get("X-Principal") or headers.get("x-principal")

            if not x_principal:
                return None, "用户身份验证失败"

            # 获取用户权限
            user_permission = PermissionDatabase.get_user_permission(x_principal)

            if not user_permission:
                return None, "用户权限验证失败，请联系管理员"

            return user_permission, None

        except Exception as e:
            return None, "系统权限验证异常，请稍后重试"

    async def on_list_tools(self, context: MiddlewareContext, call_next):
        """处理工具列表请求，根据用户权限过滤工具"""
        try:
            user_permission, error = self._get_user_permission_from_context(context)

            if not user_permission:
                logger.warning(f"权限过滤中间件（工具列表）：{error}")
                return []

            # 获取原始工具列表
            result = await call_next(context)

            # 根据用户权限过滤工具
            available_tool_names = filter_tools_by_user_permissions(user_permission.scopes)

            # result是工具列表，直接过滤
            filtered_tools = [
                tool for tool in result
                if tool.name in available_tool_names
            ]

            logger.info(f"用户 {user_permission.principal_id} ({user_permission.user_name}) 可访问 {len(filtered_tools)} 个工具: {[tool.name for tool in filtered_tools]}")

            # 返回过滤后的工具列表（不是ListToolsResult对象）
            return filtered_tools

        except Exception as e:
            logger.error(f"权限过滤中间件（工具列表）错误: {str(e)}")
            # 出错时返回空工具列表，确保安全
            return []

    async def on_call_tool(self, context: MiddlewareContext, call_next):
        """处理工具调用请求，验证用户权限"""
        try:
            user_permission, error = self._get_user_permission_from_context(context)

            if not user_permission:
                logger.warning(f"权限过滤中间件（工具调用）：{error}")
                # 记录失败的权限使用日志
                PermissionDatabase.log_permission_usage(
                    "unknown",
                    context.message.name,
                    False,
                    f"权限验证失败: {error}"
                )
                raise ToolError(error)  # 直接使用友好的错误信息

            # 检查用户是否有权限调用该工具
            tool_name = context.message.name
            required_permission = TOOL_PERMISSION_MAP.get(tool_name)

            if not required_permission:
                logger.error(f"未知工具: {tool_name}")
                # 记录失败的权限使用日志
                PermissionDatabase.log_permission_usage(
                    user_permission.principal_id,
                    tool_name,
                    False,
                    f"未知工具: {tool_name}"
                )
                raise ToolError("请求的功能不存在，请检查后重试")

            # 对于分页查询工具，进行动态权限验证
            if required_permission == "*":
                # 分页查询工具需要用户至少有一个查询权限
                query_permissions = {"violation", "ipo", "allotment", "asset", "target_asset", "nonpublic", "convertible"}
                if not any(perm in user_permission.scopes for perm in query_permissions):
                    logger.warning(f"用户 {user_permission.principal_id}({user_permission.user_name}) 无任何查询权限，无法使用分页功能")
                    PermissionDatabase.log_permission_usage(
                        user_permission.principal_id,
                        tool_name,
                        False,
                        "无任何查询权限，无法使用分页功能"
                    )
                    raise ToolError(f"用户 {user_permission.user_name} 没有权限使用此功能，请联系管理员")
            elif required_permission not in user_permission.scopes:
                # 日志中记录详细信息（包含内部ID）
                logger.warning(f"用户 {user_permission.principal_id}({user_permission.user_name}) 无权限调用工具 {tool_name}，需要权限: {required_permission}")
                # 记录失败的权限使用日志
                PermissionDatabase.log_permission_usage(
                    user_permission.principal_id,
                    tool_name,
                    False,
                    f"权限不足，需要权限: {required_permission}"
                )
                # 用户友好的错误提示（不暴露内部权限名称）
                raise ToolError(f"用户 {user_permission.user_name} 没有权限使用此功能，请联系管理员")

            logger.info(f"用户 {user_permission.principal_id} 权限验证通过，调用工具: {tool_name}")

            # 权限验证通过，继续执行工具调用
            result = await call_next(context)
            
            # 记录成功的权限使用日志
            PermissionDatabase.log_permission_usage(
                user_permission.principal_id,
                tool_name,
                True,
                f"成功调用工具 {tool_name}"
            )
            
            return result

        except ToolError:
            # ToolError直接重新抛出
            raise
        except Exception as e:
            logger.error(f"权限过滤中间件（工具调用）错误: {str(e)}")
            # 记录失败的权限使用日志
            PermissionDatabase.log_permission_usage(
                user_permission.principal_id if 'user_permission' in locals() else "unknown",
                context.message.name,
                False,
                f"中间件执行错误: {str(e)}"
            )
            raise ToolError("系统权限验证异常，请稍后重试")
