# -*- coding: utf-8 -*-
"""
权限管理初始化数据
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from utils.load_env import logger
from mcp_permissions.database import PermissionDatabase



# 初始用户权限配置 - 简化版本，移除冗余的 :read 后缀和未使用的 rate_limit
INITIAL_USERS = [
    {
        "principal_id": "user_001",
        "user_name": "基础用户A",
        "scopes": ["violation", "ipo"],
        "description": "基础用户，只能查询违规和IPO案例"
    },
    {
        "principal_id": "user_002",
        "user_name": "高级用户B",
        "scopes": ["violation", "ipo", "allotment", "asset"],
        "description": "高级用户，可以查询多种案例"
    },
    {
        "principal_id": "user_003",
        "user_name": "专业用户C",
        "scopes": ["violation", "ipo", "allotment", "asset", "target_asset"],
        "description": "专业用户，可以查询大部分案例"
    },
    {
        "principal_id": "user_004",
        "user_name": "全功能用户D",
        "scopes": ["violation", "ipo", "allotment", "asset",
                  "target_asset", "nonpublic", "convertible"],
        "description": "全功能用户，可以查询所有案例"
    },
    {
        "principal_id": "user_005",
        "user_name": "临时用户E",
        "scopes": ["violation"],
        "valid_until": datetime.now() + timedelta(days=30),
        "description": "临时用户，30天有效期，只能查询违规案例"
    },
    {
        "principal_id": "user_006",
        "user_name": "测试用户F",
        "scopes": ["ipo", "allotment"],
        "description": "测试用户，可以查询IPO和配股案例"
    },
    {
        "principal_id": "user_007",
        "user_name": "分析师G",
        "scopes": ["violation", "ipo", "asset", "nonpublic"],
        "description": "分析师用户，可以查询分析相关案例"
    },
    {
        "principal_id": "user_008",
        "user_name": "研究员H",
        "scopes": ["allotment", "convertible", "target_asset"],
        "description": "研究员用户，专注特定领域案例"
    },
    {
        "principal_id": "user_009",
        "user_name": "管理员I",
        "scopes": ["violation", "ipo", "allotment", "asset",
                  "target_asset", "nonpublic", "convertible", "admin"],
        "description": "管理员用户，有查询和管理权限"
    },
    {
        "principal_id": "admin_001",
        "user_name": "超级管理员",
        "scopes": ["violation", "ipo", "allotment", "asset",
                  "target_asset", "nonpublic", "convertible", "admin"],
        "description": "超级管理员，拥有所有权限"
    }
]


def init_user_permissions():
    """初始化用户权限数据"""
    logger.info("开始初始化用户权限数据...")
    
    success_count = 0
    error_count = 0
    
    for user_config in INITIAL_USERS:
        try:
            # 检查用户是否已存在
            existing_user = PermissionDatabase.get_user_permission(user_config["principal_id"])
            if existing_user:
                logger.info(f"用户 {user_config['principal_id']} 已存在，跳过创建")
                continue
            
            # 创建用户权限
            success = PermissionDatabase.create_user_permission(
                principal_id=user_config["principal_id"],
                user_name=user_config["user_name"],
                scopes=user_config["scopes"],
                valid_until=user_config.get("valid_until")
            )
            
            if success:
                success_count += 1
                logger.info(f"创建用户权限成功: {user_config['principal_id']} - {user_config['user_name']}")
            else:
                error_count += 1
                logger.error(f"创建用户权限失败: {user_config['principal_id']}")
                
        except Exception as e:
            error_count += 1
            logger.error(f"创建用户权限异常: {user_config['principal_id']}, 错误: {str(e)}")
    
    logger.info(f"用户权限初始化完成，成功: {success_count}, 失败: {error_count}")
    return success_count, error_count


def reset_user_permissions():
    """重置用户权限数据（谨慎使用）"""
    logger.warning("开始重置用户权限数据...")
    
    # 获取所有现有用户
    existing_users = PermissionDatabase.list_user_permissions()
    
    # 删除所有现有用户
    for user in existing_users:
        PermissionDatabase.delete_user_permission(user.principal_id)
        logger.info(f"删除用户权限: {user.principal_id}")
    
    # 重新初始化
    return init_user_permissions()


def check_user_permissions():
    """检查用户权限配置"""
    logger.info("检查用户权限配置...")
    
    users = PermissionDatabase.list_user_permissions(status=1)
    
    print("\n=== 当前用户权限配置 ===")
    for user in users:
        print(f"用户ID: {user.principal_id}")
        print(f"用户名: {user.user_name}")
        print(f"权限范围: {', '.join(user.scopes)}")
        print(f"状态: {'启用' if user.status == 1 else '禁用'}")
        if user.valid_until:
            print(f"有效期至: {user.valid_until}")
        print("-" * 50)
    
    print(f"总计: {len(users)} 个启用用户")
    return users


if __name__ == "__main__":
    # 可以直接运行此文件来初始化数据
    init_user_permissions()
    check_user_permissions()
