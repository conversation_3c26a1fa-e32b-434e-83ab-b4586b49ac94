# -*- coding: utf-8 -*-
"""
权限管理数据库操作
"""
import json
from datetime import datetime
from typing import List, Optional
from utils.load_env import logger
from utils.db_tool import get_db_connection
from .models import UserPermission, PermissionLog


class PermissionDatabase:
    """权限管理数据库操作类"""

    @staticmethod
    def get_user_permission(principal_id: str) -> Optional[UserPermission]:
        """根据principal_id获取用户权限"""
        sql = """
        SELECT principal_id, user_name, scopes, status,
               valid_from, valid_until, created_at, updated_at
        FROM mcp_user_permissions
        WHERE principal_id = %s AND status = 1
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor(dictionary=True)
            cursor.execute(sql, (principal_id,))
            result = cursor.fetchall()
            cursor.close()
            connection.close()

            if not result:
                return None
            
            user_data = result[0]
            
            # 解析权限范围
            try:
                scopes = json.loads(user_data['scopes']) if isinstance(user_data['scopes'], str) else user_data['scopes']
            except json.JSONDecodeError:
                logger.error(f"解析用户 {principal_id} 的权限范围失败: {user_data['scopes']}")
                scopes = []
            
            return UserPermission(
                principal_id=user_data['principal_id'],
                user_name=user_data['user_name'],
                scopes=scopes,
                status=user_data['status'],
                valid_from=user_data['valid_from'],
                valid_until=user_data['valid_until'],
                created_at=user_data['created_at'],
                updated_at=user_data['updated_at']
            )
        except Exception as e:
            logger.error(f"查询用户权限失败: {str(e)}")
            return None

    @staticmethod
    def create_user_permission(principal_id: str, user_name: str, scopes: List[str],
                             valid_until: Optional[datetime] = None) -> bool:
        """创建用户权限"""
        sql = """
        INSERT INTO mcp_user_permissions (principal_id, user_name, scopes, valid_until)
        VALUES (%s, %s, %s, %s)
        """

        try:
            scopes_json = json.dumps(scopes, ensure_ascii=False)

            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (principal_id, user_name, scopes_json, valid_until))
            connection.commit()
            cursor.close()
            connection.close()

            logger.info(f"创建用户权限成功: {principal_id}")
            return True
        except Exception as e:
            logger.error(f"创建用户权限失败: {str(e)}")
            return False

    @staticmethod
    def update_user_permission(principal_id: str, **kwargs) -> bool:
        """更新用户权限"""
        update_fields = []
        params = []
        
        for field, value in kwargs.items():
            if value is not None:
                if field == 'scopes':
                    update_fields.append(f"{field} = %s")
                    params.append(json.dumps(value, ensure_ascii=False))
                else:
                    update_fields.append(f"{field} = %s")
                    params.append(value)
        
        if not update_fields:
            return True
        
        params.append(principal_id)
        sql = f"""
        UPDATE mcp_user_permissions 
        SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
        WHERE principal_id = %s
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, params)
            connection.commit()
            cursor.close()
            connection.close()

            logger.info(f"更新用户权限成功: {principal_id}")
            return True
        except Exception as e:
            logger.error(f"更新用户权限失败: {str(e)}")
            return False

    @staticmethod
    def delete_user_permission(principal_id: str) -> bool:
        """删除用户权限（软删除）"""
        sql = """
        UPDATE mcp_user_permissions 
        SET status = 0, updated_at = CURRENT_TIMESTAMP
        WHERE principal_id = %s
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (principal_id,))
            connection.commit()
            cursor.close()
            connection.close()

            logger.info(f"删除用户权限成功: {principal_id}")
            return True
        except Exception as e:
            logger.error(f"删除用户权限失败: {str(e)}")
            return False

    @staticmethod
    def list_user_permissions(status: Optional[int] = None) -> List[UserPermission]:
        """获取所有用户权限列表"""
        if status is not None:
            sql = """
            SELECT principal_id, user_name, scopes, status,
                   valid_from, valid_until, created_at, updated_at
            FROM mcp_user_permissions
            WHERE status = %s
            ORDER BY created_at DESC
            """
            params = (status,)
        else:
            sql = """
            SELECT principal_id, user_name, scopes, status,
                   valid_from, valid_until, created_at, updated_at
            FROM mcp_user_permissions
            ORDER BY created_at DESC
            """
            params = ()
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor(dictionary=True)
            cursor.execute(sql, params)
            results = cursor.fetchall()
            cursor.close()
            connection.close()

            users = []
            
            for user_data in results:
                try:
                    scopes = json.loads(user_data['scopes']) if isinstance(user_data['scopes'], str) else user_data['scopes']
                except json.JSONDecodeError:
                    scopes = []
                
                users.append(UserPermission(
                    principal_id=user_data['principal_id'],
                    user_name=user_data['user_name'],
                    scopes=scopes,
                    status=user_data['status'],
                    valid_from=user_data['valid_from'],
                    valid_until=user_data['valid_until'],
                    created_at=user_data['created_at'],
                    updated_at=user_data['updated_at']
                ))
            
            return users
        except Exception as e:
            logger.error(f"查询用户权限列表失败: {str(e)}")
            return []

    @staticmethod
    def log_permission_usage(principal_id: str, tool_name: str, success: bool, error_msg: Optional[str] = None) -> bool:
        """记录权限使用日志"""
        sql = """
        INSERT INTO mcp_permission_logs (principal_id, tool_name, success, error_msg)
        VALUES (%s, %s, %s, %s)
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor()
            cursor.execute(sql, (principal_id, tool_name, 1 if success else 0, error_msg))
            connection.commit()
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            logger.error(f"记录权限使用日志失败: {str(e)}")
            return False

    @staticmethod
    def get_permission_logs(principal_id: Optional[str] = None, tool_name: Optional[str] = None, 
                          limit: int = 100) -> List[PermissionLog]:
        """获取权限使用日志"""
        conditions = []
        params = []
        
        if principal_id:
            conditions.append("principal_id = %s")
            params.append(principal_id)
        
        if tool_name:
            conditions.append("tool_name = %s")
            params.append(tool_name)
        
        where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
        params.append(limit)
        
        sql = f"""
        SELECT principal_id, tool_name, request_time, success, error_msg
        FROM mcp_permission_logs
        {where_clause}
        ORDER BY request_time DESC
        LIMIT %s
        """
        
        try:
            connection = get_db_connection('AGENT')
            cursor = connection.cursor(dictionary=True)
            cursor.execute(sql, params)
            results = cursor.fetchall()
            cursor.close()
            connection.close()

            logs = []
            
            for log_data in results:
                logs.append(PermissionLog(
                    principal_id=log_data['principal_id'],
                    tool_name=log_data['tool_name'],
                    request_time=log_data['request_time'],
                    success=bool(log_data['success']),
                    error_msg=log_data['error_msg']
                ))
            
            return logs
        except Exception as e:
            logger.error(f"查询权限使用日志失败: {str(e)}")
            return []
