# -*- coding: utf-8 -*-
"""
权限管理数据模型
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel


class UserPermission(BaseModel):
    """用户权限模型"""
    principal_id: str
    user_name: str
    scopes: List[str]
    status: int = 1
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class PermissionLog(BaseModel):
    """权限使用日志模型"""
    principal_id: str
    tool_name: str
    request_time: datetime
    success: bool
    error_msg: Optional[str] = None

    class Config:
        from_attributes = True


class CreateUserRequest(BaseModel):
    """创建用户权限请求模型"""
    principal_id: str
    user_name: str
    scopes: List[str]
    valid_until: Optional[datetime] = None


class UpdateUserRequest(BaseModel):
    """更新用户权限请求模型"""
    user_name: Optional[str] = None
    scopes: Optional[List[str]] = None
    status: Optional[int] = None
    valid_until: Optional[datetime] = None
