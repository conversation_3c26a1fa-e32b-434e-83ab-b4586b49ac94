# MCP权限管理系统

基于X-Principal请求头的FastMCP权限管理，不同用户看到和调用不同的工具。

## 核心功能

- **工具列表过滤**：用户只能看到有权限的工具
- **工具调用验证**：执行时再次验证权限
- **身份隐私保护**：对外使用随机session_id，不暴露principal_id

## 权限配置

### 工具权限映射（api/mcp_api.py）
```python
TOOL_PERMISSION_MAP = {
    "query_violation_cases_tool": ["violation:read"],
    "query_ipo_cases_tool": ["ipo:read"],
    "query_allotment_cases_tool": ["allotment:read"],
    "query_asset_restructuring_cases_tool": ["asset:read"],
    "query_target_asset_cases_tool": ["target_asset:read"],
    "query_nonpublic_offering_cases_tool": ["nonpublic:read"],
    "query_convertible_bond_cases_tool": ["convertible:read"],
}
```

## 快速开始

### 1. 初始化用户权限
```bash
python mcp_permissions/init_data.py
```

### 2. 添加新工具权限
在 `api/mcp_api.py` 的 `TOOL_PERMISSION_MAP` 中添加配置即可。

## 预定义用户

初始化后会创建10个测试用户：user_001到user_009、admin_001，权限各不相同。

## 测试

```bash
# 测试user_001（只有violation:read, ipo:read权限）
curl -X POST "http://localhost:8000/mcp-server/mcp/tools" \
  -H "X-Principal: user_001" \
  -H "Content-Type: application/json"

# 只会返回2个工具：query_violation_cases_tool, query_ipo_cases_tool
```
