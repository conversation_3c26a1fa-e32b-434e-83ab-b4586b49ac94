import asyncio
import traceback
import uuid
from datetime import datetime
from typing import Literal, Callable, TypedDict, Annotated, Optional

from langchain.schema.runnable import Runnable, RunnableConfig
from langchain_core.messages import ToolMessage, AnyMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph, add_messages
from langgraph.prebuilt import tools_condition

from utils.load_env import logger

logger.info("数据浏览器AGENT")
from common_agent.agents import ToIPOAssistant, ToViolationAssistant, main_assistant_runnable
from sub_agents.ipo_data_search.ipo_agent import ipo_assistant_runnable
from sub_agents.graph_utils import CompleteOrEscalate, create_tool_node_with_fallback, _print_event, filter_messages, \
    update_dialog_stack
from sub_agents.ipo_data_search.case_utils import get_company_code
from sub_agents.ipo_data_search.ipo import ipo_tools
from sub_agents.sql_database_chain.violation_data_search.violation_tools import violation_tools

from utils.redis_tool import remove_redis_info


class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    login_company: Optional[str]
    dialog_state: Annotated[
        list[Literal["main_assistant", "ipo", "violation"]],
        update_dialog_stack,
    ]


class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable

    async def __call__(self, state: State, config: RunnableConfig):
        """
        agent执行方法
        """
        while True:
            configuration = config.get("configurable", {})
            # 对历史消息进行管理
            state["messages"] = filter_messages(state["messages"])
            state = {
                **state,
                "login_company": configuration.get("login_company"),
                "time": datetime.now(),
            }
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(state)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                state["messages"].append(("user", "给出一个实际的输出。"))
            else:
                break
        return {"messages": result}


def create_entry_node(assistant_name: str, new_dialog_state: str) -> Callable:
    """进入子agent前，创建一个节点,用于将对话状态更改为新状态,并通知助手。"""

    def entry_node(state: State) -> dict:
        tool_call_id = state["messages"][-1].tool_calls[0]["id"]
        company_code = get_company_code(state["messages"][-1].tool_calls[0]['args']['request'])['result']
        return {
            "messages": [
                ToolMessage(
                    content=f"助手现在是{assistant_name}。请回顾上面 main_assistant 和用户之间的对话。"
                            f"用户的意图尚未得到满足。请使用提供的工具来协助用户。请记住,你是{assistant_name},"
                            "在成功调用适当的工具之前,任何操作都不算完成。"
                            "请不要提及你的身份 - 只需作为助手的代理执行操作。"
                            + (f"用户问题涉及到的证券代码：{company_code}" if company_code else ""),
                    tool_call_id=tool_call_id,
                )
            ],
            "dialog_state": new_dialog_state,
        }

    return entry_node


# 这个节点将用于所有专门助手的退出
def pop_dialog_state(state: State) -> dict:
    """子助手返回上一级时 弹出对话栈并返回到主助手。
    这允许完整的图显式跟踪对话流程,并将控制权委托给特定的子图。
    """
    messages = []
    if state["messages"][-1].tool_calls:
        # 注意:目前不处理LM执行并行工具调用的边缘情况
        messages.append(
            ToolMessage(
                content="正在恢复与主机助手的对话。请回顾过去的对话,并根据需要为用户提供帮助。",
                tool_call_id=state["messages"][-1].tool_calls[0]["id"],
            )
        )
    return {
        "dialog_state": "pop",
        "messages": messages,
    }


# 定义路由函数
def route_main_assistant(state: State
                         ) -> Literal["enter_ipo", "enter_violation", "main_assistant_tools", "__end__"]:
    """
    判断从主agent要走向哪里
    :param state:
    :return: 分配任务给agent，调用主agent绑定的其他工具，结束
    """
    route = tools_condition(state)
    if route == END:
        return END
    tool_calls = state["messages"][-1].tool_calls
    if tool_calls:
        if tool_calls[0]["name"] == ToIPOAssistant.__name__:
            return "enter_ipo"
        elif tool_calls[0]["name"] == ToViolationAssistant.__name__:
            return "enter_violation"
        return "main_assistant_tools"
    raise ValueError("Invalid route")


def route_ipo_assistant(state: State) -> Literal["ipo_tools", "main_assistant", "leave_skill", "__end__"]:
    """
    判断从ipo助手要走向哪里
    :return: 调用ipo助手绑定的工具
             返回主助手 leave_skill
             返回主助手 main_assistant(兜底，和leave_skill最终效果一样，leave_skill是IPO的agent选的）
             结束
    """
    route = tools_condition(state)
    if route == END:
        return "__end__"
    # 检查最后一条消息是否包含工具调用
    if "messages" in state and state["messages"] and hasattr(state["messages"][-1], "tool_calls"):
        tool_calls = state["messages"][-1].tool_calls

        # 检查是否使用了 CompleteOrEscalate
        did_cancel = any(tc["name"] == CompleteOrEscalate.__name__ for tc in tool_calls)
        if did_cancel:
            return "leave_skill"

        # 检查使用的工具是否都在 ipo_tools 中
        tool_names = [t.name for t in ipo_tools]
        if all(tc["name"] in tool_names for tc in tool_calls):
            return "ipo_tools"
    # 如果没有工具调用或使用了未知工具，返回到主助手
    return "main_assistant"


def route_violation_assistant(state: State) -> Literal["violation_tools", "main_assistant", "leave_skill", "__end__"]:
    """
    判断从违规案例助手要走向哪里
    :return: 调用违规案例助手绑定的工具，
             返回主助手 leave_skill
             返回主助手 main_assistant(兜底，和leave_skill最终效果一样，leave_skill是违规案例的agent选的）
             结束
    """
    route = tools_condition(state)
    if route == END:
        return "__end__"
    if "messages" in state and state["messages"] and hasattr(state["messages"][-1], "tool_calls"):
        tool_calls = state["messages"][-1].tool_calls
        did_cancel = any(tc["name"] == CompleteOrEscalate.__name__ for tc in tool_calls)
        if did_cancel:
            return "leave_skill"
        tool_names = [t.name for t in violation_tools]
        if all(tc["name"] in tool_names for tc in tool_calls):
            return "violation_tools"
    return "main_assistant"


# 定义状态图
builder = StateGraph(State)
# 添加主助手的节点和边
builder.add_node("main_assistant", Assistant(main_assistant_runnable))
builder.add_edge(START, "main_assistant")

builder.add_node("leave_skill", pop_dialog_state)
builder.add_edge("leave_skill", "main_assistant")

# 特定助手
assistants = {
    "ipo": (ipo_assistant_runnable, ipo_tools),
    # "violation": (violation_assistant_runnable, violation_tools),
}

for name, (runnable, tools) in assistants.items():
    assistant_name = f"{name}_assistant"
    builder.add_node(assistant_name, Assistant(runnable))
    builder.add_node(f"{name}_tools", create_tool_node_with_fallback(tools))
    builder.add_node(f"enter_{name}", create_entry_node(f"{name.capitalize()}Assistant", name))
    builder.add_edge(f"enter_{name}", assistant_name)
    builder.add_edge(f"{name}_tools", assistant_name)
    builder.add_conditional_edges(f"{name}_assistant", globals()[f"route_{name}_assistant"])

builder.add_conditional_edges(
    "main_assistant",
    route_main_assistant,
    {
        "enter_ipo": "enter_ipo",
        "enter_violation": "enter_violation",
        END: END,
    },
)

# 配置state保存在内存里，可以选sqlite,mongo,redis
memory = MemorySaver()
# 编译图
case_agent_graph = builder.compile(checkpointer=memory)


async def main():
    config = {
        "recursion_limit": 20,
        "configurable": {
            "login_company": "301551",
            "thread_id": "6487c4d7e4b0e9a5f4f7c4d7"
        },

    }

    _printed = set()
    while True:
        user_input = input("User: ")
        if user_input.lower() in ["quit", "exit", "q"]:
            print("Goodbye!")
            break
        try:
            async for event in case_agent_graph.astream(
                    {"messages": ("user", user_input)}, config, stream_mode="values", debug=True
            ):
                _print_event(event, _printed)
        except Exception as e:
            print(f"An error occurred: {e}")


# ==================图形界面相关beg==================
import gradio as gr


async def view_main(user_input):
    config = {
        "recursion_limit": 10,
        "configurable": {
            "login_company": "301551",
            "thread_id": str(uuid.uuid4())
        },
    }
    try:
        answer = await case_agent_graph.ainvoke(
            {"messages": ("user", user_input)},
            config,
            stream_mode="values", debug=True
        )
        answer = answer["messages"][-1].content
        return answer
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{e}"


def chat(message, history):
    bot_message = asyncio.run(view_main(message))
    return bot_message


# 创建Gradio接口
iface = gr.ChatInterface(
    chat,
    chatbot=gr.Chatbot(elem_id="chatbot", render=False),
    textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
    title="案例库助手对话Demo",
    description="这是一个使用Gradio创建的案例库助手对话界面",
    theme="soft",
    cache_examples=True,
    css="""
    #chatbot {
        height: calc(100vh - 280px) !important;
        overflow-y: auto;
    }
    """
)
# ==================图形界面相关end==================

enable_view = False

if __name__ == '__main__':
    remove_redis_info('CASE_SEARCH_CODE_MAP')
    remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')

    if enable_view:
        iface.launch(server_name="0.0.0.0", server_port=7861, debug=True)
    else:
        asyncio.run(main())
