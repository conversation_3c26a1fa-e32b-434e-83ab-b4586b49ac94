import asyncio
import json
import uuid
import weakref
from fastmcp import FastMCP
from fastmcp.exceptions import ToolError
from langchain_core.messages import AIMessageChunk
from utils.email_utils import send_email
from utils.load_env import logger

# 导入权限管理模块
from mcp_permissions import PermissionFilterMiddleware

# 导入分页查询模块
from mcp_pagination import SessionManager
from mcp_pagination.utils import get_current_user_principal_id

# 导入所有 langgraph 模块
from sub_agents.sql_database_chain.violation_data_search.violation_langgraph import violation_langgraph
from sub_agents.sql_database_chain.ipo_data_search.ipo_langgraph import ipo_langgraph
from sub_agents.sql_database_chain.allotment_data_search.allotment_langgraph import allotment_langgraph
from sub_agents.sql_database_chain.asset_restructuring_data_search.asset_restructuring_langgraph import asset_restructuring_langgraph
from sub_agents.sql_database_chain.target_asset_data_search.target_asset_langgraph import target_asset_langgraph
from sub_agents.sql_database_chain.nonpublic_offering_data_search.nonpublic_offering_langgraph import nonpublic_offering_langgraph
from sub_agents.sql_database_chain.convertible_bond_data_search.convertible_bond_langgraph import convertible_bond_langgraph

# 创建一个FastMCP服务器
mcp = FastMCP("MCP服务")

# 用户锁字典，确保同一用户的请求串行处理
# 使用WeakValueDictionary防止内存泄漏，当锁不再被引用时会自动清理
user_locks = weakref.WeakValueDictionary()


# 案例类型映射
CASE_TYPE_MAP = {
    "违规案例": "violation",
    "IPO案例": "ipo",
    "配股发行案例": "allotment",
    "资产重组案例": "asset",
    "重组标的案例": "target_asset",
    "非公开发行案例": "nonpublic",
    "可转债发行案例": "convertible"
}

# 通用的查询工具函数
async def _query_cases_generic(user_input: str, langgraph_instance, case_type_name: str):
    """
    通用的案例查询函数

    Args:
        user_input: 用户输入
        langgraph_instance: 对应的 langgraph 实例
        case_type_name: 案例类型名称，用于日志记录

    Returns:
        dict: 查询结果
    """
    try:
        # 始终生成随机的会话ID，保护用户隐私
        user_id = str(uuid.uuid4())

        # 获取当前用户的principal_id
        principal_id = get_current_user_principal_id()

        logger.info(f"[{case_type_name}查询]收到消息：{user_input} from {user_id}")

        # 获取或创建用户锁，确保同一用户的请求是串行处理的
        # 使用WeakValueDictionary安全的方式获取锁
        try:
            user_lock = user_locks[user_id]
        except KeyError:
            # 如果锁不存在，创建新锁
            user_lock = asyncio.Lock()
            user_locks[user_id] = user_lock

        # 获取用户编号的锁，确保同一用户的请求是串行处理的
        async with user_lock:
            config = {
                "recursion_limit": 15,
                "configurable": {
                    "thread_id": user_id
                }
            }

            # 收集流式返回的消息
            summary_content = ""
            custom_messages = []

            # 调用对应的处理逻辑，使用流模式
            async for stream_mode, chunk in langgraph_instance.astream(
                    {"messages": ("user", user_input), "query_result_num": 0}, config,
                    stream_mode=["messages", "custom"], debug=False):

                if stream_mode == "messages":
                    msg, metadata = chunk
                    # 只处理有效的消息
                    if isinstance(msg, AIMessageChunk):
                        if msg.content and (not metadata["langgraph_node"].endswith("tools")):
                            summary_content += msg.content

                elif stream_mode == "custom":
                    # 收集自定义消息
                    if isinstance(chunk, str):
                        try:
                            custom_data = json.loads(chunk)
                            custom_messages.append(custom_data)
                        except json.JSONDecodeError:
                            custom_messages.append({"raw_message": chunk})
                    else:
                        custom_messages.append(chunk)

            # 提取AI_AGENT_RESULT
            ai_agent_result = ""
            for msg in custom_messages:
                if isinstance(msg, dict) and "QUERY_RESULT" in msg:
                    ai_agent_result = msg["QUERY_RESULT"]
                    break

            # 尝试创建查询会话（用于分页功能）
            session_id = None
            try:
                # 从custom_messages中提取会话数据
                request_body, request_url, agent_result_data = SessionManager.extract_session_data_from_custom_messages(custom_messages)

                if request_body and request_url and agent_result_data:
                    case_type = CASE_TYPE_MAP.get(case_type_name, case_type_name.lower())
                    session_id = SessionManager.create_session(
                        user_id=user_id,
                        principal_id=principal_id,
                        case_type=case_type,
                        original_query=user_input,
                        request_body=request_body,
                        request_url=request_url,
                        ai_agent_result=agent_result_data
                    )
                    if session_id:
                        logger.info(f"创建查询会话成功: {session_id}")
                    else:
                        logger.warning(f"创建查询会话失败，但不影响正常查询")
                else:
                    logger.info(f"未找到完整的会话数据，跳过会话创建")
            except Exception as e:
                logger.error(f"创建查询会话异常: {str(e)}，但不影响正常查询")

            # 组合最终结果
            result = {
                "summary": summary_content,
                "ai_agent_result": ai_agent_result,
                "user_input": user_input,
                "user_id": user_id
            }

            # 如果成功创建会话，添加session_id到结果中
            if session_id:
                result["session_id"] = session_id

            # 记录回复日志，但不暴露principal_id
            logger.info(f"[{case_type_name}查询]回复消息长度：{len(summary_content)} to {user_input} of {user_id}")
            return result

    except Exception as e:
        error_msg = f"{case_type_name}查询工具执行失败: {str(e)}"
        logger.error(error_msg)

        # 使用FastMCP专门的异常类型
        raise ToolError(error_msg)


# 添加MCP API工具
# @mcp.tool()
# def send_email_tool(to_email: str, subject: str = "测试邮件", body: str = "这是一封测试邮件"):
#     """发送邮件工具
#
#     Args:
#         to_email: 收件人邮箱地址
#         subject: 邮件主题
#         body: 邮件正文内容
#
#     Returns:
#         发送结果信息
#     """
#     try:
#         send_email(to_email=to_email, subject=subject, body=body)
#         logger.info(f"邮件发送成功: 收件人={to_email}, 主题={subject}")
#         return {"status": "success", "message": f"邮件已成功发送至 {to_email}"}
#     except Exception as e:
#         error_msg = f"邮件发送失败: {str(e)}"
#         logger.error(error_msg)
#         return {"status": "error", "message": error_msg}


@mcp.tool()
async def query_violation_cases_tool(user_input: str):
    """查询违规案例数据的智能工具

    这是一个专门用于查询违规案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的违规案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的违规案例
    - 了解某类违规行为的案例
    - 分析违规处罚情况
    - 获取违规案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的违规案例"、"信息披露违规的案例有哪些"、
                         "最近的内幕交易处罚案例"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询上市公司信息披露违规的案例"
        输出: 包含相关违规案例的详细信息、处罚情况、案例分析等
    """
    return await _query_cases_generic(user_input, violation_langgraph, "违规案例")


@mcp.tool()
async def query_ipo_cases_tool(user_input: str):
    """查询IPO案例数据的智能工具

    这是一个专门用于查询IPO案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的IPO案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的IPO案例
    - 了解IPO发行情况
    - 分析IPO定价策略
    - 获取IPO案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的IPO案例"、"科创板IPO案例有哪些"、
                         "最近的IPO发行情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询科创板IPO案例"
        输出: 包含相关IPO案例的详细信息、发行情况、定价分析等
    """
    return await _query_cases_generic(user_input, ipo_langgraph, "IPO案例")


@mcp.tool()
async def query_allotment_cases_tool(user_input: str):
    """查询配股发行案例数据的智能工具

    这是一个专门用于查询配股发行案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的配股发行案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的配股案例
    - 了解配股发行方案
    - 分析配股定价策略
    - 获取配股案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的配股案例"、"配股发行方案有哪些"、
                         "最近的配股发行情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询配股发行案例"
        输出: 包含相关配股案例的详细信息、发行方案、定价分析等
    """
    return await _query_cases_generic(user_input, allotment_langgraph, "配股发行案例")


@mcp.tool()
async def query_asset_restructuring_cases_tool(user_input: str):
    """查询资产重组案例数据的智能工具

    这是一个专门用于查询资产重组案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的资产重组案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的资产重组案例
    - 了解重组方案设计
    - 分析重组交易结构
    - 获取重组案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的资产重组案例"、"重大资产重组方案有哪些"、
                         "最近的重组交易情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询重大资产重组案例"
        输出: 包含相关重组案例的详细信息、交易方案、估值分析等
    """
    return await _query_cases_generic(user_input, asset_restructuring_langgraph, "资产重组案例")


@mcp.tool()
async def query_target_asset_cases_tool(user_input: str):
    """查询重组标的案例数据的智能工具

    这是一个专门用于查询重组标的案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的重组标的案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定重组标的案例
    - 了解标的资产情况
    - 分析标的估值方法
    - 获取重组标的的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某标的资产案例"、"重组标的估值方法有哪些"、
                         "最近的标的资产情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询重组标的资产案例"
        输出: 包含相关标的案例的详细信息、资产情况、估值分析等
    """
    return await _query_cases_generic(user_input, target_asset_langgraph, "重组标的案例")


@mcp.tool()
async def query_nonpublic_offering_cases_tool(user_input: str):
    """查询非公开发行案例数据的智能工具

    这是一个专门用于查询非公开发行案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的非公开发行案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的非公开发行案例
    - 了解定增发行方案
    - 分析定增定价策略
    - 获取非公开发行案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的定增案例"、"非公开发行方案有哪些"、
                         "最近的定增发行情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询非公开发行案例"
        输出: 包含相关定增案例的详细信息、发行方案、定价分析等
    """
    return await _query_cases_generic(user_input, nonpublic_offering_langgraph, "非公开发行案例")


@mcp.tool()
async def query_convertible_bond_cases_tool(user_input: str):
    """查询可转债发行案例数据的智能工具

    这是一个专门用于查询可转债发行案例信息的工具，可以根据用户的自然语言查询，
    智能检索相关的可转债发行案例数据并提供详细的分析结果。

    适用场景：
    - 查询特定公司的可转债案例
    - 了解可转债发行方案
    - 分析转股条款设计
    - 获取可转债案例的详细信息

    Args:
        user_input (str): 用户的查询问题，支持自然语言描述。
                         例如："查询某某公司的可转债案例"、"可转债发行方案有哪些"、
                         "最近的可转债发行情况"等。

    Returns:
        dict: 包含查询结果的字典，结构如下：
            - summary (str): 完整的查询结果和分析内容
            - ai_agent_result (str): AI代理的具体查询结果数据
            - user_input (str): 原始用户输入
            - session_id (str): 系统生成的会话ID

    示例：
        输入: "查询可转债发行案例"
        输出: 包含相关可转债案例的详细信息、发行方案、条款分析等
    """
    return await _query_cases_generic(user_input, convertible_bond_langgraph, "可转债发行案例")


@mcp.tool()
async def paginate_query_results_tool(session_id: str, page: int = 1, page_size: int = 10):
    """分页查询案例数据的工具

    根据之前查询生成的会话ID，获取指定页码的案例数据。

    Args:
        session_id (str): 查询会话ID，来自首次查询结果中的session_id字段
        page (int): 页码，从1开始，默认为1
        page_size (int): 每页条数，默认为10

    Returns:
        dict: 包含分页查询结果的字典，结构如下：
            - summary (str): 查询结果摘要
            - ai_agent_result (dict): 分页数据结果
            - session_id (str): 会话ID
            - page_info (dict): 分页信息

    示例：
        输入: session_id="sess_abc123", page=2, page_size=10
        输出: 第2页的案例数据
    """
    try:
        from mcp_pagination.database import SessionDatabase
        from mcp_pagination.session_manager import SessionManager

        # 验证参数
        if page < 1:
            raise ToolError("页码必须大于0")
        if page_size < 1 or page_size > 100:
            raise ToolError("每页条数必须在1-100之间")

        # 获取会话信息
        session = SessionDatabase.get_session(session_id)
        if not session:
            raise ToolError("查询会话不存在或已过期，请重新查询")

        # 验证用户权限（确保当前用户有权限访问该会话）
        current_principal_id = get_current_user_principal_id()
        if current_principal_id and session.principal_id and current_principal_id != session.principal_id:
            raise ToolError("无权限访问该查询会话")

        # 获取分页数据
        result_data = SessionManager.get_paginated_data(session_id, page, page_size)
        if not result_data:
            raise ToolError("获取分页数据失败，请稍后重试")


        # 构建返回结果
        return {
            "summary": f"第{page}页查询结果，共{result_data['案例总数']}条记录",
            "ai_agent_result": result_data,
            "session_id": session_id,
        }

    except ToolError:
        raise
    except Exception as e:
        error_msg = f"分页查询失败: {str(e)}"
        logger.error(error_msg)
        raise ToolError(error_msg)


@mcp.tool()
async def get_query_session_info_tool(session_id: str):
    """获取查询会话信息的工具

    根据会话ID获取查询会话的基本信息，包括原始查询、分页状态等。

    Args:
        session_id (str): 查询会话ID

    Returns:
        dict: 包含会话信息的字典，结构如下：
            - session_id (str): 会话ID
            - case_type (str): 案例类型
            - original_query (str): 原始查询
            - total_count (int): 总记录数
            - current_page (int): 当前页码
            - page_size (int): 每页大小
            - expires_at (str): 过期时间
            - status (int): 会话状态

    示例：
        输入: session_id="sess_abc123"
        输出: 会话的详细信息
    """
    try:
        from mcp_pagination.database import SessionDatabase

        # 获取会话信息
        session_info = SessionDatabase.get_session_info(session_id)
        if not session_info:
            raise ToolError("查询会话不存在或已过期")

        # 验证用户权限
        session = SessionDatabase.get_session(session_id)
        if session:
            current_principal_id = get_current_user_principal_id()
            if current_principal_id and session.principal_id and current_principal_id != session.principal_id:
                raise ToolError("无权限访问该查询会话")

        return {
            "session_id": session_info.session_id,
            "case_type": session_info.case_type,
            "original_query": session_info.original_query,
            "total_count": session_info.total_count,
            "current_page": session_info.current_page,
            "page_size": session_info.page_size,
            "expires_at": session_info.expires_at.isoformat(),
            "status": session_info.status,
            "total_pages": (session_info.total_count + session_info.page_size - 1) // session_info.page_size
        }

    except ToolError:
        raise
    except Exception as e:
        error_msg = f"获取会话信息失败: {str(e)}"
        logger.error(error_msg)
        raise ToolError(error_msg)


# 添加权限过滤中间件到FastMCP
mcp.add_middleware(PermissionFilterMiddleware())

# 创建 FastMCP 的 ASGI 应用
mcp_app = mcp.http_app(path='/mcp')
