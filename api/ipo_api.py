import asyncio
import json

from fastapi import WebSocket, APIRouter
from langchain_core.messages import AIMessageChunk

from utils.load_env import logger

logger.info("ipo api init")

from sub_agents.sql_database_chain.ipo_data_search.ipo_langgraph import ipo_langgraph
from utils.websocket_utils import ConnectionManager

router = APIRouter(prefix="/ipo")


@router.websocket("/ws/{v1}")
async def final(websocket: WebSocket, v1: str):
    logger.info(f"session_id#######################################################v1: {v1}")
    manager = ConnectionManager()
    await manager.connect(websocket)

    # 获取用户id
    data = await websocket.receive_text()
    item = json.loads(data)
    user_input = item.get('user_input')
    user_id = item.get('user_id')

    # 检查并获取锁
    if user_id not in user_locks:
        user_locks[user_id] = asyncio.Lock()

    # 获取该用户的锁，确保串行处理
    async with (user_locks[user_id]):
        config = {
            "recursion_limit": 15,
            "configurable": {
                "thread_id": user_id,
                "manager": manager,
                "websocket": websocket
            }
        }

        logger.info(f"final方法开始#######################################################item: {item}")
        try:
            async def message_stream():
                summary_started = False  # 添加标志位，确保只发送一次 "summary": "start"
                try:
                    # 调用 CRM 处理逻辑，使用流模式
                    async for stream_mode, chunk in ipo_langgraph.astream(
                            {"messages": ("user", user_input), "query_result_num": 0}, config,
                            stream_mode=["messages", "custom"], debug=True):
                        if stream_mode == "messages":
                            msg, metadata = chunk
                            # 只处理有效的消息并返回
                            if isinstance(msg, AIMessageChunk):
                                if msg.content and (not metadata["langgraph_node"].endswith("tools")):
                                    if not summary_started:
                                        await manager.send_personal_message(
                                            json.dumps({"AI_AGENT_FLOW": "已为您生成最佳答案。"}, ensure_ascii=False),
                                            websocket)
                                        await manager.send_personal_message(json.dumps({"summary": "start"}), websocket)
                                        summary_started = True

                                    await manager.send_personal_message(
                                        json.dumps({"summary": msg.content}, ensure_ascii=False), websocket)

                        elif stream_mode == "custom":
                            await manager.send_personal_message(chunk, websocket)

                except Exception as e:
                    logger.error(f"Error while processing user input: {str(e)}")
                    await manager.send_personal_message(f"抱歉，处理您的请求时出现了错误：{str(e)}", websocket)

                # 确保发送结束消息
                await manager.send_personal_message(json.dumps({"summary": "stop"}), websocket)
                await manager.send_personal_message(json.dumps({"is_end": "yes"}, ensure_ascii=False),
                                                    websocket)

            # 启动流式返回
            await message_stream()

        except Exception as e:
            logger.error(f"WebSocket 错误：{str(e)}")
            await manager.send_personal_message(
                json.dumps({"error": "stop"}),
                websocket)
            await manager.send_personal_message(
                json.dumps({
                    "final_result": {'status': 'error', 'message': str(e)}
                }, ensure_ascii=False), websocket)
            await manager.send_personal_message(json.dumps({
                "is_end": "yes"
            }, ensure_ascii=False), websocket)

        finally:
            # 断开连接
            await manager.disconnect(websocket)


user_locks = {}


@router.post("/case")
async def ipo(user_input: str, user_id: str):
    logger.info(f"[CASE AGENT]收到消息：{user_input} from {user_id}")

    # 如果该用户没有锁，则为其创建一个锁
    if user_id not in user_locks:
        user_locks[user_id] = asyncio.Lock()

    # 获取用户编号的锁，确保同一用户的请求是串行处理的
    async with user_locks[user_id]:
        config = {
            "recursion_limit": 15,
            "configurable": {
                "thread_id": user_id
            }
        }

        # 调用 CRM 处理逻辑
        answer = await ipo_langgraph.ainvoke({"messages": ("user", user_input)},
                                               config,
                                               stream_mode="values", debug=True)
        answer = answer["messages"][-1].content

        logger.info(f"[CASE AGENT]回复消息：{answer} to {user_input} of {user_id}")
        return answer
