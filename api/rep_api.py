import asyncio
import json
import traceback
from typing import Optional

from fastapi import WebSocket, APIRouter, Query
from langchain_core.messages import AIMessageChunk

from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.enum.ws_msg_enum import Answer, AnswerType, EndMode
from sub_agents.rep.general.rep_gen_agent_builder import rep_builder
from utils.company_utils import confirm_company
from utils.db_tool import MyAIOMySQLSaver, agent_checkpoint_db_uri
from utils.load_env import logger
from utils.websocket_utils import ConnectionManager

router = APIRouter(prefix="/rep")

user_locks = {}
session_locks = {}


@router.websocket("/ws/{session_id}")
async def chat_with_stream(websocket: WebSocket, session_id: str,
                           company_code: Optional[str] = Query(None, alias="companyCode"),
                           person_name: Optional[str] = Query(None, alias="personName"),
                           company_id: Optional[str] = Query(None, alias="companyId"),
                           user_id: Optional[str] = Query(None, alias="userId"), ):
    logger.info(f"chat_with_stream.session_id: {session_id}")
    logger.info(f"chat_with_stream.company_code: {company_code}"
                f"\nperson_name: {person_name}"
                f"\ncompany_id: {company_id}"
                f"\nuser_id: {user_id}")
    manager = ConnectionManager()
    await manager.connect(websocket)
    # 获取用户信息
    data = await websocket.receive_text()
    item = json.loads(data)
    user_input = item.get('user_input')
    system_flag = "YDQuestion" if user_id else "AgentToolsStore"
    user_id = user_id if user_id else item.get('user_id')

    # 检查并获取锁
    if session_id not in session_locks:
        session_locks[session_id] = asyncio.Lock()

    # 获取该用户的锁，确保串行处理
    async with (session_locks[session_id]):
        companies = confirm_company(company_code if company_code else "000001")
        if not companies:
            raise ValueError("未查到此公司信息")
        curr_com_info = companies[0]
        config = {
            "recursion_limit": 20,
            "configurable": {
                "thread_id": session_id,  # thread_id 不可修改
                "user_id": user_id,
                "user_cur_input": user_input,
                "curr_com_info": curr_com_info,
                "manager": manager,
                "websocket": websocket,
                "system_flag": system_flag
            }
        }

        logger.info(f"final方法开始#######################################################item: {item}")
        try:
            async def message_stream():
                summary_started = False  # 添加标志位，确保只发送一次 "start"
                reasoning_block_started = False  # 标记 reasoning_content 代码块是否已开始
                reasoning_block_closed = False  # 标记 reasoning_content 代码块是否已结束
                try:
                    async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
                        rep_gen_agent_graph = rep_builder.compile(checkpointer=checkpointer)
                        async for msg, metadata in rep_gen_agent_graph.astream(
                                {
                                    "messages": ("user", user_input),
                                    "reflector_num": 0  # 反思次数初始化
                                }, config, stream_mode="messages", debug=False):
                            # 只处理有效的消息并返回
                            if isinstance(msg, AIMessageChunk) and (metadata['langgraph_node'] == 'RepSpokesman'):
                                reasoning_chunk = msg.additional_kwargs.get('reasoning_content', '')
                                content_chunk = msg.content or ''
                                output_chunk = ""

                                if reasoning_chunk or content_chunk:
                                    if not summary_started:
                                        await manager.send_personal_message(
                                            json.dumps({RepWSParam.OUTPUT.value: "start"}),
                                            websocket)
                                        summary_started = True

                                    if reasoning_chunk:
                                        if not reasoning_block_started:  # 如果 reasoning_content 代码块还没开始，先开始
                                            if system_flag == "AgentToolsStore":
                                                await manager.send_personal_message(
                                                    json.dumps(
                                                        {RepWSParam.FLOW_PROCESS.value: "深度策略推演"},
                                                        ensure_ascii=False),
                                                    websocket)
                                                output_chunk += "```\n"

                                            reasoning_block_started = True
                                            reasoning_block_closed = False  # 确保在新的 reasoning_content 开始时，closed 标记是 False
                                        output_chunk += reasoning_chunk  # 输出 reasoning_content chunk

                                    if content_chunk:
                                        if reasoning_block_started and not reasoning_block_closed:  # 如果 reasoning_content 代码块已经开始 并且还没结束，现在结束它
                                            if system_flag == "AgentToolsStore":
                                                output_chunk += "```\n"
                                                await manager.send_personal_message(
                                                    json.dumps({RepWSParam.FLOW_PROCESS.value: "最佳答案生成"},
                                                               ensure_ascii=False),
                                                    websocket)
                                            reasoning_block_closed = True  # 标记为已结束
                                        output_chunk += content_chunk  # 输出 content chunk
                                    if system_flag == "AgentToolsStore":
                                        await manager.send_personal_message(
                                            json.dumps({RepWSParam.OUTPUT.value: output_chunk}, ensure_ascii=False),
                                            websocket)
                                    else:
                                        if not reasoning_block_closed:
                                            # 反思
                                            content = {
                                                Answer.TYPE_NAME.value: AnswerType.REASONING.value,
                                                Answer.OUTPUT.value: output_chunk
                                            }
                                        else:
                                            # 回答
                                            content = {
                                                Answer.TYPE_NAME.value: AnswerType.CONTENT.value,
                                                Answer.OUTPUT.value: output_chunk
                                            }
                                        await manager.send_personal_message(json.dumps(content, ensure_ascii=False),
                                                                            websocket)

                            # 检查是否结束
                            if hasattr(msg, 'response_metadata') and msg.response_metadata.get(
                                    'finish_reason') == 'stop' and summary_started:
                                if system_flag == "AgentToolsStore":
                                    await manager.send_personal_message(json.dumps({RepWSParam.OUTPUT.value: "stop"}),
                                                                        websocket)
                                    await manager.send_personal_message(
                                        json.dumps({RepWSParam.END_FLAG.value: "yes"}, ensure_ascii=False),
                                        websocket)
                                else:
                                    await manager.send_personal_message(
                                        json.dumps({EndMode.END.value: True}, ensure_ascii=False),
                                        websocket)


                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"Error while processing user input: {str(e)}")
                    if system_flag == "AgentToolsStore":
                        await manager.send_personal_message(f"抱歉，处理您的请求时出现了错误：{str(e)}", websocket)
                    else:
                        await manager.send_personal_message(
                            json.dumps({EndMode.ERROR.value: True}, ensure_ascii=False), websocket)

            # 启动流式返回
            await message_stream()

        except Exception as e:
            traceback.print_exc()
            logger.error(f"WebSocket 错误：{str(e)}")
            await manager.send_personal_message(
                json.dumps({"error": "stop"}),
                websocket)
            await manager.send_personal_message(
                json.dumps({
                    "final_result": {'status': 'error', 'message': str(e)}
                }, ensure_ascii=False), websocket)
            await manager.send_personal_message(json.dumps({
                RepWSParam.END_FLAG: "yes"
            }, ensure_ascii=False), websocket)

        finally:
            # 断开连接
            await manager.disconnect(websocket)


@router.post("/chat")
async def chat(user_input: str, user_id: str):
    logger.info(f"chat收到消息：{user_input} from {user_id}")

    # 如果该用户没有锁，则为其创建一个锁
    if user_id not in user_locks:
        user_locks[user_id] = asyncio.Lock()

    # 获取用户编号的锁，确保同一用户的请求是串行处理的
    async with user_locks[user_id]:
        companies = confirm_company("000001")
        if not companies:
            raise ValueError("未查到此公司信息")
        curr_com_info = companies[0]
        config = {
            "recursion_limit": 20,
            "configurable": {
                "curr_com_info": curr_com_info,
                "thread_id": user_id,
                "user_cur_input": user_input
            },

        }
        async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
            rep_gen_agent_graph = rep_builder.compile(
                checkpointer=checkpointer,
            )
            answer = await rep_gen_agent_graph.ainvoke({"messages": ("user", user_input)},
                                                       config,
                                                       stream_mode="values", debug=False)
        answer = answer["messages"][-1].content

        logger.info(f"[CASE AGENT]回复消息：{answer} to {user_input} of {user_id}")
        return answer
