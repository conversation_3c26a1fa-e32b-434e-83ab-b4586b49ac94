from datetime import datetime
from typing import TypedDict, Annotated, Optional

from langchain_core.messages import AnyMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.runnables import Runnable, RunnableConfig
from langgraph.graph import add_messages, StateGraph
from langgraph.prebuilt import tools_condition

from sub_agents.sql_database_chain.public_offering_data_search.public_offering_prompt import \
    public_offering_system_prompt
from sub_agents.sql_database_chain.public_offering_data_search.public_offering_tools import \
    public_offering_tools
from sub_agents.sql_database_chain.public_offering_data_search.public_offering_utils import filter_messages, \
    create_tool_node_with_fallback
from utils.llm_utils import get_a_llm


class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    login_company: Optional[str]
    query_result_num: Optional[int]  # 查询结果次数


class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable

    async def __call__(self, state: State, config: RunnableConfig):
        while True:
            configuration = config.get("configurable", {})
            # 对历史消息进行管理
            state["messages"] = filter_messages(state["messages"])
            state = {
                **state,
                "login_company": configuration.get("login_company"),
                "time": datetime.now(),
            }
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                state["messages"].append(("user", "给出一个实际的输出。"))
            else:
                break
        return {"messages": result}


# 获取llm
llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0, parallel_tool_calls=False)
# 构建public_offering_agent_prompt
public_offering_agent_prompt = ChatPromptTemplate.from_messages([
    ("system", public_offering_system_prompt),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))
# 构建"助理"
public_offering_assistant_runnable = public_offering_agent_prompt | llm.bind_tools(public_offering_tools)

# 初始化节点
public_offering_langgraph = StateGraph(State)
public_offering_langgraph.add_node("assistant", Assistant(public_offering_assistant_runnable))
public_offering_langgraph.add_node("tools", create_tool_node_with_fallback(public_offering_tools))

# 初始化边
public_offering_langgraph.set_entry_point("assistant")
public_offering_langgraph.add_edge("tools", "assistant")
public_offering_langgraph.add_conditional_edges(
    "assistant",
    tools_condition,
)

public_offering_langgraph = public_offering_langgraph.compile()

# 生成流程图并保存到文件
# public_offering_langgraph.get_graph(xray=True).draw_mermaid_png(output_file_path="sub_agents/sql_database_chain/public_offering_data_search/public_offering_langgraph.png")
