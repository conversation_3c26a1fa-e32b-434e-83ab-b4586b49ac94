import asyncio
import json
from typing import List, Dict, Any, Union, Optional, Annotated

from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from sub_agents.sql_database_chain.violation_data_search.violation_utils import remove_duplicates_by_param_name, \
    send_processing_feedback, format_filter_conditions, get_violation_type_for_llm, get_penalty_type_for_llm, \
    get_target_list_for_llm, build_request_body, \
    make_post_request, search_violate_collection_data, process_special_fields, vector_case_search, vector_qa_search, \
    manual_processing_filters, extract_violation_keywords_for_llm
from utils.db_tool import get_case_browser_params_from_mysql
from utils.load_env import logger
from utils.my_bing_search import search


@tool
async def fetch_violation_info(input_text: str):
    """从用户输入中提取【违规案例】相关的候选指标名称极其解释。

    参数:
    input_text (str): 用户输入的文本 HumanMessage

    返回:
    str: 指标列表，结果以字符串形式返回。
    """
    logger.info(f"[违规案例] 用户输入：{input_text}")

    # 发送流程消息
    await send_processing_feedback(input_text)
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 获取参数列表
    target_list = get_case_browser_params_from_mysql('7')
    filtered_target_list = [
        {
            **{field: (item.get(field)[:100] + "..." if field == "optional_value" and item.get(field) and len(
                item.get(field)) > 100 else item.get(field))
               for field in ["indicators_name", "data_type", "optional_value", "description", "mapper_fields",
                             "indicators_name_alias"]}
        }
        for item in target_list
    ]

    # 并行执行任务 - 立即开始最耗时的异步任务，让它在后台运行
    llm_target_task = asyncio.create_task(
        get_target_list_for_llm(input_text, str(filtered_target_list))
    )

    # 同步执行向量搜索（因为search_data是同步函数）
    mv_target = vector_case_search(input_text, "违规案例")
    annotated_params = vector_qa_search(input_text)

    logger.debug('[违规案例]向量库选取的指标：' + str(mv_target))

    # 先用关键词提取来改进向量检索
    keywords_result = await extract_violation_keywords_for_llm(input_text)
    violation_keywords = keywords_result.get("violation_keywords", "")
    penalty_keywords = keywords_result.get("penalty_keywords", "")
    industry_keywords = keywords_result.get("industry_keywords", "")

    logger.debug(f'[违规案例]提取的违规类型关键词：{violation_keywords}')
    logger.debug(f'[违规案例]提取的处罚类型关键词：{penalty_keywords}')
    logger.debug(f'[违规案例]提取的行业关键词：{industry_keywords}')

    # 只有提取到关键词时才进行向量搜索
    violation_type = []
    penalty_type = []
    industry_type = []

    if violation_keywords.strip():
        violation_type = vector_case_search(violation_keywords, '违规类型', top_k=10)
        logger.debug('[违规案例]向量库选取的违规类型：' + str(violation_type))

    if penalty_keywords.strip():
        penalty_type = vector_case_search(penalty_keywords, '处罚类型', top_k=5)
        logger.debug('[违规案例]向量库选取的处罚类型：' + str(penalty_type))

    if industry_keywords.strip():
        industry_type = vector_case_search(industry_keywords, '行业', top_k=5)
        logger.debug('[违规案例]向量库选取的行业：' + str(industry_type))

    # 此时才等待最耗时的llm_target任务结果，让它有最大的后台运行时间
    llm_target = await llm_target_task
    logger.info('[违规案例]大模型选取的指标：' + str(llm_target))

    # 合并结果并去重
    combined_results = llm_target + mv_target
    unique_combined_results = remove_duplicates_by_param_name(combined_results)

    # 构建返回结果
    result_dict = {
        "指标列表": unique_combined_results,
        **({"违规类型": violation_type} if violation_type else {}),
        **({"处罚类型": penalty_type} if penalty_type else {}),
        **({"相关行业": industry_type} if industry_type else {}),
        **({"参考入参": annotated_params} if annotated_params else {})
    }
    return json.dumps(result_dict, ensure_ascii=False)


@tool
async def query_violation_data(metrics: List[str], filters: Optional[Dict[str, Union[str, Dict[str, Any]]]] = None,
                               sort_by: Optional[str] = '处罚日期', sort_order: str = 'desc',
                               config: RunnableConfig = None,
                               tool_call_id: Annotated[str, InjectedToolCallId] = None,
                               state: Annotated[dict, InjectedState] = None) -> Command:
    """
    查询违规案例数据的方法，支持多指标和复杂筛选条件，在使用本方法前需要调用fetch_violation_info。

    :param metrics: List[str] 要查询的指标名称列表
    :param filters: Dict[str, Union[str, Dict[str, Any]]] 筛选条件(必填)
        文本类型:  使用字典表示关键词匹配，{"keywords": "空格分隔的关键词", "op": "and/or", }，"and"全包含，"or"或包含。
        数值/DATE类型: 使用字典表示范围 {"equal/gt/lt/gte/lte": 值, "op": "and/or"}
    :param sort_by: Optional[str] 排序字段，默认使用时间/金额类型指标
    :param sort_order: str 排序方式，'asc'升序或'desc'降序，默认'desc'
    :return: 查询结果字典，包含查询到的数据、总数、当前页码等
    示例:
    metrics = ["公司简称", "证券代码", "案例标题"]
    filters = {
        "处罚日期": {"gt": "2024-03-01", "lt": "2023-12-31", "op": "or"},
        "证券代码": {"keywords": "000000 000001", "op": "or"},
        "案例标题": {"keywords": "价值 在线", "op": "and"},
        "罚款金额": {"gt": 10000, "lt": 50000, "op": "and"},
        "违规类型": {'keywords': '上市公司/公司信息披露违规/信息披露不真实', 'op': 'and'},
        "处罚类型": {'keywords': '处罚类型/沪深交易所/自律监管措施', 'op': 'and'}
    }
    result = query_violation_data(metrics, filters, sort_by="处罚日期", sort_order="desc")
    """
    # 检索条件特别处理
    filters = manual_processing_filters(filters)

    # 格式化筛选条件
    formatted_conditions = format_filter_conditions(filters) if filters else ""
    filters_description = f"[{formatted_conditions}]" if formatted_conditions else ""

    # 发送流程消息
    writer = get_stream_writer()
    writer(
        json.dumps({"AI_AGENT_FLOW": f"正在为您精准筛选符合{filters_description}条件的数据..."}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 发送标注消息
    writer(
        json.dumps({
            "AI_AGENT_ORIGINAL_PARAMS": {
                "caseType": "7",
                "metrics": metrics,
                "filters": filters,
                "sort_by": sort_by,
                "sort_order": sort_order
            }
        }, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 获取当前的 query_result_num
    query_result_num = state.get('query_result_num', 0)

    # 将获取到的值加 1
    query_result_num += 1

    # 判断是否发送query_result_num
    if query_result_num > 1:
        writer(json.dumps({"QUERY_RESULT_NUM": query_result_num}, ensure_ascii=False))
        await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 构建请求体(案例序号不能删除 是默认排序字段！)
    metrics.extend(['处罚日期'])

    # 处理处罚对象身份映射 弃用
    # filters = map_punishment_target_identity(filters)

    # 处理违规类型关键词，获取更新后的筛选条件和违规类型questions
    updated_filters, results_by_field = process_special_fields(filters)
    logger.debug(f"Updated filters: {updated_filters}")
    logger.debug(f"Results by field: {results_by_field}")

    # 将results_by_field中的所有key添加到metrics中
    results_by_field_keys = list(results_by_field.keys())
    metrics.extend(results_by_field_keys)

    query_params = {
        "metrics": metrics,
        "filters": updated_filters,
        "sort_by": sort_by,
        "sort_order": sort_order
    }

    body = build_request_body(query_params, case_type='7')

    if not body or 'error' in body:
        return Command(
            update={
                # 更新violation_num
                "query_result_num": query_result_num,
                # 同时更新消息历史
                "messages": [ToolMessage(content=str(body), tool_call_id=tool_call_id)]
            }
        )

    # 处理成分和违规类型
    if results_by_field:
        # 将违规类型路径添加到questions中
        if "违规类型" in results_by_field and results_by_field["违规类型"]:
            if 'questions' not in body:
                body['questions'] = []
            body['questions'].extend(results_by_field["违规类型"])

        # 将除违规类型外的所有其它字段添加到ingredients中
        if 'ingredients' not in body:
            body['ingredients'] = []

        # 遍历所有字段，除了违规类型外都放入ingredients
        for field, values in results_by_field.items():
            if field != "违规类型" and values:
                body['ingredients'].extend(values)

        # 判断成分是合集还是交集
        # 根据不同 mapper 值的数量来判断
        unique_mappers = set(item["mapper"] for item in body['ingredients'])
        if len(unique_mappers) > 1:
            body['logic'] = "AND"
        else:
            body['logic'] = "OR"

    logger.info('[违规案例]构造的请求体：' + str(body))

    result = await make_post_request(body)

    # 如果查询次数为2,并且result中包含error，则尝试使用向量库匹配
    if query_result_num >= 2 and 'error' in result:
        logger.info('[违规案例]触发向量库匹配补救机制')
        user_input = config.get('configurable', {}).get('user_input', '')
        result = search_violate_collection_data(collection_name='violate_collection_demo', query=user_input)

    writer(json.dumps({"QUERY_RESULT": result}, ensure_ascii=False, indent=2))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    return Command(
        update={
            # 更新violation_num
            "query_result_num": query_result_num,
            # 同时更新消息历史
            "messages": [ToolMessage(content=str(result), tool_call_id=tool_call_id)]
        }
    )


@tool
async def bing_search(query: str) -> str:
    """
    提供Bing搜索功能，用于处理用户提出的问题。生成查询内容覆盖更全面的信息。
    在以下情况启用此功能：
    1.当其他工具查询失败或结果不适用时
    2.根据判断，其他工具不适合处理当前问题。
    3.行业分析，概念统计

    Args:
        query: 搜索内容。

    Returns:
        返回Bing搜索的结果
    """
    # 发送流程消息
    writer = get_stream_writer()
    writer(
        json.dumps({"AI_AGENT_FLOW": f"正在网页中查询[{query}]..."}, ensure_ascii=False))
    writer(json.dumps({"DATA_SOURCE": "BING"}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 限制查询的域名,注意结尾的空格
    site = (" site:finance.eastmoney.com OR site:caam.org.cn OR site:stats.gov.cn OR site:cs.com.cn OR site:cls.cn "
            "OR site:csrc.gov.cn OR site:sse.com.cn OR site:szse.cn OR site:bse.cn OR site:pbc.gov.cn OR site:mof.gov.cn "
            "OR site:ndrc.gov.cn OR site:sasac.gov.cn OR site:neeq.com.cn OR site:finance.caixin.com OR site:cfi.cn "
            "OR site:cninfo.com.cn OR site:jjckb.xinhuanet.com OR site:jiemian.com OR site:zqrb.cn "
            "OR site:finance.sina.com.cn OR site:people.com.cn OR site:cnstock.com OR site:news.qq.com "
            "OR site:miit.gov.cn OR site:cnr.cn OR site:gov.cn OR site:financialnews.com.cn OR site:finance.china.com.cn "
            "OR site:gmw.cn OR site:chnfund.com OR site:thepaper.cn OR site:sohu.com")

    query += site

    results = search.results(query, 10)
    writer(
        json.dumps({"AI_AGENT_BING_DATA": results}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    all_results = {
        "query_results": str(results),
    }

    return str(all_results)  # 返回一个包含所有查询结果的字典字符串


violation_tools = [fetch_violation_info, query_violation_data, bing_search]
