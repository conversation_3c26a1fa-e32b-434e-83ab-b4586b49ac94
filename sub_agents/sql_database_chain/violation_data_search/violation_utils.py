import ast
import copy
import json
import os
import time
import traceback
from contextlib import contextmanager
from typing import Optional, Dict, Union, Any, List, Tuple

import requests
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_core.runnables import RunnableLambda, RunnablePassthrough, RunnableConfig
from langchain_openai import ChatOpenAI
from langgraph.config import get_stream_writer
from langgraph.prebuilt import ToolNode

from sub_agents.sql_database_chain.violation_data_search.violation_prompts import violation_list_prompt, \
    penalty_list_prompt, target_list_prompt
from utils.db_tool import get_case_browser_params_from_mysql, get_association_metrics_from_mysql, \
    get_compiled_mappers_from_mysql, query_ingredients_by_keywords
from utils.load_env import logger
from utils.milvus_tools import MilvusClient, milvus_host
from utils.redis_tool import remove_redis_info, get_redis_connect

MODEL_HUB_URL = os.getenv('MODEL_HUB_HOST')
INDICATOR_TYPES = ["违规案例"]
INGREDIENT_TYPES = ["违规类型", "处罚类型","所在地区"]
FIELD_MAPPERS = {
    "违规类型": {
        "query_param": {"type_name": "违规类型", "case_type": "7"},
        "mapper": "ingredient.violate_type_path",
        "name_field": "label_path"
    },
    "案例所属板块": {
        "query_param": {"type_name": "案例板块", "case_type": "7"},
        "mapper": "ingredient.violate_apply_module",
        "name_field": "ingredients_name"
    },
    "处罚类型": {
        "query_param": {"type_name": "处罚类型", "case_type": "7"},
        "mapper": "ingredient.penalty_type_code",
        "name_field": "label_path"
    },
    "所在地区": {
        "query_param": {"type_name": "所在地区", "case_type": "7"},
        "mapper": "ingredient.area_path",
        "name_field": "ingredients_name"
    },
    "省份": {
        "query_param": {"type_name": "所在地区", "case_type": "7"},
        "mapper": "ingredient.area_path",
        "name_field": "ingredients_name"
    },
    "处罚机构": {
        "query_param": {"type_name": "处罚机构", "case_type": "7"},
        "mapper": "ingredient.disciplinary_org",
        "name_field": "ingredients_name"
    },
    "申辩情况": {
        "query_param": {"type_name": "申辩情况", "case_type": "7"},
        "mapper": "ingredient.adopt_condition",
        "name_field": "ingredients_name"
    },
    "所属证监会行业（废止）": {
        "query_param": {"type_name": "证监会行业(废止)", "case_type": "7"},
        "mapper": "ingredient.industry_path",
        "name_field": "ingredients_name"
    },
    "所属中上协行业（新）": {
        "query_param": {"type_name": "中上协行业(新)", "case_type": "7"},
        "mapper": "ingredient.industry_capc_path",
        "name_field": "ingredients_name"
    },
    "所属申万行业": {
        "query_param": {"type_name": "申万行业(2021)", "case_type": "7"},
        "mapper": "ingredient.industry_sw_path",
        "name_field": "ingredients_name"
    },
    "处罚对象身份": {
        "query_param": {"type_name": "处罚对象身份", "case_type": "7", "level": "1"},
        "mapper": "ingredient.person_type",
        "name_field": "ingredients_name"
    }
    # 可以添加其他字段的配置
}
violate_collection_demo = [
    {
        "name": "violate_id",
        "description": "案例ID"
    },
    {
        "name": "company_code",
        "description": "公司代码"
    },
    {
        "name": "company_name",
        "description": "公司名称"
    },
    {
        "name": "com_industry_name",
        "description": "行业"
    },
    {
        "name": "com_belongs_plate_name",
        "description": "所属板块名称"
    },
    {
        "name": "punish_org",
        "description": "处罚机构"
    },
    {
        "name": "violate_person",
        "description": "处罚对象"
    },
    {
        "name": "violate_person_label",
        "description": "处罚对象身份"
    },
    {
        "name": "punish_type_label",
        "description": "处罚类型名称"
    },
    {
        "name": "case_list",
        "description": "违规事项"
    },
    {
        "name": "title",
        "description": "标题"
    },
    {
        "name": "address",
        "description": "地址"
    },
    {
        "name": "adopt_condition",
        "description": "申辩情况"
    },
    {
        "name": "event_time_str",
        "description": "事件时间"
    },
    {
        "name": "embedding_string",
        "description": "向量文本"
    }
]


def filter_messages(messages: list):
    # 获取所有 type == 'human' 的下标
    human_indices = [i for i, message in enumerate(messages) if message.type == "human"]

    # 如果少于3个 'human' 消息，则返回原列表
    if len(human_indices) < 3:
        return messages

    # 获取倒数第三个 'human' 消息的下标

    third_last_human_index = human_indices[-2]

    # 返回从倒数第三个 'human' 消息开始的子列表
    return messages[third_last_human_index:]


def create_tool_node_with_fallback(tools: list):
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )


def handle_tool_error(state) -> dict:
    error = state.get("error")
    tool_calls = state["messages"][-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"错误: {repr(error)}\n 请修正错误。",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }


async def send_processing_feedback(input_text: str):
    """
    发送处理反馈消息到客户端。

    Args:
        input_text (str): 用户输入的文本。
    """
    writer = get_stream_writer()
    writer(json.dumps({"AI_AGENT_FLOW": f"正在违规案例案例库中查询{input_text}问题"},
                      ensure_ascii=False))
    writer(json.dumps({"DATA_SOURCE": "7"},
                      ensure_ascii=False))


async def get_target_list_for_llm(input_text: str, target_list: str) -> list[Any]:
    try:
        prompt_template = ChatPromptTemplate.from_messages([
            HumanMessagePromptTemplate.from_template(target_list_prompt)
        ])

        parser = JsonOutputParser()

        llm = ChatOpenAI(
            model='hs-deepseek-v3-0324',
            temperature=0,
            max_tokens=None,
            timeout=300,
            api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
            base_url=MODEL_HUB_URL
        )
        # llm = get_a_llm(llm_type='gpt-4o')
        chain = (
                {"input": RunnablePassthrough(), "target_list": RunnablePassthrough()}
                | prompt_template
                | llm
                | parser
        )

        with timer():
            result = await chain.ainvoke({
                "input": input_text,
                "target_list": target_list
            })
            # 发射自定义事件

            # 添加 证券代码 公司简称 公司全称 到结果中
            result.extend(['违规内容', '证券代码', '公司简称', '总罚款金额', '所属中上协行业（新）'])

            # 去除 含有 【拆】 的指标
            result = [item for item in result if '【拆】' not in item]

            # 打印结果用于调试
            print('大模型指标',result)

            # 如果result大于20则返回[]
            if len(result) > 20:
                result = result[:20]

            # 使用 ast.literal_eval 解析带有单引号的 target_list
            try:
                target_list_dict = ast.literal_eval(target_list)
            except (ValueError, SyntaxError) as e:
                print(f"解析错误: {e}")
                return []  # 返回空列表或其他处理逻辑

            # 匹配结果，返回与 LLM 输出结果匹配的完整字典（键值对），然后转换为 JSON 数组
            matched_results = [item for item in target_list_dict if item.get('indicators_name') in result]

            # 返回 JSON 数组
            return matched_results
    except Exception as e:
        logger.error(f"[get_target_list_for_llm] 异常：{e}")
        return []


def vector_case_search(query: str, search_type: str, top_k: int = 10, sim_threshold: float = 0.55):
    """
    支持指标和成分的通用向量检索：根据类型自动路由到指标库或成分库，直接传文本即可。
    :param query: 检索文本（不用向量化）
    :param search_type: 检索类型
    :param top_k: 返回数量
    :param sim_threshold: 相似度阈值
    :return: 检索结果
    """
    if search_type in INDICATOR_TYPES:
        collection_name = "agent_knowledge_indicators"
        expr = f"case_type_name == '{search_type}'" if search_type else ''
        default_fields = ["indicators_name", "indicators_name_alias", "data_type", "description", "optional_value",
                          "mapper_fields"]
    elif search_type in INGREDIENT_TYPES:
        collection_name = "agent_knowledge_ingredients"
        expr = f"type_name == '{search_type}'" if search_type else ''
        expr += " and case_type == '7'"
        default_fields = ["ingredients_name", "ingredients_name_alias", "description", "label_path", "level"]
    elif search_type == '行业':
        collection_name = "agent_knowledge_ingredients"
        expr = f'type_name in ["中上协行业(新)", "申万行业(2021)"] and case_type == "7" '
        default_fields = ["ingredients_name", "ingredients_name_alias", "description", "label_path", "level"]
    else:
        raise ValueError(f"未知的检索类型: {search_type}")

    client = MilvusClient(milvus_host)
    response = client.search_vector(
        collection_name=collection_name,
        vectors=query,  # 直接传文本
        expr=expr,
        top_k=top_k,
        out_fields=default_fields,
    )
    results = []
    if response.get('success') and response.get('result'):
        results = response.get('result')[0]

        # 过滤相似度
        results = [hit for hit in results if hit.get('distance', 0) > sim_threshold]

        # 处理 label_path，按 "-" 分割并取最后一部分
        for hit in results:
            if 'label_path' in hit and hit['label_path']:  # 检查 label_path 是否存在且非空
                hit['label_path'] = hit['label_path'].split("-")[-1]
            # 删除 id 和 distance 字段
            hit.pop('id', None)  # 使用 pop 安全删除，避免 KeyError
            hit.pop('distance', None)
    return results


def vector_qa_search(user_question: str, sim_threshold: float = 0.95, out_fields=None):
    """
    检索向量库，返回符合条件的标注查询参数。
    :param user_question: 用户问题文本（直接传文本即可）
    :param sim_threshold: 相似度阈值，默认0.95
    :param out_fields: 查询字段列表，默认返回['params']
    :return: 标注字段内容（params）或 None
    """
    from utils.milvus_tools import MilvusClient, milvus_host
    client = MilvusClient(milvus_host)
    # 默认只查 params 字段
    if out_fields is None:
        out_fields = ['params']
    response = client.search_vector(
        collection_name="agent_chat_annotation_vector",
        vectors=user_question,  # 直接传文本，后端负责向量化
        metric_type="COSINE",
        top_k=1,
        out_fields=out_fields,
    )
    if response.get('success') and response.get('result'):
        hits = response['result'][0]
        for hit in hits:
            # milvus返回的距离字段有时叫 distance，有时叫 score，需兼容
            distance = hit.get('distance') or hit.get('score')
            if distance is not None and distance >= sim_threshold:
                params = json.loads(hit.get('params'))
                params.pop('caseType', None)
                logger.debug(f"找到匹配数据，相似度:{distance},标注字段:{params}")
                return params
    return None


@contextmanager
def timer():
    start = time.time()
    yield
    end = time.time()
    print(f"Elapsed time: {end - start:.2f} seconds")


async def get_violation_type_for_llm(input_text: str, violation_list: str) -> list[Any]:
    try:
        prompt_template = ChatPromptTemplate.from_messages([
            HumanMessagePromptTemplate.from_template(violation_list_prompt)
        ])

        parser = JsonOutputParser()

        llm = ChatOpenAI(
            model='hs-deepseek-v3-0324',
            temperature=0,
            max_tokens=None,
            timeout=300,
            api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
            base_url=MODEL_HUB_URL
        )
        chain = (
                {"input": RunnablePassthrough(), "violation_list": RunnablePassthrough()}
                | prompt_template
                | llm
                | parser
        )

        with timer():
            result = await chain.ainvoke({
                "input": input_text,
                "violation_list": violation_list
            })

            # 打印结果用于调试
            print(result)

            # 如果result大于10则返回[]
            if len(result) > 10:
                result = result[:10]

            # 使用 ast.literal_eval 解析带有单引号的 violation_list
            try:
                violation_list_dict = ast.literal_eval(violation_list)
            except (ValueError, SyntaxError) as e:
                print(f"解析错误: {e}")
                return []  # 返回空列表或其他处理逻辑

            # 匹配结果，返回与 LLM 输出结果匹配的完整字典（键值对），然后转换为 JSON 数组
            matched_results = [item for item in violation_list_dict if item.get('ingredients_name') in result]

            # 返回 JSON 数组
            return matched_results
    except Exception as e:
        logger.error(f"[get_violation_type_for_llm] 异常：{e}")
        return []


async def get_penalty_type_for_llm(input_text: str, penalty_list: str) -> list[Any]:
    try:
        prompt_template = ChatPromptTemplate.from_messages([
            HumanMessagePromptTemplate.from_template(penalty_list_prompt)
        ])

        parser = JsonOutputParser()

        llm = ChatOpenAI(
            model='hs-deepseek-v3-0324',
            temperature=0,
            max_tokens=None,
            timeout=300,
            api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
            base_url=MODEL_HUB_URL
        )
        chain = (
                {"input": RunnablePassthrough(), "penalty_list": RunnablePassthrough()}
                | prompt_template
                | llm
                | parser
        )

        with timer():
            result = await chain.ainvoke({
                "input": input_text,
                "penalty_list": penalty_list
            })

            # 打印结果用于调试
            print(result)

            # 如果result大于10则返回[]
            if len(result) > 10:
                result = result[:10]

            # 使用 ast.literal_eval 解析带有单引号的 violation_list
            try:
                penalty_list_dict = ast.literal_eval(penalty_list)
            except (ValueError, SyntaxError) as e:
                print(f"解析错误: {e}")
                return []  # 返回空列表或其他处理逻辑

            # 匹配结果，返回与 LLM 输出结果匹配的完整字典（键值对），然后转换为 JSON 数组
            matched_results = [item for item in penalty_list_dict if item.get('ingredients_name') in result]

            # 返回 JSON 数组
            return matched_results
    except Exception as e:
        logger.error(f"[get_penalty_type_for_llm] 异常：{e}")
        return []


async def extract_violation_keywords_for_llm(input_text: str) -> dict:
    """
    从用户输入中提取违规类型、处罚类型和行业相关的关键词，用于向量检索

    参数:
    input_text (str): 用户输入的文本

    返回:
    dict: {"violation_keywords": "违规类型关键词", "penalty_keywords": "处罚类型关键词", "industry_keywords": "行业关键词"}
    """
    try:
        # 定义提取关键词的提示词
        extract_keywords_prompt = """从用户输入中判断用户是否需要按违规类型、处罚类型、行业进行筛选，如果需要则提取对应的关键词用于向量检索。

核心判断原则：
- 只有当用户明确需要把这些字段作为筛选条件来过滤数据时，才提取对应关键词
- 仅仅提到这些概念但不作为筛选条件的，不提取关键词

要求：
1. 违规类型筛选：用户明确要查找特定违规行为的案例时提取，如"财务造假"、"信息披露违规"、"内幕交易"等
2. 处罚类型筛选：用户明确要查找特定处罚措施的案例时提取，如"行政处罚"、"罚款"、"警告"、"责令改正"等
3. 行业筛选：用户明确要查找特定行业的案例时提取，如"机械"、"医药"、"房地产"、"银行"等
4. 优先选择用户原文中出现的词汇，每类1-2个最核心的词汇
5. 避免过于宽泛的词汇（如"违规"、"处罚"、"案例"、"行业"等）
6. 如果用户只是泛泛询问或不需要特定筛选，返回空字符串
7. 按JSON格式输出：{{"violation_keywords": "关键词", "penalty_keywords": "关键词", "industry_keywords": "关键词"}}

示例：
用户: 2020年至今因财务造假被行政处罚的上市公司有多少家
分析: 需要按"财务造假"筛选违规类型，按"行政处罚"筛选处罚类型
输出: {{"violation_keywords": "财务造假", "penalty_keywords": "行政处罚", "industry_keywords": ""}}

用户: 机械行业内幕交易相关的处罚案例
分析: 需要按"机械"筛选行业，按"内幕交易"筛选违规类型，处罚类型不明确
输出: {{"violation_keywords": "内幕交易", "penalty_keywords": "", "industry_keywords": "机械"}}

用户: 最近有哪些违规案例
分析: 泛泛询问，不需要特定筛选条件
输出: {{"violation_keywords": "", "penalty_keywords": "", "industry_keywords": ""}}

用户: 违规案例的处罚类型有哪些
分析: 询问处罚类型种类，不是要按处罚类型筛选案例
输出: {{"violation_keywords": "", "penalty_keywords": "", "industry_keywords": ""}}

用户: 北京地区的违规案例有哪些
分析: 按地区筛选，不涉及违规类型、处罚类型、行业筛选
输出: {{"violation_keywords": "", "penalty_keywords": "", "industry_keywords": ""}}

用户查询: {input}
输出:"""

        prompt_template = ChatPromptTemplate.from_messages([
            HumanMessagePromptTemplate.from_template(extract_keywords_prompt)
        ])

        parser = JsonOutputParser()

        llm = ChatOpenAI(
            model='hs-deepseek-v3-0324',
            temperature=0,
            max_tokens=None,
            timeout=300,
            api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
            base_url=MODEL_HUB_URL
        )

        chain = (
                {"input": RunnablePassthrough()}
                | prompt_template
                | llm
                | parser
        )

        with timer():
            result = await chain.ainvoke({"input": input_text})
            logger.debug(f"[extract_violation_keywords_for_llm] 提取的关键词: {result}")
            return result

    except Exception as e:
        logger.error(f"[extract_violation_keywords_for_llm] 异常：{e}")
        return {"violation_keywords": "", "penalty_keywords": "", "industry_keywords": ""}


def remove_duplicates_by_param_name(data_list):
    unique_items = {}

    for item in data_list:
        # 修改键名
        item["指标名称"] = item.pop("indicators_name", None)
        item["配置项"] = item.pop("mapper_fields", None)
        item["可选值"] = item.pop("optional_value", None)
        item["指标别名"] = item.pop("indicators_name_alias", None)
        item["指标类型"] = item.pop("data_type", None)
        item["指标解释"] = item.pop("description", None)
        # 以 "指标名称" 和 "可选值" 为依据去重
        key = (item.get('指标名称'), item.get('可选值'))
        if key not in unique_items:
            unique_items[key] = item

    # 返回去重后的对象列表
    return list(unique_items.values())


def format_filter_conditions(filters: Dict[str, Union[str, Dict[str, Any]]]) -> str:
    if not filters:
        return ""
    formatted_conditions = []

    for field, condition in filters.items():
        if isinstance(condition, dict) and "keywords" in condition:  # 检查字典且包含 "keywords" 键
            # 文本匹配条件 (字典形式)
            keywords = condition["keywords"]
            op = condition.get("op", "and")  # 默认 "and" 如果 "op" 没指定

            if op == "and":
                formatted_conditions.append(f"{field} 全包含关键词 '{keywords}'")
            elif op == "or":
                formatted_conditions.append(f"{field} 包含任一关键词 '{keywords}'")
        elif isinstance(condition, dict):
            # 数值范围条件
            range_conditions = []
            for operator, value in condition.items():
                if operator == "op":
                    continue  # 跳过逻辑运算符
                if operator == "gt":
                    range_conditions.append(f"{field} > {value}")
                elif operator == "lt":
                    range_conditions.append(f"{field} < {value}")
                elif operator == "gte":
                    range_conditions.append(f"{field} ≥ {value}")
                elif operator == "lte":
                    range_conditions.append(f"{field} ≤ {value}")
                elif operator == "equal":
                    range_conditions.append(f"{field} = {value}")
            # 使用 "并且" 或 "或者" 连接多个条件
            if "op" in condition:
                logical_op = condition["op"]
                if logical_op.lower() == "and":
                    range_conditions_str = " 并且 ".join(range_conditions)
                elif logical_op.lower() == "or":
                    range_conditions_str = " 或者 ".join(range_conditions)
                else:
                    range_conditions_str = f" {logical_op} ".join(range_conditions)
            else:
                range_conditions_str = " 并且 ".join(range_conditions)
            formatted_conditions.append(range_conditions_str)

    return "，".join(formatted_conditions)


def map_punishment_target_identity(filters):
    """
    将处罚对象身份的简化类别映射为详细关键词。

    :param filters: 包含筛选条件的字典
    :return: 更新后的筛选条件字典，包含展开后的身份关键词
    """
    if not filters or "处罚对象身份" not in filters:
        return filters

    if isinstance(filters["处罚对象身份"], dict) and "keywords" in filters["处罚对象身份"]:
        identity_mapping = {
            '中介机构': '投资咨询机构及其从业人员 资信评级机构及其从业人员 律师事务所及其从业人员 会计师事务所及其从业人员 其他机构及其从业人员 评估机构及其从业人员 基金公司及其从业人员 期货公司及其从业人员',
            '公司及股东': '全资子公司 法定代表人 公司本身 法人股东 实际控制人 控股参股公司 控股股东 自然人股东 其他股东 挂牌公司',
            '董监高': '时任董事长 董事长 时任董事 董事 时任独董 独立董事 时任董秘 董事会秘书 时任监事 监事 总经理 时任总经理 时任财务总监 财务总监 高管 时任高管',
            '基金': '总经理 合伙人 法定代表人 投资经理 合规风控负责人 实际控制人 基金管理人 基金从业人员 董事长 高管 董事 其他关系方 基金销售机构 股东 监事 律师事务所及其从业人员',
            '证券公司': '证券公司及其从业人员 法人股东 自然人股东 董事长 董事会秘书 监事 高管 其他关系方',
            '银行业': '银行本身 银行从业人员 法定代表人 主要负责人 高级管理人员 非银行金融机构 金融机构从业人员 股东',
            '保险业': '保险公司本身 保险公司从业人员 董事 监事 高级管理人员 法定代表人 保险公估机构 保险公估从业人员 保险专业代理机构 保险兼业代理机构 保险代理人 保险经纪机构 保险经纪人 保险销售从业人员 其他机构或相关人员',
            '人民银行': '银行本身 银行从业人员 保险公司本身 保险公司从业人员 非银行金融机构 金融机构从业人员 证券公司本身 证券公司从业人员 期货公司本身 期货公司从业人员 普通公司 高级管理人员 法定代表人 征信公司本身 征信公司从业人员 支付结算公司本身 支付结算公司从业人员 普通公司员工 当事人本身 其他'
        }

        category = filters["处罚对象身份"]['keywords']
        if category in identity_mapping:
            filters["处罚对象身份"]['keywords'] = identity_mapping.get(category)
            filters["处罚对象身份"]['op'] = 'or'

    return filters


# query_ingredients_by_keywords 返回结果示例
# type_name ingredients_name    label_path                                              code
# 违规类型	违规类型	            违规类型	                                                VIOLATE_TYPE_ALL
# 违规类型	违规类型分类-上市公司	违规类型/违规类型分类-上市公司	                            VIOLATE_TYPE_ALL/01
# 违规类型	公司信息披露违规	    违规类型/违规类型分类-上市公司/公司信息披露违规	            VIOLATE_TYPE_ALL/01/459097900465909058
# 违规类型	信息披露不真实	    违规类型/违规类型分类-上市公司/公司信息披露违规/信息披露不真实    VIOLATE_TYPE_ALL/01/459097900465909058/459097900465909059

def filter_hierarchical_options(items, name_field):
    """
    过滤具有层级关系的选项，只保留最上层的父级选项

    :param items: 匹配到的选项列表
    :param name_field: 选项名称字段
    :return: 过滤后的选项列表
    """
    if not items:
        return []

    # 提取所有选项的路径
    paths = [item.get(name_field, "") for item in items]

    # 过滤掉子级选项，只保留父级选项
    filtered_indices = []
    for i, path1 in enumerate(paths):
        is_child = False
        for j, path2 in enumerate(paths):
            if i != j and path1.startswith(path2 + "/"):
                # path1是path2的子级，所以不保留path1
                is_child = True
                break
        if not is_child:
            filtered_indices.append(i)

    # 返回过滤后的选项
    return [items[i] for i in filtered_indices]


def process_special_fields(filters: Optional[Dict[str, Union[str, Dict[str, Any]]]]) -> Tuple[
    Dict, Dict[str, List[Dict]]]:
    """
    处理所有特殊字段的筛选条件

    :param filters: 原始筛选条件
    :return: 更新后的筛选条件和各字段处理结果的字典
    """
    if not filters:
        return {}, {}

    updated_filters = copy.deepcopy(filters)
    results_by_field = {}

    # 遍历过滤条件中的每个字段
    for field, condition in list(filters.items()):
        # 检查字段是否在配置中
        if field in FIELD_MAPPERS and isinstance(condition, dict) and "keywords" in condition:
            keywords = [k.strip() for k in condition["keywords"].split() if k.strip()]
            if not keywords:
                continue

            # 获取字段配置
            field_config = FIELD_MAPPERS[field]

            # 执行查询
            matched_items = query_ingredients_by_keywords(**field_config["query_param"])

            # 处理匹配结果
            matched_items_by_keyword = []
            matched_keywords = []

            for keyword in keywords:
                for item in matched_items:
                    # 简化的匹配逻辑，根据需要可以调整
                    name_field = field_config.get("name_field")
                    if keyword == item.get(name_field, "").split("-")[-1]:  # 违规类型特殊处理
                        matched_items_by_keyword.append(item)
                        matched_keywords.append(keyword)
                        break

            # 对于具有层级关系的字段（如“违规类型”），过滤掉子级选项
            formatted_results = []
            if field in ["违规类型", "处罚类型"]:
                name_field = field_config.get("name_field")
                filtered_items = filter_hierarchical_options(matched_items_by_keyword, name_field)

                # 重新构建结果
                for item in filtered_items:
                    formatted_results.append({
                        "mapper": field_config.get("mapper"),
                        "code": item.get("code")
                    })
            else:
                # 对于其他字段，保持原有逻辑
                for item in matched_items_by_keyword:
                    formatted_results.append({
                        "mapper": field_config.get("mapper"),
                        "code": item.get("code")
                    })

            # 保存结果
            if formatted_results:
                results_by_field[field] = formatted_results

                # 更新filters，移除已匹配的关键词
                unmatched_keywords = [k for k in keywords if k not in matched_keywords]
                if not unmatched_keywords:
                    del updated_filters[field]
                else:
                    updated_filters[field]["keywords"] = " ".join(unmatched_keywords)

    return updated_filters, results_by_field


def build_request_body(query_params: Dict[str, Any], case_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    构建完整的请求体，支持多指标和复杂筛选条件。

    :param query_params: 查询参数字典，包含 metrics 和 filters
    :param case_type: 案例类型编码(1:IPO 2:公开增发 3:可转债发行 4:资产重组 5:配股发行 6:非公开发行 7:违规案例 8:重组标的)，用于查询mysql数据库
    :return: 构建好地请求体，如果没有结果则返回 None
    """
    target_list = get_case_browser_params_from_mysql(case_type)
    target_data = {}
    for target in target_list:
        target_data[target.get("indicators_name")] = {
            "param_name": target.get("indicators_name"),  # 保持原有键名兼容性
            "param_type": target.get("case_type_name"),
            "mapper": target.get("mapper"),
            "data_type": target.get("data_type"),
            "optional_value": target.get("optional_value"),
            "description": target.get("description"),
            "hot_score": target.get("hot_score"),
            "mapper_fields": target.get("mapper_fields"),
            "param_alias": target.get("indicators_name_alias"),
            "is_required": target.get("is_required"),
            "sort": target.get("sort")
        }

    # 从 query_params 获取要查询的指标和筛选条件
    metrics = query_params.get("metrics", [])
    filters = query_params.get("filters", {})
    sort_by = query_params.get("sort_by")
    sort_order = query_params.get("sort_order", "asc").upper()

    # 构建 quotas 列表，包含每个指标的 mapper, label, data_type 等信息
    quotas: List[Dict[str, Any]] = [
        {"label": "案例ID", "mapper": "mapper.vid", "mapper_fields": [], "date_type": "STRING"}]
    # 把 filters 中的 key 和排序字段添加到 metrics
    if filters:
        metrics.extend([k for k in filters.keys() if k not in metrics])

    # 添加排序字段到 metrics（如果存在且不在 metrics 中）
    if sort_by and sort_by not in metrics:
        metrics.append(sort_by)

    # 将必查字段加进metrics中
    required_metrics = [param_name for param_name, target_info in target_data.items() if
                        target_info.get("is_required") in {'1', '2'}]

    metrics.extend(required_metrics)

    # 对指标去重
    metrics = list(dict.fromkeys(metrics))

    # 查询出全部关联指标
    association_metrics = get_association_metrics_from_mysql(case_type, metrics)
    for association_metric in association_metrics:
        metrics.extend(association_metric.get('related_params').split(" "))

    # 对指标去重
    metrics = list(dict.fromkeys(metrics))

    # metrics 按照 target_data 的 is_required 字段进行倒序排序
    metrics.sort(key=lambda x: target_data.get(x, {}).get("is_required", 0), reverse=True)

    for metric in metrics:
        # 从 target_data 中找到指标相关信息
        target_info = target_data.get(metric)
        if not target_info:
            if metric == '次数':
                return {"error": '次数指标不存在，根据返回数据条数判断即可'}
            else:
                return {
                    "error": f"指标异常: {metric}。该指标可能为模型自动生成，建议尝试替代指标进行重试，或向用户从指标列表中确认正确的 '{metric}' 指标。"}

        # 如果该指标有对应的筛选条件，构建 mapper_screens
        mapper_screens = build_mapper_screens({metric: filters[metric]}) if filters and metric in filters else None
        quotas.append(create_quota_entry(target_info, mapper_screens))

    if not quotas:
        print("未找到有效的指标")
        return {"error": "未找到有效的指标"}

    # 处理排序
    if sort_by:
        sort_mapper = next((quota["mapper"] for quota in quotas if quota["label"] == sort_by), None)
        if sort_mapper:
            sorts = [{
                "mapper": sort_mapper,
                "direction": "ASC" if sort_order == "ASC" else "DESC"
            }]
        else:
            return {"error": f"未找到排序字段: {sort_by}"}
    else:
        # 如果没有提供 sort_by，使用默认排序
        sorts = [{"mapper": "principal.sort", "direction": "DESC"}]

    # 构建排序和其他部分
    return {
        "title": query_params.get("title", "数据浏览器"),
        "quotas": quotas,
        "ingredients": [],
        "sorts": sorts,
        "selections": []
    }


def build_mapper_screens(filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据 filters 构建 mapper_screens 条件。
    :param filters: 查询参数中的筛选条件部分
    :return: 构建好的 mapper_screens 结构
    """
    conditions = []
    formula_map = {"gt": "GT", "gte": "GTE", "lt": "LT", "lte": "LTE", "equal": "EQ"}

    for field, condition in filters.items():
        if isinstance(condition, dict) and "keywords" in condition:
            logic = condition.get('op', 'AND').upper()  # 文本条件的 logic 默认也是 AND，保持一致
            keywords_str = condition["keywords"]
            conditions.append({"logic": logic, "formula": "CONTAINS", "val": keywords_str})  # val 直接传递关键词字符串

        elif isinstance(condition, dict):  # 数值/时间范围条件 (保持不变)
            logic = condition.get('op', 'AND').upper()
            for formula, val in condition.items():
                if formula in formula_map:
                    conditions.append({"logic": logic, "formula": formula_map[formula], "val": val})
        elif isinstance(condition, str):
            conditions.append({"logic": "AND", "formula": "CONTAINS", "val": condition})

    return {"mode": "CUSTOM", "condition": conditions}


def create_quota_entry(target_info: Dict[str, Any], mapper_screens: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    根据目标信息和 mapper_screens 创建配额条目。
    :param target_info: 目标指标的信息
    :param mapper_screens: 指标的 mapper_screens（如果有）
    :return: 构建好的配额条目
    """
    # 处理 mapper_fields，检查是否为字符串并尝试解析为列表
    mapper_fields = target_info.get("mapper_fields") or []
    if isinstance(mapper_fields, str):
        try:
            # 如果字符串有效，则解析为列表
            mapper_fields = json.loads(mapper_fields)
        except json.JSONDecodeError:
            # 如果解析失败，保持为原始字符串或根据需求处理
            mapper_fields = []

    # 初始化拼接后的部分
    appended_values = []

    # 遍历 mapper_fields 并提取 value
    for field in mapper_fields:
        if "value" in field:
            appended_values.append(str(field["value"]))

    # 如果存在 value，将它们用 '_' 拼接并添加到 mapper 字段的末尾
    if appended_values:
        target_info["mapper"] += "." + "_".join(appended_values)

    # 构建配额条目
    quota_entry = {
        "label": target_info["param_name"],
        "mapper": target_info["mapper"],  # 使用修改后的 mapper
        "mapper_fields": mapper_fields,  # 使用转换后的 mapper_fields
        "date_type": target_info["data_type"]
    }
    # 如果target_info的sort存在切不为空,则quota_entry里面也要放入sort
    if "sort" in target_info and target_info["sort"]:
        quota_entry["sort"] = target_info["sort"]

    if mapper_screens:
        quota_entry["mapper_screens"] = mapper_screens

    return quota_entry


async def make_post_request(body):
    """发送 POST 请求并处理可能的错误。"""
    try:
        # 定义 API 的 URL 和请求头
        api_url = "https://services.easy-board.com.cn/tdb-api/tdbv/quota?page=0&size=10"
        access_token = ''
        headers = {
            "authorization": access_token,
            "Content-Type": "application/json;charset=UTF-8"
        }

        response = requests.post(api_url, headers=headers, json=body)

        # 如果状态码是 401，需要重新获取 access_token
        if response.status_code == 401:
            # 获取新的 access_token
            remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')
            new_access_token = get_access_token()
            headers.update({"authorization": new_access_token})
            response = requests.post(api_url, headers=headers, json=body)

        if response.status_code == 200:
            result = response.json()

            # 发送请求体、url、结果 信息
            await send_request_body_feedback(body, api_url, result)
            result = truncate_values(result, max_length=500)

            # 判断返回结果是否为空
            if result['total'] == 0:
                # return {"error": "未找到符合条件的案例，请检查指标后重试，或者使用bing_search工具进行搜索"}
                return {
                    "error": "没有找到符合当前查询条件的数据。",
                    "next_action": (
                        "请根据以下建议，尝试调整并重新生成搜索条件："
                        "1. 检查查询条件表达是否准确、完整，避免使用缩略语、简称或不规范表述，建议采用更标准、更正式的术语。"
                        "2. 适当放宽条件（如更宽泛的关键词、去掉部分过滤、扩大时间范围等）。"
                        "3. 利用优化后的新条件，重新进行数据查询。"
                        "4. 如果优化条件后尝试两次仍未获得结果，再切换为使用外部 Bing 搜索工具。"
                        "注意：LLM请主动根据上述策略修正和迭代查询"
                    )
                }

            # 处理数据
            result = {
                "案例总数": result['total'],
                "每页条数": result['size'],
                "当前页码": result['page'] + 1,
                "案例列表": [clean_dict(line) for line in result['result']],
            }

            # 调用替换函数
            compiled_mappers = get_compiled_mappers_from_mysql('7')
            result = replace_keys(result, compiled_mappers)
            logger.info(
                f"[make_post_request] violation查询结果\n"
                f"案例总数{result['案例总数']},案例信息demo{result['案例列表']}")

            return result
        else:
            return {"error": f"请求失败，状态码: {response.status_code}, 信息: {response.text}"}
    except requests.exceptions.RequestException as e:
        return {"error": f"请求出错: {e}"}


def get_access_token():
    r_key = 'CASE_SEARCH_ACCESS_TOKEN'
    r = get_redis_connect()

    if r.exists(r_key):
        access_token = r.get(r_key).decode('utf-8')
    else:
        resp = requests.get(
            'https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=610abd52fac84f60&client_secret=4ea53ec5610abd52fac84f60a02bcf4c')
        access_token = resp.json().get('access_token')
        logger.info(f'get_code_map r_key: {r_key} {access_token}')
        r.set(r_key, access_token, ex=60 * 30)
    return access_token


def truncate_values(obj, max_length=100, ellipsisStr="..."):
    """
    截断字典或列表中的过长值。

    Args:
        obj: 需要处理的对象，可以是字典、列表或其它基本类型。
        max_length: 值的最大长度，默认为100。
        ellipsisStr: 截断后添加的省略号，默认为 "..."。

    Returns:
        返回一个新的对象，其中过长的值已被截断。
    """
    if isinstance(obj, dict):
        return {k: truncate_values(v, max_length, ellipsisStr) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [truncate_values(elem, max_length, ellipsisStr) for elem in obj]
    else:
        if isinstance(obj, str) and len(obj) > max_length:
            return obj[:max_length - len(ellipsisStr)] + ellipsisStr
        elif isinstance(obj, (list, dict)):
            return obj
        elif obj is None:
            return None
        elif isinstance(obj, (int, float, bool)):
            return obj
        elif len(str(obj)) > max_length:
            return str(obj)[:max_length - len(ellipsisStr)] + ellipsisStr
        else:
            return obj


async def send_request_body_feedback(request_body: Dict[str, Any], url: str, result: Dict[str, Any]):
    """
    发送请求体、url、结果 信息到客户端。

    Args:
        request_body (Dict[str, Any]): 请求体。
        url (str): 请求的 URL。
        result (Dict[str, Any]): 请求结果。
        config (Optional[Dict]): 配置信息，包含 manager 和 websocket。
    """
    writer = get_stream_writer()

    # 创建包含所有信息的 JSON 对象，并添加 message_type
    message = {
        "AI_AGENT_RESPONSE": {
            "AI_AGENT_BODY": request_body,
            "AI_AGENT_URL": url,
            "AI_AGENT_RESULT": result
        }
    }

    # 将 JSON 对象序列化为字符串并发送
    writer(json.dumps(message, ensure_ascii=False))


def clean_dict(d):
    if not isinstance(d, dict):
        return d
    result = {}
    for k, v in d.items():
        cleaned_v = clean_dict(v)
        if cleaned_v not in ("", {}, []):
            result[k] = cleaned_v
    return result


def replace_keys(obj, mapper):
    """
    将字典中的键名替换为中文

    Args:
        obj: 要处理的对象（字典、列表或其他类型）
        mapper: 映射字典，键是原始路径（如 'company.securities_code.newest'），值是中文键名

    Returns:
        处理后的对象，键名已替换为中文
    """
    # 预处理映射字典，提取字段映射
    field_mappers = {}
    for path, value in mapper.items():
        parts = path.split('.')
        if len(parts) >= 2:
            # 对于形如 'company.securities_code.newest' 的路径，提取 'securities_code'
            if len(parts) >= 3 and parts[-1] == 'newest':
                field_key = parts[-2]  # 取倒数第二个部分
                field_mappers[field_key] = value
            # 对于形如 'company.securities_code' 的路径，提取 'securities_code'
            else:
                field_key = parts[-1]  # 取最后一个部分
                field_mappers[field_key] = value

    # 处理函数
    def process(obj):
        # 处理列表
        if isinstance(obj, list):
            return [process(item) for item in obj]

        # 处理字典
        if isinstance(obj, dict):
            result = {}

            for k, v in obj.items():
                # 特殊处理 'newest' 键
                if k == 'newest':
                    result['最新'] = v
                else:
                    # 检查是否有字段映射
                    new_key = k
                    if k in field_mappers:
                        new_key = field_mappers[k]

                    # 递归处理值
                    result[new_key] = process(v)

            return result

        # 其他类型直接返回
        return obj

    return process(obj)


def search_violate_collection_data(collection_name: str, query: str, company_code: str, sim_threshold: float = 0.55):
    results = []
    out_fields = [item['name'] for item in violate_collection_demo]
    mv_client = MilvusClient(os.getenv('MILVUS_HOST'))
    try:
        # 只在 company_code 有值的时候才添加查询条件
        expr = f"company_code == '{company_code}'" if company_code else None

        response = mv_client.search_vector(
            collection_name=collection_name,
            expr=expr,
            vectors=query,
            top_k=12,
            out_fields=out_fields,
        )
        if response.get('success') and response.get('result'):
            results = response.get('result')[0]
            results = [hit for hit in results if hit.get('distance') > sim_threshold]
            results = convert_keys_to_chinese(results)

    except Exception as e:
        logger.error(f"查询milvus失败: {e}\n{traceback.format_exc()}")
    return results


def convert_keys_to_chinese(results, field_mapping=None):
    """
    将结果中的键名转换为中文描述

    Args:
        results: 查询结果列表
        field_mapping: 字段名到中文描述的映射字典，如果为None则自动从violate_collection_demo生成

    Returns:
        转换后的结果列表
    """
    if field_mapping is None:
        # 从violate_collection_demo生成字段映射
        field_mapping = {}
        for field in violate_collection_demo:
            field_mapping[field["name"]] = field["description"]

    converted_results = []
    for item in results:
        converted_item = {}
        for key, value in item.items():
            # 如果键在映射中存在，则使用中文描述，否则保持原样
            new_key = field_mapping.get(key, key)
            converted_item[new_key] = value
        converted_results.append(converted_item)

    return converted_results


def manual_processing_filters(filters: Optional[Dict[str, Union[str, Dict[str, Any]]]]) -> None | dict[Any, Any] | dict[
    str, str | dict[str, Any]]:
    """
    处理所有特殊字段的筛选条件

    :param filters: 原始筛选条件
    :return: 更新后的filters
    """
    if not filters:
        return {}

    # 处理处罚机构中的关键字
    if "处罚机构" in filters and isinstance(filters["处罚机构"], dict) and "keywords" in filters["处罚机构"]:
        keywords = filters["处罚机构"]["keywords"]
        # 将关键字按空格分隔
        keyword_list = keywords.split()
        # 替换包含"证监局"的关键字为"中国证监会"
        for i, keyword in enumerate(keyword_list):
            if "证监局" == keyword:
                keyword_list[i] = "中国证监会"
        # 重新组合关键字
        filters["处罚机构"]["keywords"] = " ".join(keyword_list)

    return filters


if __name__ == '__main__':
    print(vector_case_search('银行', '行业', top_k=10))
