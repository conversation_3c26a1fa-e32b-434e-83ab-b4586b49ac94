[
  {
    'indicators_name': '案例序号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '案例标题',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '处罚对象名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被处罚对象名称',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规的具体内容，没有特别合适检索指标时，请使用该字段检索，使用该关键字时，将检索词拆分成多个短词，提高准确率',
    'mapper_fields': None,
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '处罚对象身份',
    'data_type': 'STRING',
    'optional_value': '中介机构 投资咨询机构及其从业人员 资信评级机构及其从业人员 律师事务所及其从业人员 会计师事务所及其从业人员 其他机构及其从业人员 评估机构及其从业人员 基金公司及其从业人员 期货公司及其从业人员 公司及股东 全资子公司 法定代表人 公司本身 法人股东 实际控制人 控股参股公司 控股股东 自然人股东 其他股东 挂牌公司 董监高 时任董事长 董事长 时任董事 董事 时任独董 独立董事 时任董秘 董事会秘书 时任监事 监事 总经理 时任总经理 时任财务总监 财务总监 高管 时任高管 其他 证券事务代表 公司员工 交易公司股票账户 其他关系方 当事人本身 交易对手方 业绩承诺方 核心技术人员 基金 合伙人 投资经理 合规风控负责人 基金管理人 基金从业人员 基金销售机构 股东 证券公司 证券公司及其从业人员 银行业 银行本身 银行从业人员 主要负责人 高级管理人员 非银行金融机构 金融机构从业人员 保险业 保险公司本身 保险公司从业人员 保险公估机构 保险公估从业人员 保险专业代理机构 保险兼业代理机构 保险代理人 保险经纪机构 保险经纪人 保险销售从业人员 其他机构或相关人员 人民银行 证券公司本身 证券公司从业人员 期货公司本身 期货公司从业人员 普通公司 征信公司本身 征信公司从业人员 支付结算公司本身 支付结算公司从业人员',
    'description': '独立董事和时任独董都是独立董事,董事长和时任董事长都是董事长,其他时任的身份也是如此',
    'mapper_fields': None,
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '违规类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '处罚类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '对违规行为采取的处罚、处分或监管措施的类别，例如 行政处罚、行政监管、自律处分、自律监管处分、立案调查 等。 处罚可能由 监管机构、自律组织、交易所、公司内部或司法机关 做出',
    'mapper_fields': '[{"label": "末级", "value": 1, "title": "处罚类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '罚款明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被处罚的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '总罚款金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位", "type": "currency"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '罚款金额币种',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '没收明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被没收的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '总没收金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '没收金额币种',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '禁入年限',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被市场禁入、公开认定不适合担任上市公司董监高、撤销任职资格、暂停业务、加入黑名单等的处罚年限',
    'mapper_fields': '[{"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '禁入时间',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被市场禁入、公开认定不适合担任上市公司董监高、撤销任职资格、暂停业务、加入黑名单等的处罚时间',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '判刑期限',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '判刑生效日期',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规案例原文',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '案例文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '公告日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚机构',
    'data_type': 'STRING',
    'optional_value': '司法机关 北京证券交易所 国家金融监督管理总局 生态环境部 中华人民共和国财政部 中国证监会及派出机构 中国证券监督管理委员会云南监管局 中国证券监督管理委员会广西监管局 中国证券监督管理委员会湖南监管局 中国证券监督管理委员会河南监管局 中国证券监督管理委员会甘肃监管局 中国证券监督管理委员会吉林监管局 中国证券监督管理委员会安徽监管局 中国证券监督管理委员会内蒙古监管局 中国证券监督管理委员会黑龙江监管局 中国证券监督管理委员会陕西监管局 中国证券监督管理委员会重庆监管局 中国证券监督管理委员会厦门监管局 中国证券监督管理委员会湖北监管局 中国证券监督管理委员会河北监管局 中国证券监督管理委员会江苏监管局 中国证券监督管理委员会山东监管局 中国证券监督管理委员会海南监管局 中国证券监督管理委员会山西监管局 中国证券监督管理委员会新疆监管局 中国证券监督管理委员会江西监管局 中国证券监督管理委员会大连监管局 中国证券监督管理委员会西藏监管局 中国证券监督管理委员会浙江监管局 中国证券监督管理委员会 证监会会计部 中国证券监督管理委员会四川监管局 中国证券监督管理委员会上海监管局 中国证券监督管理委员会宁夏监管局 中国证券监督管理委员会宁波监管局 中国证券监督管理委员会青海监管局 中国证券监督管理委员会广东监管局 中国证券监督管理委员会天津监管局 中国证券监督管理委员会北京监管局 中国证券监督管理委员会上海证券监管专员办事处 中国证券监督管理委员会深圳证券监管专员办事处 中国证券监督管理委员会辽宁监管局 中国证券监督管理委员会青岛监管局 中国证券监督管理委员会深圳监管局 中国证券监督管理委员会贵州监管局 中国证券监督管理委员会福建监管局 深圳证券交易所 深圳证券交易所债券业务中心 深圳证券交易所上市公司管理二部 深圳证券交易所会员管理部 深圳证券交易所公司管理部 深圳证券交易所固定收益部 深圳证券交易所债券业务部 深圳证券交易所上市审核中心 深圳证券交易所中小板公司管理部 深圳证券交易所市场发展部 深圳证券交易所 深圳证券交易所创业板公司管理部 深圳证券交易所上市公司管理一部 深圳证券交易所会计监管部 上海证券交易所 上海证券交易所上市公司管理二部 上海证券交易所上市公司监管二部 上海证券交易所上市公司管理一部 上海证券交易所上市公司监管一部 上海证券交易所债券业务中心 上海证券交易所科创板公司监管部 上海证券交易所科创板上市审核中心 上海证券交易所科创板公司管理部 上海证券交易所发行承销管理部 上海证券交易所上市审核中心 上海证券交易所债券业务部 上海证券交易所 北京证券交易所监管执行部 北京证券交易所 全国股转公司 全国股转公司融资并购一部 全国股转公司会计监管部 全国股转公司公司管理部 全国中小企业股份转让系统有限责任公司公司业务部 全国中小企业股份转让系统 全国股转公司融资并购二部 全国股转公司融资并购部 股转公司市场监察部 全国股转公司监管执行部 全国股转公司自律管理部 全国股转公司挂牌公司监管二部 全国股转公司公司监管一部 全国股转公司公司监管二部 全国股转公司挂牌审查部 全国股转公司公司监管部 全国股转系统 全国股转公司 全国股转公司挂牌公司管理一部 全国股转公司挂牌公司管理二部 国家金融监督管理总局黑龙江监管局 国家金融监督管理总局河北监管局 国家金融监督管理总局江苏监管局 国家金融监督管理总局浙江监管局 国家金融监督管理总局天津监管局 国家金融监督管理总局四川监管局 国家金融监督管理总局重庆监管局 国家金融监督管理总局厦门监管局 国家金融监督管理总局云南监管局 国家金融监督管理总局河南监管局 国家金融监督管理总局西藏监管局 国家金融监督管理总局江西监管局 国家金融监督管理总局广西监管局 国家金融监督管理总局福建监管局 国家金融监督管理总局深圳监管局 国家金融监督管理总局内蒙古监管局 国家金融监督管理总局湖南监管局 国家金融监督管理总局辽宁监管局 国家金融监督管理总局安徽监管局 国家金融监督管理总局北京监管局 国家金融监督管理总局贵州监管局 国家金融监督管理总局 国家金融监督管理总局宁夏监管局 国家金融监督管理总局陕西监管局 国家金融监督管理总局甘肃监管局 国家金融监督管理总局大连监管局 国家金融监督管理总局上海监管局 国家金融监督管理总局山西监管局 国家金融监督管理总局山东监管局 国家金融监督管理总局湖北监管局 国家金融监督管理总局广东监管局 国家金融监督管理总局吉林监管局 国家金融监督管理总局青海监管局 国家金融监督管理总局海南监管局 国家金融监督管理总局宁波监管局 国家金融监督管理总局青岛监管局 国家金融监督管理总局新疆监管局 山东省财政厅 广东省财政厅 江西省财政厅 浙江省财政厅 江苏省财政厅 山西省财政厅 福建省财政厅 吉林省财政厅 上海市财政局 新疆维吾尔自治区财政厅 广西壮族自治区财政厅 云南省财政厅 青海省财政厅 海南省财政厅 贵州省财政厅 黑龙江省财政厅 厦门市财政局 湖南省财政厅 重庆市财政局 天津市财政局 河南省财政厅 深圳市财政局 河北省财政厅 北京市财政局 西藏自治区财政厅 四川省财政厅 陕西省财政厅 甘肃省财政厅 内蒙古自治区财政厅 中华人民共和国财政部 辽宁省财政厅 青岛市财政局 安徽省财政厅 大连市财政局 宁波市财政局 宁夏回族自治区财政厅 湖北省财政厅 行业/交易商协会 中国银行间市场交易商协会 中国证券投资基金业协会 中国注册会计师协会 中国期货业协会 中国证券业协会 重庆市生态环境局 辽宁省生态环境厅 广东省生态环境厅 浙江省生态环境厅 云南省生态环境厅 福建省生态环境厅 河北省生态环境厅 北京市生态环境局 中华人民共和国生态环境部 黑龙江省生态环境厅 天津市生态环境局 江苏省生态环境厅 吉林省生态环境厅 新疆维吾尔自治区生态环境厅 贵州省生态环境厅 青海省生态环境厅 海南省生态环境厅 江西省生态环境厅 湖南省生态环境厅 陕西省生态环境厅 上海生态环境局 内蒙古自治区生态环境厅 山东省生态环境厅 安徽省生态环境厅 山西省生态环境厅 广西壮族自治区生态环境厅 宁夏回族自治区生态环境厅 西藏自治区生态环境厅 四川省生态环境厅 河南省生态环境厅 湖北省生态环境厅 甘肃省生态环境厅 应急管理部 应急管理局 公安消防大队/消防救援支队 生产监督管理局 税务局稽查局 市场监督管理局 食品药品监督管理局 工商行政管理局 质量技术监督局 市场监督管理局 物价局 自然资源管理局 自然资源管理局 林业和草原局 国土资源局 地方政府 开发区管理委员会 综合行政执法局 文化和旅游局 交通运输局 纪律检查/监察委员会 卫生和计划生育局 人民政府 水务局 海关 人民法院 检察院 公安机关 国家发展和改革委员会 住房和城乡建设局 公司自身 其他',
    'description': '违规案例的发出机构，注意使用 地方证监局 检索时，关键字使用"xx监管局"',
    'mapper_fields': None,
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '申辩情况',
    'data_type': 'STRING',
    'optional_value': '申辩不被采纳 申辩被采纳 部分申辩被采纳 无申辩',
    'description': '违规案例中被处罚对象提起异议的申辩情况',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '申辩内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中被处罚对象提起异议或申请听证的内容',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚情况',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '案例当前进程',
    'data_type': 'STRING',
    'optional_value': '立案调查 自律组织监管结果 非行政处罚性监管措施 自律组织监管结果 行政处罚决定 行政处罚事先告知书 公司自查 一审判决 调查终结 二审判决结果 证监会移送公安机关',
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '案例所属板块',
    'data_type': 'STRING',
    'optional_value': '上市公司 所属板块 沪主板 深主板 创业板 科创板 北交所 发行制度 注册制 核准制 企业性质 国有企业 地方国有企业 中央国有企业 民营企业 集体企业 中外合资企业 其他内地企业 证券公司及相关 基金公司及相关 债券及相关 中介机构及其他相关 新三板 基础层 创新层 IPO 银行业 保险业',
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规主体本身',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规主体的交易对手',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '涉事主体所在公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '被执业的公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '被保荐的公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '被内幕交易的公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '被承诺的公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '被操纵股价的公司',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规事件数量',
    'data_type': 'INTEGER',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规事件',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中事件的区分',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚对象名称【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的处罚对象名称',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚对象身份【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的处罚对象身份',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违规类型【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的违规类型',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚类型【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的处罚类型',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '罚款明细【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的罚款情况',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '总罚款金额【拆】',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '没收明细【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的没收金额情况',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '总没收金额【拆】',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '禁入年限【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的被市场禁入、公开认定不适合担任上市公司董监高、撤销任职资格、暂停业务、加入黑名单等的禁入年限',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '禁入时间【拆】',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规案例中单一违规事件的被市场禁入、公开认定不适合担任上市公司董监高、撤销任职资格、暂停业务、加入黑名单等的禁入时间',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联违规案例内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件的其他处罚情况',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联案例处罚机构',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件的其他处罚情况的发出机构',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联案例处罚日期',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件的其他处罚情况的处罚日期',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联函件内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件中涉及的相关函件内容',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '发函日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '违规事件中涉及的相关函件的发函日期',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '回函日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '违规事件中涉及的相关函件的回函日期',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '发函单位',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件中涉及的相关函件的发函单位',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联公告内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件中涉及的相关公司公告内容',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联公告日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '违规事件中涉及的相关公司公告日期',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '串联资本运作案例',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '违规事件中串联案例的情况',
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-处罚对象名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-违规内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-处罚对象身份',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-违规类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-处罚类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-案例原文',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-案例标题',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-案例文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-公告日期',
    'data_type': 'DATE',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '立案调查-处罚机构',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-处罚对象名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-违规内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-处罚对象身份',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-违规类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "末级", "value": 1, "title": "分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-处罚类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一件", "value": 0, "title": "违规事项排名"}, {"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-罚款明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书中被处罚的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-总罚款金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-没收明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书中被没收的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-总没收金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-禁入年限',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书中被市场禁入的年限',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-案例原文',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-案例标题',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-案例文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书的文号',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-公告日期',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书的公告日期',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '事先处罚告知-处罚机构',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '事先处罚告知书的发出机构',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-处罚对象名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-违规内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-处罚对象身份',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-违规类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "末级", "value": 1, "title": "分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-处罚类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "末级", "value": 1, "title": "分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-罚款明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '行政处罚中被处罚的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-总罚款金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-没收明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '行政处罚中被没收的金额情况，默认为人民币',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-总没收金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-禁入年限',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '行政处罚中被市场禁入的年限',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}, {"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-禁入时间',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '行政处罚中被市场禁入的处罚时间',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-案例原文',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-案例标题',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-案例文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-公告日期',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-处罚机构',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-申辩情况',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '行政处罚-申辩内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '[{"label": "进程一", "value": 0, "title": "排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-违规类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "末级", "value": 1, "title": "违规类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-处罚类型',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "末级", "value": 1, "title": "处罚类型分类"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-罚款明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-总罚款金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-没收明细',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-总没收金额',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '上一进程-禁入年限',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "年", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚是否撤销',
    'data_type': 'STRING',
    'optional_value': '已撤销 未撤销',
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '处罚撤销情况',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-对象名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-案例内容',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-对象身份',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-案例原文',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-案例标题',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-案例文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-公告日期',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '调查终结-处罚机构',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '证券代码',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': None,
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '公司简称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '',
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '公司名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '',
    'mapper_fields': '',
    'indicators_name_alias': ''
  },
  {
    'indicators_name': '企业性质',
    'data_type': 'STRING',
    'optional_value': '国有企业 地方国有企业 中央国有企业 民营企业 集体企业 联营企业 有限责任公司 股份有限公司 其他内地企业 外资企业 中外合资经营企业 外资独资经营企业 港、澳、台商投资企业 合资经营企业(港或澳、台资) 港、澳、台商独资经营企业 港、澳、台机构 境外机构',
    'description': '公司的企业性质。根据不同的企业存在的形态和类型，反映了企业的地位、作用和行为方式。',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '注册资本',
    'data_type': 'NUMBER',
    'optional_value': None,
    'description': '合营企业在登记管理机构登记的资本总额,是合营各方已经缴纳的或合营者承诺一定要缴纳的出资额的总和。我国法律、法规规定，合营企业成立之前必须在合营企业合同、章程中明确企业的注册资本，合营各方的出资额、出资比例、利润分配和亏损分担的比例，并向登记机构登记。',
    'mapper_fields': '[{"label": "万元", "value": 0, "title": "单位"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '控股股东',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '出资额占有限责任公司资本总额百分之五十以上或者其持有的股份占股份有限公司股本总额百分之五十以上的股东；出资额或者持有股份的比例虽然不足百分之五十，但依其出资额或者持有的股份所享有的表决权已足以对股东会、股东大会的决议产生重大影响的股东。',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '实际控制人',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '通过投资关系、协议或者其他安排，能够实际支配公司行为的人，实际控制上市公司的自然人、法人或其他组织。',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '实际控制人性质',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '主营业务',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '公司为完成其经营目标所从事的经常性活动所实现的业务。',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属申万行业',
    'data_type': 'STRING',
    'optional_value': '农林牧渔 种植业 种子 粮食种植 其他种植业 食用菌 渔业 海洋捕捞 水产养殖 林业Ⅱ 林业Ⅲ 饲料 畜禽饲料 水产饲料 宠物食品 农产品加工 果蔬加工 粮油加工 其他农产品加工 养殖业 生猪养殖 肉鸡养殖 其他养殖 动物保健Ⅱ 动物保健Ⅲ 农业综合Ⅱ 农业综合Ⅲ 基础化工 化学原料 纯碱 氯碱 无机盐 其他化学原料 煤化工 钛白粉 化学制品 涂料油墨 民爆制品 纺织化学制品 其他化学制品 氟化工 聚氨酯 食品及饲料添加剂 有机硅 胶黏剂及胶带 化学纤维 涤纶 粘胶 其他化学纤维 氨纶 锦纶 塑料 其他塑料制品 改性塑料 合成树脂 膜材料 橡胶 其他橡胶制品 炭黑 橡胶助剂 农化制品 氮肥 磷肥及磷化工 农药 钾肥 复合肥 非金属材料Ⅱ 非金属材料Ⅲ 钢铁 冶钢原料 铁矿石 冶钢辅料 普钢 长材 板材 钢铁管材 特钢Ⅱ 特钢Ⅲ 有色金属 金属新材料 其他金属新材料 磁性材料 工业金属 铝 铜 铅锌 贵金属 黄金 白银 小金属 稀土 钨 其他小金属 钼 能源金属 钴 镍 锂 电子 半导体 分立器件 半导体材料 数字芯片设计 模拟芯片设计 集成电路制造 集成电路封测 半导体设备 元件 印制电路板 被动元件 光学光电子 面板 LED 光学元件 其他电子Ⅱ 其他电子Ⅲ 消费电子 品牌消费电子 消费电子零部件及组装 电子化学品Ⅱ 电子化学品Ⅲ 汽车 汽车零部件 车身附件及饰件 底盘与发动机系统 轮胎轮毂 其他汽车零部件 汽车电子电气系统 汽车服务 汽车经销商 汽车综合服务 摩托车及其他 其他运输设备 摩托车 乘用车 电动乘用车 综合乘用车 商用车 商用载货车 商用载客车 家用电器 白色家电 空调 冰洗 黑色家电 彩电 其他黑色家电 小家电 厨房小家电 清洁小家电 个护小家电 厨卫电器 厨房电器 卫浴电器 照明设备Ⅱ 照明设备Ⅲ 家电零部件Ⅱ 家电零部件Ⅲ 其他家电Ⅱ 其他家电Ⅲ 食品饮料 食品加工 肉制品 其他食品 预加工食品 保健品 白酒Ⅱ 白酒Ⅲ 非白酒 啤酒 其他酒类 饮料乳品 软饮料 乳品 休闲食品 零食 烘焙食品 熟食 调味发酵品Ⅱ 调味发酵品Ⅲ 纺织服饰 纺织制造 棉纺 印染 辅料 其他纺织 纺织鞋类制造 服装家纺 鞋帽及其他 家纺 运动服装 非运动服装 饰品 钟表珠宝 多品类奢侈品 其他饰品 轻工制造 造纸 大宗用纸 特种纸 包装印刷 印刷 金属包装 塑料包装 纸包装 综合包装 家居用品 瓷砖地板 成品家居 定制家居 卫浴制品 其他家居用品 文娱用品 文化用品 娱乐用品 医药生物 化学制药 原料药 化学制剂 中药Ⅱ 中药Ⅲ 生物制品 血液制品 疫苗 其他生物制品 医药商业 医药流通 线下药店 互联网药店 医疗器械 医疗设备 医疗耗材 体外诊断 医疗服务 诊断服务 医疗研发外包 医院 其他医疗服务 公用事业 电力 火力发电 水力发电 热力服务 光伏发电 风力发电 核力发电 其他能源发电 电能综合服务 燃气Ⅱ 燃气Ⅲ 交通运输 物流 原材料供应链服务 中间产品及消费品供应链服务 快递 跨境物流 仓储物流 公路货运 铁路公路 高速公路 公交 铁路运输 航空机场 航空运输 机场 航运港口 航运 港口 房地产 房地产开发 住宅开发 商业地产 产业地产 房地产服务 物业管理 房产租赁经纪 房地产综合服务 商贸零售 贸易Ⅱ 贸易Ⅲ 一般零售 百货 超市 多业态零售 商业物业经营 专业连锁Ⅱ 专业连锁Ⅲ 互联网电商 综合电商 跨境电商 电商服务 旅游零售Ⅱ 旅游零售Ⅲ 社会服务 体育Ⅱ 体育Ⅲ 本地生活服务Ⅱ 本地生活服务Ⅲ 专业服务 人力资源服务 检测服务 会展服务 其他专业服务 酒店餐饮 酒店 餐饮 旅游及景区 博彩 人工景区 自然景区 旅游综合 教育 学历教育 培训教育 教育运营及其他 银行 国有大型银行Ⅱ 国有大型银行Ⅲ 股份制银行Ⅱ 股份制银行Ⅲ 城商行Ⅱ 城商行Ⅲ 农商行Ⅱ 农商行Ⅲ 其他银行Ⅱ 其他银行Ⅲ 非银金融 证券Ⅱ 证券Ⅲ 保险Ⅱ 保险Ⅲ 多元金融 金融控股 期货 信托 租赁 金融信息服务 资产管理 其他多元金融 综合 综合Ⅱ 综合Ⅲ 建筑材料 水泥 水泥制造 水泥制品 玻璃玻纤 玻纤制造 玻璃制造 装修建材 耐火材料 管材 其他建材Ⅲ 防水材料 涂料 建筑装饰 房屋建设Ⅱ 房屋建设Ⅲ 装修装饰Ⅱ 装修装饰Ⅲ 基础建设 基建市政工程 园林工程 专业工程 钢结构 化学工程 国际工程 其他专业工程 工程咨询服务Ⅱ 工程咨询服务Ⅲ 电力设备 电机Ⅱ 电机Ⅲ 其他电源设备Ⅱ 综合电力设备商 火电设备 其他能源设备Ⅲ 光伏设备 硅料硅片 光伏电池组件 逆变器 光伏辅材 光伏加工设备 风电设备 风电整机 风电零部件 电池 锂电池 电池化学品 锂电专用设备 燃料电池 蓄电池及其他电池 电网设备 输变电设备 配电设备 电网自动化设备 电工仪器仪表 线缆部件及其他 机械设备 通用设备 机床工具 磨具磨料 制冷空调设备 其他通用设备 仪器仪表 金属制品 专用设备 能源及重型设备 楼宇设备 纺织服装设备 农用机械 印刷包装机械 其他专用设备 轨交设备Ⅱ 轨交设备Ⅲ 工程机械 工程机械整机 工程机械器件 自动化设备 机器人 工控设备 激光设备 其他自动化设备 国防军工 航天装备Ⅱ 航天装备Ⅲ 航空装备Ⅱ 航空装备Ⅲ 地面兵装Ⅱ 地面兵装Ⅲ 航海装备Ⅱ 航海装备Ⅲ 军工电子Ⅱ 军工电子Ⅲ 计算机 计算机设备 安防设备 其他计算机设备 IT服务Ⅱ IT服务Ⅲ 软件开发 垂直应用软件 横向通用软件 传媒 游戏Ⅱ 游戏Ⅲ 广告营销 营销代理 广告媒体 影视院线 影视动漫制作 院线 数字媒体 视频媒体 音频媒体 图片媒体 门户网站 文字媒体 其他数字媒体 社交Ⅱ 社交Ⅲ 出版 教育出版 大众出版 其他出版 电视广播Ⅱ 电视广播Ⅲ 通信 通信服务 电信运营商 通信工程及服务 通信应用增值服务 通信设备 通信网络设备及器件 通信线缆及配套 通信终端及配件 其他通信设备 煤炭 煤炭开采 动力煤 焦煤 焦炭Ⅱ 焦炭Ⅲ 石油石化 油气开采Ⅱ 油气开采Ⅲ 油服工程 油田服务 油气及炼化工程 炼化及贸易 炼油化工 油品石化贸易 其他石化 环保 环境治理 大气治理 水务及水治理 固废治理 综合环境治理 环保设备Ⅱ 环保设备Ⅲ 美容护理 个护用品 生活用纸 洗护用品 化妆品 化妆品制造及其他 品牌化妆品 医疗美容 医美耗材 医美服务',
    'description': '按照2021年最新申万行业分类标准，公司所属行业类别。',
    'mapper_fields': '[{"label":"最新","value":"newest","title":"次数"},{"label": "全部", "value": 3, "title": "行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属申万行业代码',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '按照2021年最新申万行业分类标准，公司所属行业代码',
    'mapper_fields': '[{"label":"最新","value":"newest","title":"次数"},{"label": "全部", "value": 3, "title": "行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属证监会行业（废止）',
    'data_type': 'STRING',
    'optional_value': '农、林、牧、渔业 农业 林业 畜牧业 渔业 农、林、牧、渔服务业 采矿业 煤炭开采和洗选业 石油和天然气开采业 黑色金属矿采选业 有色金属矿采选业 非金属矿采选业 开采辅助活动 其他采矿业 制造业 农副食品加工业 食品制造业 酒、饮料和精制茶制造业 烟草制品业 纺织业 纺织服装、服饰业 皮革、毛皮、羽毛及其制品和制鞋业 木材加工及木、竹、藤、棕、草制品业 家具制造业 造纸及纸制品业 印刷和记录媒介复制业 文教、工美、体育和娱乐用品制造业 石油加工、炼焦及核燃料加工业 化学原料及化学制品制造业 医药制造业 化学纤维制造业 橡胶和塑料制品业 非金属矿物制品业 黑色金属冶炼和压延加工业 有色金属冶炼和压延加工业 金属制品业 通用设备制造业 专用设备制造业 汽车制造业 铁路、船舶、航空航天和其他运输设备制造业 电气机械和器材制造业 计算机、通信和其他电子设备制造业 仪器仪表制造业 其他制造业 废弃资源综合利用业 金属制品、机械和设备修理业 电力、热力、燃气及水生产和供应业 电力、热力生产和供应业 燃气生产和供应业 水的生产和供应业 建筑业 房屋建筑业 土木工程建筑业 建筑安装业 建筑装饰和其他建筑业 批发和零售业 批发业 零售业 交通运输、仓储和邮政业 铁路运输业 道路运输业 水上运输业 航空运输业 管道运输业 装卸搬运和运输代理业 仓储业 邮政业 住宿和餐饮业 住宿业 餐饮业 信息传输、软件和信息技术服务业 电信、广播电视和卫星传输服务 互联网和相关服务 软件和信息技术服务业 金融业 货币金融服务 资本市场服务 保险业 其他金融业 房地产业 房地产业 租赁和商务服务业 租赁业 商务服务业 科学研究和技术服务业 研究和试验发展 专业技术服务业 科技推广和应用服务业 水利、环境和公共设施管理业 水利管理业 生态保护和环境治理业 公共设施管理业 居民服务、修理和其他服务业 居民服务业 机动车、电子产品和日用产品修理业 其他服务业 教育 教育 卫生和社会工作 卫生 社会工作 文化、体育和娱乐业 新闻和出版业 广播、电视、电影和影视录音制作业 文化艺术业 体育 娱乐业 综合 综合',
    'description': None,
    'mapper_fields': '[{"label": "全部明细", "value": 2, "title": "行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属证监会行业（废止）代码',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "全部明细", "value": 2, "title": "行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属中上协行业（新）',
    'data_type': 'STRING',
    'optional_value': '农、林、牧、渔业 农业 林业 畜牧业 渔业 农、林、牧、渔专业及辅助性活动 采矿业 煤炭开采和洗选业 石油和天然气开采业 黑色金属矿采选业 有色金属矿采选业 非金属矿采选业 开采专业及辅助性活动 其他采矿业 制造业 食品、饮料、烟草 农副食品加工业 食品制造业 酒、饮料和精制茶制造业 烟草制品业 纺织、服装、鞋帽 纺织业 纺织服装、服饰业 皮革、毛皮、羽毛及其制品和制鞋业 木材、家具 木材加工和木、竹、藤、棕、草制品业 家具制造业 造纸、印刷、文教 造纸和纸制品业 印刷和记录媒介复制业 文教、工美、体育和娱乐用品制造业 石油、化学、生物医药 石油、煤炭及其他燃料加工业 化学原料和化学制品制造业 医药制造业 化学纤维制造业 橡胶和塑料制品业 金属、非金属 非金属矿物制品业 黑色金属冶炼和压延加工业 有色金属冶炼和压延加工业 金属制品业 专用、通用及交通运输设备 通用设备制造业 专用设备制造业 汽车制造业 铁路、船舶、航空航天和其他运输设备制造业 电气、电子及通讯 电气机械和器材制造业 计算机、通信和其他电子设备制造业 仪器、仪表 仪器仪表制造业 其他制造业 其他制造业 废弃资源综合利用业 金属制品、机械和设备修理业 电力、热力、燃气及水生产和供应业 电力、热力生产和供应业 燃气生产和供应业 水的生产和供应业 建筑业 房屋建筑业 土木工程建筑业 建筑安装业 建筑装饰、装修和其他建筑业 批发和零售业 批发业 零售业 交通运输、仓储和邮政业 铁路运输业 道路运输业 水上运输业 航空运输业 管道运输业 多式联运和运输代理业 装卸搬运和仓储业 邮政业 住宿和餐饮业 住宿业 餐饮业 信息传输、软件和信息技术服务业 电信、广播电视和卫星传输服务 互联网和相关服务 软件和信息技术服务业 金融业 货币金融服务 资本市场服务 保险业 其他金融业 房地产业 房地产业 租赁和商务服务业 租赁业 商务服务业 科学研究和技术服务业 研究和试验发展 专业技术服务业 科技推广和应用服务业 水利、环境和公共设施管理业 水利管理业 生态保护和环境治理业 公共设施管理业 土地管理业 居民服务、修理和其他服务业 居民服务业 机动车、电子产品和日用产品修理业 其他服务业 教育 教育 卫生和社会工作 卫生 社会工作 文化、体育和娱乐业 新闻和出版业 广播、电视、电影和录音制作业 文化艺术业 体育 娱乐业 综合 综合',
    'description': None,
    'mapper_fields': '[{"label":"最新","value":"newest","title":"次数"},{"label":"全部明细","value":3,"title":"行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所属中上协行业（新）代码',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label":"最新","value":"newest","title":"次数"},{"label":"全部明细","value":3,"title":"行业类别"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '科创板战略新兴行业',
    'data_type': 'STRING',
    'optional_value': '新一代信息技术产业 高端装备制造产业 新材料产业 生物产业 新能源汽车产业 新能源产业 节能环保产业 数字创意产业 相关服务业 下一代信息网络产业 电子核心产业 新兴软件和新型信息技术服务 互联网与云计算、大数据服务 人工智能 智能制造装备产业 航空装备产业 卫星及应用产业 轨道交通装备产业 海洋工程装备产业  先进钢铁材料 先进有色金属材料 先进石化化工新材料 先进无机非金属材料 高性能纤维及制品和复合材料 前沿新材料 新材料相关服务 生物医药产业 生物医学工程产业 生物农业及相关产业 生物质能产业 其他生物业 新能源汽车整车制造 新能源汽车装置、配件制造 新能源汽车相关设施制造 新能源汽车相关服务 核电产业 风能产业 太阳能产业 生物质能及其他新能源产业 智能电网产业 高效节能产业 先进环保产业 资源循环利用产业 数字创意技术设备制造 数字文化创意活动 设计服务 数字创意与融合服务 新技术与创新创业服务 其他相关服务 网络设备制造 新型计算机及信息终端设备制造 信息安全设备制造 新一代移动通信网络服务 其他网络运营服务 计算机和辅助设备修理 新型电子元器件及设备制造 电子专用设备仪器制造 高储能和关键电子材料制造 集成电路制造 新兴软件开发 网络与信息安全软件开发 互联网安全服务 新型信息技术服务 工业互联网及支持服务 互联网平台服务(互联网+) 云计算与大数据服务 互联网相关信息服务 人工智能软件开发 智能消费相关设备制造 人工智能系统服务 机器人与增材设备制造 重大成套设备制造 智能测控装备制造 其他智能设备制造 智能关键基础零部件制造 智能制造相关服务 航空器装备制造 其他航空装备制造及相关服务  卫星装备制造 卫星应用技术设备制造 卫星应用服务 其他航天器及运载火箭制造 铁路高端装备制造 城市轨道装备制造 其他轨道交通装备制造 轨道交通相关服务 海洋工程装备制造 深海石油钻探设备制造 其他海洋相关设备与产品制造 海洋环境监测与探测装备制造 海洋工程建筑及相关服务 先进制造基础零部件用钢制造 高技术船舶及海洋工程用钢加工 先进轨道交通用钢加工 新型高强塑汽车钢加工 能源用钢加工 能源油气钻采集储用钢加工 石化压力容器用钢加工 新一代功能复合化建筑用钢加工 高性能工程、矿山及农业机械用钢加工 高品质不锈钢及耐蚀合金加工 其他先进钢铁材料制造 先进钢铁材料制品制造 铝及铝合金制造 铜及铜合金制造 钛及钛合金制造 镁及镁合金制造 稀有金属材料制造 贵金属材料制造 稀土新材料制造 硬质合金及制品制造 其他有色金属材料制造 高性能塑料及树脂制造 聚氨酯材料及原料制造 氟硅合成材料制造 高性能橡胶及弹性体制造 高性能膜材料制造 专用化学品及材料制造 新型功能涂层材料制造 生物基合成材料制造 生命基高分子材料及功能化合物制造 其他化工新材料制造 特种玻璃制造 特种陶瓷制造 人工晶体制造 新型建筑材料制造 矿物功能材料制造 高性能纤维及制品制造 高性能纤维复合材料制造 其他高性能复合材料制造 3D打印用材料制造 超导材料制造 智能、仿生与超材料制造 纳米材料制造 生物医用材料制造 液态金属制造 新材料研发与设计服务 质检技术服务 科技推广和应用服务 生物药品制品制造 化学药品与原料药制造 现代中药与民族药制造 生物医药关键装备与原辅料制造 生物医药相关服务 先进医疗设备及器械制造 植介入生物医用材料及设备制造 其他生物医用材料及用品制造 生物医学工程信息技术服务 生物医学工程相关服务 生物育种 生物农药制造 生物肥料制造 生物饲料制造 生物兽药、兽用生物制品及疫苗制造 生物农业相关服务 生物相关原料供应体系活动 生物质燃料加工 生物质能相关服务 生物基材料制造 生物化工制品制造 生物酶等发酵制品制造 海洋生物制品制造 其他生物工程相关设备制造 其他生物业相关服务 新能源汽车整车制造 电机、发动机制造 新能源汽车储能装置制造 新能源汽车零部件配件制造 供能装置制造 试验装置制造 其他相关设施制造 新能源汽车充电及维修服务 新能源汽车其他相关服务 核燃料加工及设备制造 核电装备制造 核电运营维护 核电工程施工 核电工程技术服务 风能发电机装备及零部件制造 风能发电其他相关装备及材料制造 风能发电运营维护 风能发电工程施工 风能发电工程技术服务 太阳能设备和生产装备制造 太阳能材料制造 太阳能发电运营维护 太阳能工程施工 太阳能工程技术服务 生物质能及其他新能源设备制造 生物质能发电 生物质供热 生物质燃气生产和供应 生物质能工程施工 生物质能工程技术服务 其他新能源运营服务 智能电力控制设备及电缆制造 电力电子基础元器件制造 智能电网输送与配电 高效节能通用设备制造 高效节能专用设备制造 高效节能电气机械器材制造 高效节能工业控制装置制造 绿色节能建筑材料制造 节能工程施工 节能研发与技术服务 环境保护专用设备制造 环境保护监测仪器及电子设备制造 环境污染处理药剂材料制造 环境评估与监测服务 环境保护及污染治理服务 环保工程施工 环保研发与技术服务 矿产资源与工业废弃资源利用设备制造 矿产资源综合利用 工业固体废物、废气、废液回收和资源化利用  城乡生活垃圾与农林废弃资源利用设备制造 城乡生活垃圾综合利用 农林废弃物资源化利用 水及海水资源利用设备制造 水资源循环利用与节水活动 海水淡化活动 数字创意技术 数字文化创意软件开发 数字文化创意内容制作服务 新型媒体服务 数字文化创意广播电视服务 其他数字文化创意活动 数字设计服务 数字创意与融合服务 研发服务 检验检测认证服务 标准化服务 其他专业技术服务 知识产权及相关服务 创新创业服务 其他技术推广服务 航空运营及支持服务 现代金融服务 高性能轴承用钢加工 高性能齿轮用钢加工 高应力弹簧钢加工 高强度紧固件用钢加工 高性能工具模具钢加工 机床专用钢加工 线材制品用钢加工 高技术船舶用钢加工 海洋工程用钢加工 车轮用钢加工 钢轨用钢加工 车轴用钢加工 转向架用钢加工 车体用钢加工 高强度汽车用冷轧板加工 先进超高强度板及其镀层板加工 核电用钢加工 超超临界火电用钢加工 高性能电工钢加工 电池壳用钢加工 高性能油气钻采用钢加工 高性能油气输送用钢加工 高温压力容器用钢加工 低温压力容器用钢加工 高强耐火耐候房屋建筑钢加工 桥梁用钢加工 沿海建筑用钢加工 高强钢加工 高耐磨钢加工 高品质不锈钢加工 耐蚀合金加工 高温合金制造 超高强度钢加工 先进钢铁材料铸件制造 先进钢铁材料锻件制造 优质焊接材料制造 高性能丝绳制品制造 高性能金属密封材料制造 高品质不锈钢制品制造 新型铝合金制造 高品质铝铸件制造 高品质铝材制造 高品质铝锻件制造 新型铜及铜合金制造 高品质铜铸件制造 高品质铜材制造 铜合金锻件产品制造 高品质钛铸件制造 高品质钛材制造 高品质钛锻件制造 高品质镁铸件制造 高品质镁材制造 镁合金锻件产品制造 钨钼材料制造 钽铌材料制造 锆铪材料制造 其他稀有金属材料制造 贵金属催化材料制造 新型电接触贵金属材料制造 电子浆料制造 高品质贵金属加工材料制造 稀土磁性材料制造 稀土光功能材料制造 稀土催化材料制造 稀土储氢材料制造 稀土抛光材料制造 稀土陶瓷材料制造 稀土特种合金制造 特殊物性稀土化合物制造 高纯稀土化合物制造 高纯稀土金属及制品制造 稀土助剂制造 超细晶硬质合金切削刀片类制造 超大晶粒硬质合金矿用合金制造 耐磨零件制造 硬质合金棒材制造 硬面合金与陶瓷粉料与丝材制造 其他硬质合金制造 高纯金属制造 高性能靶材制造 粉末、泡沫及多孔材料制造 稀有金属涂层材料制造 锑系催化、阻燃材料制造 锡材料制造 锌及锌合金材料制造 薄膜材料(金属薄膜)制造 工程塑料制造 高端聚烯烃塑料制造 其他高性能树脂制造 高分子光、电、磁材料制造 聚氨酯材料及原料制造 合成氟树脂制造 氟制冷剂制造 其他含氟烷烃制造 有机硅环体制造 合成硅材料制造 特种橡胶制造 氟硅合成橡胶制造 弹性体制造 水处理用膜制造 离子交换膜产品制造 特种分离膜制造 电池膜制造 光学膜制造 光伏用膜制造 其他新型膜材料制造 专用化学品及材料制造 涂料制造 油墨制造 颜料制造 染料制造 生物基原料制造 生物基聚合物制造 单体材料制造 聚合物制造 二次电池材料制造 高性能有机密封材料制造 新型催化材料及助剂制造 特种玻璃制品制造 技术玻璃制品制造 结构陶瓷制造 功能陶瓷制造 半导体晶体制造 其他人工晶体制造 水泥基材料制造 新型墙体材料制造 新型建筑防水材料制造 隔热隔音材料制造 轻质建筑材料制造 环境处置功能材料制造 节能、密封、保温材料制造 新能源材料制造 功能性填料制造 其他矿物功能材料制造 新型耐火材料制造 玻璃纤维及制品制造 高性能碳纤维及制品制造 石墨纤维制造 陶瓷纤维及制品制造 有机纤维制造 生物基化学纤维制造 高性能热固性树脂基复合材料制造 高性能热塑性树脂基复合材料制造 金属基复合材料制造 陶瓷基复合材料制造 碳碳复合材料制造 其他结构复合材料制造 金属增材制造专用材料制造 非金属增材制造专用材料制造 医用增材制造专用材料制造 高场超导磁体用材料制造 超导电力用材料制造 超导电力及磁体材料制造 智能响应材料制造 仿生材料制造 超材料制造 碳基纳米材料制造 无机纳米材料制造 金属纳米材料制造 高分子纳米复合材料制造 纳米催化剂材料制造 生物医用材料制造 液态金属制造 研发与设计服务 质检技术服务 科技推广和应用服务',
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '省份',
    'data_type': 'STRING',
    'optional_value': '上海 云南 内蒙古 北京 台湾 吉林 四川 天津 宁夏 安徽 山东 山西 广东 广西 新疆 江苏 江西 河北 河南 浙江 海南 湖北 湖南 澳门特别行政区 甘肃 福建 西藏 贵州 辽宁 重庆 陕西 青海 香港特别行政区 黑龙江',
    'description': '公司注册地所在省份。',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '城市',
    'data_type': 'STRING',
    'optional_value': '七台河市 三亚市 三明市 三沙市 三门峡市 上海市 上饶市 东莞市 东营市 中卫市 中山市 临夏回族自治州 临汾市 临沂市 临沧市 丹东市 丽水市 丽江市 乌兰察布市 乌海市 乌鲁木齐市 乐山市 九江市 云浮市 亳州市 伊春市 伊犁哈萨克自治州 佛山市 佳木斯市 保定市 保山市 信阳市 儋州市 克孜勒苏柯尔克孜自治州 克拉玛依市 六安市 六盘水市 兰州市 兴安盟 内江市 凉山彝族自治州 包头市 北京市 北海市 十堰市 南京市 南充市 南宁市 南平市 南昌市 南通市 南阳市 博尔塔拉蒙古自治州 厦门市 双鸭山市 台州市 合肥市 吉安市 吉林市 吐鲁番市 吕梁市 吴忠市 周口市 呼伦贝尔市 呼和浩特市 和田地区 咸宁市 咸阳市 哈密市 哈尔滨市 唐山市 商丘市 商洛市 喀什地区 嘉兴市 嘉峪关市 四平市 固原市 塔城地区 大兴安岭地区 大同市 大庆市 大理白族自治州 大连市 天水市 天津市 太原市 威海市 娄底市 孝感市 宁德市 宁波市 安庆市 安康市 安阳市 安顺市 定西市 宜宾市 宜昌市 宜春市 宝鸡市 宣城市 宿州市 宿迁市 山南地区 山南市 岳阳市 崇左市 巴中市 巴彦淖尔市 巴音郭楞蒙古自治州 常州市 常德市 平凉市 平顶山市 广元市 广安市 广州市 庆阳市 廊坊市 延安市 延边朝鲜族自治州 开封市 张家口市 张家界市 张掖市 徐州市 德宏傣族景颇族自治州 德州市 德阳市 忻州市 怀化市 怒江傈僳族自治州 恩施土家族苗族自治州 惠州市 成都市 扬州市 承德市 抚州市 抚顺市 拉萨市 揭阳市 攀枝花市 文山壮族苗族自治州 新乡市 新余市 无锡市 日喀则地区 日照市 昆明市 昌吉回族自治州 昌都地区 昌都市 昭通市 晋中市 晋城市 普洱市 景德镇市 曲靖市 朔州市 朝阳市 本溪市 来宾市 杭州市 松原市 林芝地区 林芝市 果洛藏族自治州 枣庄市 柳州市 株洲市 桂林市 梅州市 梧州市 楚雄彝族自治州 榆林市 武威市 武汉市 毕节市 永州市 汉中市 汕头市 汕尾市 江门市 池州市 沈阳市 沧州市 河池市 河源市 泉州市 泰安市 泰州市 泸州市 洛阳市 济南市 济宁市 海东市 海北藏族自治州 海南藏族自治州 海口市 海西蒙古族藏族自治州 淄博市 淮北市 淮南市 淮安市 深圳市 清远市 温州市 渭南市 湖州市 湘潭市 湘西土家族苗族自治州 湛江市 滁州市 滨州市 漯河市 漳州市 潍坊市 潮州市 濮阳市 烟台市 焦作市 牡丹江市 玉林市 玉树藏族自治州 玉溪市 珠海市 甘南藏族自治州 甘孜藏族自治州 白城市 白山市 白银市 百色市 益阳市 盐城市 盘锦市 省直辖县级行政区划 眉山市 石嘴山市 石家庄市 福州市 秦皇岛市 红河哈尼族彝族自治州 绍兴市 绥化市 绵阳市 聊城市 肇庆市 自治区直辖县级行政区划 自贡市 舟山市 芜湖市 苏州市 茂名市 荆州市 荆门市 莆田市 莱芜市 菏泽市 萍乡市 营口市 葫芦岛市 蚌埠市 衡水市 衡阳市 衢州市 襄阳市 西双版纳傣族自治州 西宁市 西安市 许昌市 贵港市 贵阳市 贺州市 资阳市 赣州市 赤峰市 辽源市 辽阳市 达州市 运城市 连云港市 迪庆藏族自治州 通化市 通辽市 遂宁市 遵义市 邢台市 那曲地区 那曲市 邯郸市 邵阳市 郑州市 郴州市 鄂尔多斯市 鄂州市 酒泉市 重庆市 金华市 金昌市 钦州市 铁岭市 铜仁市 铜川市 铜陵市 银川市 锡林郭勒盟 锦州市 镇江市 长春市 长沙市 长治市 阜新市 阜阳市 防城港市 阳江市 阳泉市 阿克苏地区 阿勒泰地区 阿坝藏族羌族自治州 阿拉善盟 阿里地区 陇南市 随州市 雅安市 青岛市 鞍山市 韶关市 马鞍山市 驻马店市 鸡西市 鹤壁市 鹤岗市 鹰潭市 黄冈市 黄南藏族自治州 黄山市 黄石市 黑河市 黔东南苗族侗族自治州 黔南布依族苗族自治州 黔西南布依族苗族自治州 齐齐哈尔市 龙岩市',
    'description': '公司注册地所在城市',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '区县信息',
    'data_type': 'STRING',
    'optional_value': '丁青县 七星关区 七星区 七里河区 万全区 万宁市 万安县 万山区 万州区 万年县 万柏林区 万源市 万秀区 万荣县 万载县 三元区 三原县 三台县 三山区 三水区 三江侗族自治县 三河市 三穗县 三都水族自治县 三门县 上党区 上城区 上思县 上杭县 上林县 上栗县 上犹县 上甘岭区 上蔡县 上虞区 上虞市 上街区 上饶县 上高县 下花园区 下陆区 且末县 丘北县 丛台区 东丰县 东丽区 东乌珠穆沁旗 东乡区 东乡族自治县 东光县 东兰县 东兴区 东兴市 东区 东台市 东坡区 东城区 东宁县 东宁市 东安区 东安县 东宝区 东山区 东山县 东川区 东平县 东方市 东昌区 东昌府区 东明县 东河区 东洲区 东海县 东港区 东港市 东湖区 东源县 东胜区 东至县 东营区 东西湖区 东辽县 东阳市 东阿县 东风区 两当县 个旧市 中原区 中宁县 中山区 中方县 中江县 中沙群岛的岛礁及其海域 中牟县 中站区 中阳县 丰南区 丰县 丰台区 丰城市 丰宁满族自治县 丰泽区 丰润区 丰满区 丰镇市 丰顺县 临县 临城县 临夏县 临夏市 临安区 临安市 临川区 临平区 临朐县 临桂区 临武县 临江市 临沭县 临河区 临泉县 临泽县 临洮县 临海市 临淄区 临清市 临渭区 临湘市 临漳县 临潭县 临潼区 临澧县 临猗县 临翔区 临西县 临邑县 临颍县 临高县 丹凤县 丹寨县 丹巴县 丹徒区 丹棱县 丹江口市 丹阳市 乃东区 久治县 义乌市 义县 义安区 义马市 乌什县 乌伊岭区 乌兰县 乌兰浩特市 乌审旗 乌尔禾区 乌当区 乌恰县 乌拉特中旗 乌拉特前旗 乌拉特后旗 乌苏市 乌达区 乌马河区 乌鲁木齐县 乐业县 乐东黎族自治县 乐亭县 乐安县 乐平市 乐昌市 乐清市 乐至县 乐都区 乐陵市 九原区 九台区 九台市 九寨沟县 九江县 九龙县 九龙坡区 习水县 乡城县 乡宁县 乳山市 乳源瑶族自治县 乾县 乾安县 二七区 二连浩特市 二道区 二道江区 于洪区 于田县 于都县 云冈区 云县 云和县 云城区 云安区 云岩区 云州区 云梦县 云溪区 云霄县 云龙区 云龙县 互助土族自治县 五华区 五华县 五原县 五台县 五大连池市 五家渠市 五寨县 五峰土家族自治县 五常市 五指山市 五河县 五莲县 五营区 五通桥区 井冈山市 井研县 井陉县 井陉矿区 亚东县 交口县 交城县 京口区 京山县 京山市 亭湖区 什邡市 仁化县 仁和区 仁寿县 仁布县 仁怀市 介休市 从化区 从化市 从江县 仓山区 仙居县 仙桃市 仙游县 代县 仪征市 仪陇县 仲巴县 任丘市 任县 任城区 任泽区 伊吾县 伊宁县 伊宁市 伊川县 伊州区 伊春区 伊美区 伊通满族自治县 伊金霍洛旗 伍家岗区 休宁县 会东县 会同县 会宁县 会昌县 会泽县 会理市 伽师县 余姚市 余干县 余庆县 余杭区 余江区 佛冈县 佛坪县 佳县 依兰县 依安县 侯马市 保亭黎族苗族自治县 保康县 保德县 保靖县 信丰县 信宜市 信州区 修文县 修武县 修水县 偃师区 偏关县 元宝区 元宝山区 元氏县 元江哈尼族彝族傣族自治县 元谋县 元阳县 光山县 光明区 光泽县 克东县 克什克腾旗 克山县 克拉玛依区 兖州区 兖州市 全南县 全州县 全椒县 八公山区 八宿县 八步区 公主岭市 公安县 六合区 六枝特区 兰坪白族普米族自治县 兰山区 兰溪市 兰考县 兰西县 兰陵县 共和县 共青城市 关岭布依族苗族自治县 兴业县 兴义市 兴仁县 兴仁市 兴化市 兴县 兴和县 兴国县 兴城市 兴宁区 兴宁市 兴安区 兴安县 兴宾区 兴山区 兴山县 兴平市 兴庆区 兴文县 兴海县 兴隆县 兴隆台区 冀州区 冀州市 内丘县 内乡县 内黄县 册亨县 冕宁县 农安县 冠县 冷水江市 冷水滩区 准格尔旗 凉城县 凉州区 凌云县 凌河区 凌海市 凌源市 凤冈县 凤凰县 凤县 凤台县 凤城市 凤山县 凤庆县 凤泉区 凤翔区 凤阳县 凭祥市 凯里市 分宜县 刚察县 利川市 利州区 利津县 利辛县 利通区 前进区 前郭尔罗斯蒙古族自治县 前锋区 剑川县 剑河县 剑阁县 加查县 务川仡佬族苗族自治县 勃利县 勉县 勐海县 勐腊县 包河区 化州市 化德县 化隆回族自治县 北仑区 北关区 北塔区 北塘区 北安市 北屯市 北川羌族自治县 北市区 北戴河区 北林区 北流市 北湖区 北碚区 北票市 北辰区 北镇市 千山区 千阳县 华亭县 华亭市 华县 华坪县 华宁县 华安县 华容区 华容县 华州区 华池县 华蓥市 华阴市 华龙区 卓尼县 卓资县 单县 南丰县 南丹县 南乐县 南关区 南华县 南县 南召县 南和区 南和县 南城县 南安市 南宫市 南山区 南岔区 南岔县 南岗区 南岳区 南岸区 南川区 南市区 南康区 南康市 南开区 南昌县 南明区 南木林县 南江县 南沙区 南沙群岛 南浔区 南海区 南涧彝族自治县 南湖区 南溪区 南漳县 南澳县 南皮县 南票区 南芬区 南谯区 南郊区 南郑区 南部县 南长区 南陵县 南雄市 南靖县 博乐市 博兴县 博山区 博望区 博湖县 博爱县 博白县 博罗县 博野县 卡若区 卢氏县 卢龙县 卧龙区 卫东区 卫滨区 卫辉市 印台区 印江土家族苗族自治县 即墨区 即墨市 历下区 历城区 原州区 原平市 原阳县 友好区 友谊县 双台子区 双城区 双城市 双塔区 双峰县 双柏县 双桥区 双江拉祜族佤族布朗族傣族自治县 双河市 双流区 双清区 双湖县 双滦区 双牌县 双辽市 双阳区 叙州区 叙永县 叠彩区 古丈县 古交市 古冶区 古县 古城区 古塔区 古浪县 古田县 古蔺县 句容市 召陵区 可克达拉市 台儿庄区 台前县 台安县 台山市 台江区 台江县 右江区 右玉县 叶县 叶城县 叶集区 合作市 合山市 合川区 合水县 合江县 合浦县 合阳县 吉县 吉安县 吉州区 吉木乃县 吉木萨尔县 吉水县 吉阳区 吉隆县 吉首市 同仁县 同安区 同德县 同心县 同江市 名山区 向阳区 君山区 含山县 启东市 吴中区 吴兴区 吴堡县 吴川市 吴桥县 吴江区 吴起县 呈贡区 周宁县 周村区 周至县 呼兰区 呼图壁县 呼玛县 和县 和布克赛尔蒙古自治县 和平区 和平县 和政县 和林格尔县 和田县 和田市 和硕县 和静县 和顺县 和龙市 咸丰县 咸安区 哈巴河县 响水县 唐县 唐河县 商南县 商城县 商州区 商水县 商河县 商都县 喀什市 喀喇沁左翼蒙古族自治县 喀喇沁旗 喜德县 嘉善县 嘉定区 嘉祥县 嘉禾县 嘉荫县 嘉陵区 嘉鱼县 嘉黎县 噶尔县 囊谦县 四会市 四子王旗 四方台区 回民区 团风县 围场满族蒙古族自治县 固始县 固安县 固镇县 固阳县 图们市 图木舒克市 土默特右旗 土默特左旗 坊子区 坡头区 坪山区 垣曲县 垦利区 埇桥区 城东区 城中区 城关区 城北区 城区 城厢区 城固县 城子河区 城步苗族自治县 城西区 城阳区 堆龙德庆区 塔什库尔干塔吉克自治县 塔城市 塔河县 增城区 增城市 墨江哈尼族自治县 墨玉县 墨竹工卡县 墨脱县 壤塘县 壶关县 复兴区 夏县 夏河县 夏津县 夏邑县 多伦县 大东区 大丰区 大丰市 大余县 大关县 大兴区 大冶市 大化瑶族自治县 大厂回族自治县 大同区 大同县 大名县 大城县 大埔县 大姚县 大宁县 大安区 大安市 大悟县 大新县 大方县 大武口区 大洼区 大渡口区 大理市 大田县 大石桥市 大祥区 大竹县 大英县 大荔县 大观区 大足区 大通区 大通回族土族自治县 大邑县 天元区 天全县 天台县 天宁区 天山区 天峨县 天峻县 天心区 天柱县 天桥区 天河区 天涯区 天祝藏族自治县 天等县 天镇县 天长市 天门市 太仆寺旗 太仓市 太和区 太和县 太子河区 太平区 太康县 太湖县 太白县 太谷区 太谷县 头屯河区 夷陵区 夹江县 奇台县 奈曼旗 奉化区 奉化市 奉新县 奉贤区 奎屯市 奎文区 如东县 如皋市 始兴县 姑苏区 姚安县 姜堰区 威信县 威县 威宁彝族回族苗族自治县 威远县 娄星区 娄烦县 婺城区 婺源县 嫩江县 嫩江市 子洲县 子长县 子长市 孙吴县 孝义市 孝南区 孝昌县 孟州市 孟村回族自治县 孟津区 孟连傣族拉祜族佤族自治县 宁乡县 宁乡市 宁化县 宁南县 宁县 宁国市 宁城县 宁安市 宁强县 宁明县 宁晋县 宁武县 宁江区 宁河区 宁津县 宁洱哈尼族彝族自治县 宁海县 宁蒗彝族自治县 宁远县 宁都县 宁阳县 宁陕县 宁陵县 安丘市 安义县 安乡县 安仁县 安化县 安县 安吉县 安国市 安图县 安塞区 安多县 安宁区 安宁市 安定区 安居区 安岳县 安州区 安平县 安新县 安次区 安泽县 安源区 安溪县 安福县 安达市 安远县 安阳县 安陆市 安龙县 宏伟区 宕昌县 官渡区 定兴县 定南县 定安县 定州市 定日县 定海区 定结县 定襄县 定边县 定远县 定陶区 宛城区 宜丰县 宜兴市 宜君县 宜城市 宜宾县 宜川县 宜州区 宜州市 宜秀区 宜章县 宜良县 宜都市 宜阳县 宜黄县 宝丰县 宝兴县 宝坻区 宝塔区 宝安区 宝山区 宝应县 宝清县 宣化区 宣化县 宣威市 宣州区 宣恩县 宣汉县 容县 容城县 宽城区 宽城满族自治县 宽甸满族自治县 宾县 宾川县 宾阳县 宿城区 宿松县 宿豫区 密云区 密山市 富县 富宁县 富川瑶族自治县 富平县 富拉尔基区 富民县 富源县 富蕴县 富裕县 富锦市 富阳区 富阳市 富顺县 寒亭区 察哈尔右翼中旗 察哈尔右翼前旗 察哈尔右翼后旗 察布查尔锡伯自治县 察隅县 察雅县 寻乌县 寻甸回族彝族自治县 寿光市 寿县 寿宁县 寿阳县 封丘县 封开县 射洪县 射洪市 射阳县 将乐县 尉氏县 尉犁县 小店区 小金县 尖山区 尖扎县 尖草坪区 尚义县 尚志市 尤溪县 尧都区 尼勒克县 尼木县 尼玛县 屏南县 屏山县 屏边苗族自治县 屯昌县 屯溪区 屯留区 山丹县 山亭区 山城区 山海关区 山阳区 山阳县 山阴县 岐山县 岑巩县 岑溪市 岗巴县 岚县 岚山区 岚皋县 岢岚县 岫岩满族自治县 岭东区 岱山县 岱岳区 岳塘区 岳普湖县 岳池县 岳西县 岳阳县 岳阳楼区 岳麓区 岷县 峄城区 峡江县 峨山彝族自治县 峨眉山市 峨边彝族自治县 峰峰矿区 崂山区 崆峒区 崇义县 崇仁县 崇信县 崇安区 崇川区 崇州市 崇明区 崇礼区 崇阳县 崖州区 嵊州市 嵊泗县 嵩县 嵩明县 巍山彝族回族自治县 川汇区 巢湖市 工农区 工布江达县 左云县 左权县 左贡县 巧家县 巨野县 巨鹿县 巩义市 巩留县 巴东县 巴南区 巴塘县 巴宜区 巴州区 巴彦县 巴林右旗 巴林左旗 巴楚县 巴里坤哈萨克自治县 巴青县 巴马瑶族自治县 市中区 市北区 市南区 市辖区 布尔津县 布拖县 师宗县 带岭区 常宁市 常山县 常熟市 平乐县 平乡县 平利县 平南县 平原县 平和县 平坝区 平城区 平塘县 平安区 平定县 平山区 平山县 平川区 平度市 平房区 平昌县 平果县 平果市 平桂区 平桥区 平武县 平江县 平泉县 平泉市 平湖市 平潭县 平罗县 平舆县 平谷区 平远县 平遥县 平邑县 平阳县 平阴县 平陆县 平顺县 平鲁区 广丰区 广信区 广南县 广宁县 广安区 广宗县 广平县 广德县 广德市 广昌县 广水市 广汉市 广河县 广灵县 广阳区 广陵区 广饶县 庄河市 庄浪县 庆云县 庆元县 庆城县 庆安县 庐山市 庐江县 庐阳区 库伦旗 库尔勒市 库车县 库车市 应县 应城市 府谷县 康乐县 康保县 康县 康定县 康定市 康巴什区 康平县 康马县 廉江市 延吉市 延寿县 延川县 延平区 延庆区 延津县 延长县 建华区 建始县 建宁县 建安区 建平县 建德市 建昌县 建水县 建湖县 建瓯市 建邺区 建阳区 建阳市 开化县 开原市 开封县 开州区 开平区 开平市 开江县 开福区 开远市 开阳县 开鲁县 弋江区 弋阳县 弓长岭区 张北县 张家川回族自治县 张家港市 张店区 张湾区 弥勒市 弥渡县 当涂县 当阳市 当雄县 彝良县 彬县 彬州市 彭山区 彭州市 彭泽县 彭阳县 彰武县 徐水区 徐汇区 徐闻县 得荣县 循化撒拉族自治县 微山县 德令哈市 德保县 德兴市 德化县 德城区 德安县 德庆县 德惠市 德昌县 德格县 德江县 德清县 德钦县 徽县 徽州区 志丹县 忻城县 忻府区 怀仁县 怀仁市 怀宁县 怀安县 怀来县 怀柔区 怀远县 怀集县 思南县 思明区 思茅区 恒山区 恩平市 恩施市 恩阳区 恭城瑶族自治县 息县 息烽县 惠东县 惠农区 惠城区 惠安县 惠山区 惠来县 惠民县 惠水县 惠济区 惠阳区 慈利县 慈溪市 成华区 成县 成安县 成武县 戚墅堰区 户县 房县 房山区 扎兰屯市 扎囊县 扎赉特旗 扎赉诺尔区 扎鲁特旗 托克托县 托克逊县 托里县 扬中市 扶余市 扶沟县 扶绥县 扶风县 承德县 抚宁区 抚松县 抚远县 抚远市 抚顺县 拉孜县 招远市 拜城县 拜泉县 拱墅区 振兴区 振安区 掇刀区 措勤县 措美县 揭东区 揭西县 播州区 攸县 改则县 政和县 故城县 敖汉旗 敦化市 敦煌市 文县 文圣区 文安县 文山市 文峰区 文成县 文昌市 文水县 文登区 文登市 斗门区 新丰县 新乐市 新乡县 新会区 新兴区 新兴县 新化县 新北区 新华区 新县 新吴区 新和县 新城区 新宁县 新安县 新宾满族自治县 新密市 新巴尔虎右旗 新巴尔虎左旗 新市区 新干县 新平彝族傣族自治县 新建区 新抚区 新昌县 新星市 新晃侗族自治县 新民市 新沂市 新河县 新泰市 新津区 新洲区 新浦区 新源县 新田县 新绛县 新罗区 新荣区 新蔡县 新邱区 新邵县 新郑市 新都区 新野县 新青区 新龙县 方城县 方山县 方正县 施甸县 施秉县 旅顺口区 旌德县 旌阳区 无为县 无为市 无极县 无棣县 日喀则市 日土县 旬邑县 旬阳市 旺苍县 昂仁县 昂昂溪区 昆山市 昆玉市 昆都仑区 昌乐县 昌吉市 昌图县 昌宁县 昌平区 昌江区 昌江黎族自治县 昌邑区 昌邑市 昌都县 昌黎县 明光市 明山区 明水县 明溪县 易县 易门县 昔阳县 星子县 昭化区 昭平县 昭苏县 昭觉县 昭阳区 晋宁区 晋安区 晋州市 晋江市 晋源区 普兰县 普兰店区 普兰店市 普宁市 普安县 普定县 普格县 普陀区 景东彝族自治县 景县 景宁畲族自治县 景泰县 景洪市 景谷傣族彝族自治县 晴隆县 曲周县 曲松县 曲水县 曲江区 曲沃县 曲阜市 曲阳县 曲麻莱县 曹县 曹妃甸区 曾都区 月湖区 朔城区 朗县 望城区 望奎县 望江县 望花区 望谟县 望都县 朝天区 朝阳区 朝阳县 木兰县 木垒哈萨克自治县 木里藏族自治县 未央区 本溪满族自治县 札达县 杂多县 李沧区 杏花岭区 杜尔伯特蒙古族自治县 杜集区 杞县 来凤县 来安县 杨浦区 杨陵区 杭锦后旗 杭锦旗 松北区 松山区 松桃苗族自治县 松江区 松溪县 松滋市 松潘县 松阳县 林口县 林周县 林州市 林甸县 林芝县 林西县 枝江市 枞阳县 枣强县 枣阳市 柏乡县 柘城县 柘荣县 柞水县 柯坪县 柯城区 柯桥区 柳北区 柳南区 柳城县 柳林县 柳江区 柳河县 柴桑区 栖霞区 栖霞市 株洲县 根河市 格尔木市 栾城区 栾川县 桂东县 桂平市 桂阳县 桃城区 桃山区 桃江县 桃源县 桐乡市 桐城市 桐庐县 桐柏县 桐梓县 桑日县 桑植县 桓仁满族自治县 桓台县 桥东区 桥西区 桦南县 桦川县 桦甸市 梁园区 梁子湖区 梁山县 梁平区 梁河县 梁溪区 梅县区 梅江区 梅河口市 梅里斯达斡尔族区 梓潼县 梨树区 梨树县 椒江区 楚雄市 榆中县 榆树市 榆次区 榆社县 榆阳区 榕城区 榕江县 槐荫区 樊城区 樟树市 横山区 横峰县 横州市 歙县 正宁县 正安县 正定县 正蓝旗 正镶白旗 正阳县 武义县 武乡县 武侯区 武冈市 武功县 武城县 武夷山市 武宁县 武安市 武定县 武宣县 武山县 武川县 武平县 武强县 武昌区 武江区 武清区 武穴市 武胜县 武进区 武邑县 武都区 武陟县 武陵区 武陵源区 武隆区 武鸣区 殷都区 比如县 民丰县 民乐县 民勤县 民和回族土族自治县 民权县 水城区 水城县 水富县 水富市 水磨沟区 永丰县 永仁县 永修县 永兴县 永吉县 永和县 永善县 永嘉县 永城市 永宁县 永安市 永定区 永寿县 永川区 永平县 永年区 永康市 永德县 永新县 永昌县 永春县 永泰县 永济市 永清县 永登县 永福县 永胜县 永靖县 永顺县 汇川区 汉南区 汉台区 汉寿县 汉川市 汉源县 汉滨区 汉阳区 汉阴县 汝南县 汝城县 汝州市 汝阳县 江东区 江北区 江华瑶族自治县 江南区 江口县 江城区 江城哈尼族彝族自治县 江夏区 江孜县 江宁区 江安县 江山市 江岸区 江川区 江州区 江永县 江汉区 江油市 江津区 江海区 江源区 江达县 江都区 江阳区 江阴市 江陵县 汤原县 汤旺河区 汤阴县 汨罗市 汪清县 汶上县 汶川县 汾西县 汾阳市 沁县 沁水县 沁源县 沁阳市 沂南县 沂水县 沂源县 沅江市 沅陵县 沈丘县 沈北新区 沈河区 沐川县 沙依巴克区 沙县区 沙坡头区 沙坪坝区 沙市区 沙河口区 沙河市 沙洋县 沙湾区 沙湾市 沙雅县 沛县 沧县 沧源佤族自治县 沭阳县 河东区 河北区 河南蒙古族自治县 河口区 河口瑶族自治县 河曲县 河津市 河西区 河间市 治多县 沽源县 沾化区 沾益区 沿河土家族自治县 沿滩区 泉山区 泉港区 泊头市 泌阳县 法库县 泗县 泗水县 泗洪县 泗阳县 波密县 泰兴市 泰和县 泰宁县 泰山区 泰来县 泰顺县 泸县 泸定县 泸水县 泸水市 泸溪县 泸西县 泽州县 泽库县 泽普县 泾县 泾川县 泾源县 泾阳县 洋县 洛南县 洛宁县 洛川县 洛扎县 洛江区 洛浦县 洛隆县 洛龙区 洞口县 洞头区 津南区 津市市 洪山区 洪江市 洪泽区 洪洞县 洪湖市 洪雅县 洮北区 洮南市 洱源县 浈江区 浉河区 济源市 济阳区 浏阳市 浑南区 浑江区 浑源县 浔阳区 浚县 浠水县 浦东新区 浦北县 浦口区 浦城县 浦江县 浪卡子县 浮山县 浮梁县 海丰县 海伦市 海兴县 海勃湾区 海南区 海原县 海城区 海城市 海宁市 海安县 海安市 海州区 海拉尔区 海晏县 海曙区 海林市 海棠区 海沧区 海淀区 海港区 海珠区 海盐县 海门区 海门市 海阳市 海陵区 涉县 涞水县 涞源县 涟水县 涟源市 涡阳县 润州区 涧西区 涪城区 涪陵区 涵江区 涿州市 涿鹿县 淄川区 淅川县 淇县 淇滨区 淮上区 淮安区 淮滨县 淮阳区 淮阳县 淮阴区 深州市 深泽县 淳化县 淳安县 清丰县 清原满族自治县 清城区 清徐县 清新区 清水县 清水河县 清江浦区 清河区 清河县 清河门区 清流县 清浦区 清涧县 清苑区 清镇市 渌口区 渑池县 渝中区 渝北区 渝水区 渠县 温县 温宿县 温岭市 温江区 温泉县 渭城区 渭源县 渭滨区 港北区 港南区 港口区 港闸区 游仙区 湄潭县 湖口县 湖滨区 湖里区 湘东区 湘乡市 湘桥区 湘潭县 湘阴县 湛河区 湟中区 湟中县 湟源县 湾沚区 湾里区 溆浦县 源城区 源汇区 溧水区 溧阳市 溪湖区 滑县 滕州市 满城区 满洲里市 滦南县 滦县 滦州市 滦平县 滨城区 滨江区 滨海县 滨海新区 滨湖区 滴道区 漠河县 漠河市 漳县 漳平市 漳浦县 漾濞彝族自治县 潍城区 潘集区 潜山县 潜山市 潜江市 潞城区 潞城市 潞州区 潢川县 潮南区 潮安区 潮阳区 潼关县 潼南区 澄城县 澄江县 澄江市 澄海区 澄迈县 澜沧拉祜族自治县 澧县 濂溪区 濉溪县 濠江区 濮阳县 瀍河回族区 灌云县 灌南县 灌阳县 灞桥区 灯塔市 灵丘县 灵台县 灵宝市 灵寿县 灵山县 灵川县 灵武市 灵璧县 灵石县 炉霍县 炎陵县 点军区 烈山区 焉耆回族自治县 爱民区 爱辉区 牙克石市 牟定县 牟平区 牡丹区 牧野区 特克斯县 犍为县 独山县 独山子区 狮子山区 猇亭区 献县 玄武区 玉屏侗族自治县 玉山县 玉州区 玉树市 玉泉区 玉环县 玉环市 玉田县 玉门市 玉龙纳西族自治县 王益区 玛多县 玛曲县 玛沁县 玛纳斯县 环县 环江毛南族自治县 环翠区 珙县 珠山区 珠晖区 班戈县 班玛县 珲春市 琅琊区 理县 理塘县 琼中黎族苗族自治县 琼山区 琼海市 琼结县 瑞丽市 瑞安市 瑞昌市 瑞金市 瑶海区 璧山区 瓜州县 瓦房店市 瓮安县 瓯海区 甘井子区 甘南县 甘孜县 甘州区 甘德县 甘泉县 甘洛县 甘谷县 田东县 田家庵区 田林县 田阳区 田阳县 申扎县 电白区 界首市 留坝县 略阳县 番禺区 疏勒县 疏附县 登封市 白云区 白云鄂博矿区 白塔区 白朗县 白水县 白沙黎族自治县 白河县 白玉县 白碱滩区 白银区 皇姑区 皋兰县 皮山县 盂县 盈江县 盐亭县 盐山县 盐池县 盐津县 盐湖区 盐源县 盐田区 盐边县 盐都区 监利县 监利市 盖州市 盘县 盘山县 盘州市 盘龙区 盱眙县 相城区 相山区 眉县 睢县 睢宁县 睢阳区 石台县 石城县 石屏县 石峰区 石拐区 石景山区 石林彝族自治县 石棉县 石楼县 石河子市 石泉县 石渠县 石狮市 石门县 石阡县 石首市 石鼓区 石龙区 矿区 砀山县 砚山县 硚口区 确山县 碌曲县 碑林区 碧江区 碾子山区 磁县 磐安县 磐石市 磴口县 礼县 礼泉县 社旗县 祁东县 祁县 祁连县 祁门县 祁阳市 神农架林区 神木县 神木市 神池县 祥云县 祥符区 禄丰市 禄劝彝族苗族自治县 禅城区 福安市 福山区 福泉市 福海县 福清市 福田区 福绵区 福贡县 福鼎市 禹会区 禹城市 禹州市 禹王台区 离石区 秀屿区 秀峰区 秀洲区 秀英区 科尔沁区 科尔沁右翼中旗 科尔沁右翼前旗 科尔沁左翼中旗 科尔沁左翼后旗 秦安县 秦州区 秦淮区 秦都区 秭归县 积石山保安族东乡族撒拉族自治县 称多县 稷山县 稻城县 穆棱市 突泉县 立山区 站前区 竞秀区 章丘区 章丘市 章贡区 端州区 竹山县 竹溪县 策勒县 筠连县 简阳市 管城回族区 米东区 米易县 米林县 米脂县 类乌齐县 精河县 索县 紫云苗族布依族自治县 紫金县 紫阳县 綦江区 繁峙县 繁昌区 繁昌县 红原县 红古区 红塔区 红安县 红寺堡区 红山区 红岗区 红旗区 红星区 红桥区 红河县 红花岗区 红谷滩区 纳溪区 纳雍县 细河区 织金县 绍兴县 绛县 绥中县 绥宁县 绥德县 绥棱县 绥江县 绥滨县 绥芬河市 绥阳县 绩溪县 维西傈僳族自治县 绵竹市 绿园区 绿春县 缙云县 罗城仫佬族自治县 罗定市 罗山县 罗平县 罗庄区 罗江区 罗湖区 罗源县 罗田县 罗甸县 美兰区 美姑县 美溪区 翁源县 翁牛特旗 翔安区 翠屏区 翠峦区 翼城县 耀州区 老城区 老河口市 老边区 耒阳市 耿马傣族佤族自治县 聂拉木县 聂荣县 肃北蒙古族自治县 肃南裕固族自治县 肃宁县 肃州区 肇东市 肇州县 肇源县 肥东县 肥乡区 肥城市 肥西县 胡杨河市 胶州市 腾冲县 腾冲市 自流井区 舒兰市 舒城县 舞钢市 舞阳县 舟曲县 船山区 船营区 良庆区 色尼区 色达县 芒市 芒康县 芗城区 芙蓉区 芜湖县 芝罘区 芦山县 芦淞区 芦溪县 芮城县 花垣县 花山区 花溪区 花都区 芷江侗族自治县 苍南县 苍梧县 苍溪县 苏仙区 苏家屯区 苏尼特右旗 苏尼特左旗 若尔盖县 若羌县 英吉沙县 英山县 英德市 茂南区 茂县 茂港区 范县 茄子河区 茅箭区 茌平区 茌平县 茫崖市 茶陵县 荆州区 荔城区 荔波县 荔浦县 荔浦市 荔湾区 荣县 荣成市 荣昌区 荥经县 荥阳市 荷塘区 莎车县 莒南县 莒县 莘县 莫力达瓦达斡尔族自治旗 莱城区 莱山区 莱州市 莱芜区 莱西市 莱阳市 莲池区 莲湖区 莲花县 莲都区 获嘉县 萝北县 萝岗区 营山县 萧县 萧山区 萨嘎县 萨尔图区 萨迦县 蒙城县 蒙山县 蒙自市 蒙阴县 蒲县 蒲城县 蒲江县 蒸湘区 蓝山县 蓝田县 蓟州区 蓬安县 蓬江区 蓬溪县 蓬莱区 蓬莱市 蔚县 蔡甸区 蕉城区 蕉岭县 蕲春县 薛城区 藁城区 藁城市 藤县 虎丘区 虎林市 虞城县 虹口区 蚌山区 蛟河市 蜀山区 融安县 融水苗族自治县 蠡县 行唐县 衡东县 衡南县 衡山县 衡阳县 衢江区 袁州区 裕华区 裕安区 裕民县 襄垣县 襄城区 襄城县 襄州区 襄汾县 西丰县 西乌珠穆沁旗 西乡县 西乡塘区 西充县 西区 西华县 西吉县 西和县 西固区 西城区 西塞山区 西夏区 西安区 西山区 西岗区 西峡县 西峰区 西工区 西市区 西平县 西昌市 西林区 西林县 西沙群岛 西湖区 西畴县 西盟佤族自治县 西秀区 西陵区 西青区 覃塘区 观山湖区 解放区 让胡路区 讷河市 许昌县 诏安县 诸城市 诸暨市 调兵山市 谢家集区 谢通门县 谯城区 谷城县 象山区 象山县 象州县 贞丰县 贡井区 贡嘎县 贡山独龙族怒族自治县 贡觉县 贵南县 贵定县 贵德县 贵池区 贵溪市 费县 贺兰县 贾汪区 资中县 资兴市 资源县 资溪县 资阳区 赛罕区 赞皇县 赣县区 赣榆区 赤坎区 赤城县 赤壁市 赤水市 赫山区 赫章县 赵县 越城区 越秀区 越西县 路北区 路南区 路桥区 轮台县 辉南县 辉县市 辛集市 辰溪县 边坝县 辽中区 辽阳县 达坂城区 达孜区 达尔罕茂明安联合旗 达川区 达拉特旗 达日县 迁安市 迁西县 迎江区 迎泽区 运河区 进贤县 远安县 连云区 连南瑶族自治县 连城县 连山区 连山壮族瑶族自治县 连州市 连平县 连江县 迭部县 逊克县 通化县 通城县 通山县 通川区 通州区 通榆县 通江县 通河县 通海县 通渭县 通许县 通道侗族自治县 遂川县 遂平县 遂昌县 遂溪县 道县 道外区 道孚县 道真仡佬族苗族自治县 道里区 遵义县 遵化市 邓州市 邕宁区 邗江区 邛崃市 邢台县 那坡县 那曲县 邯山区 邯郸县 邱县 邳州市 邵东县 邵东市 邵武市 邵阳县 邹城市 邹平县 邹平市 邻水县 郁南县 郊区 郎溪县 郏县 郓城县 郧县 郧西县 郧阳区 郫都区 郯城县 郸城县 都兰县 都匀市 都安瑶族自治县 都昌县 都江堰市 郾城区 鄂伦春自治旗 鄂城区 鄂托克前旗 鄂托克旗 鄂温克族自治旗 鄄城县 鄞州区 鄠邑区 鄢陵县 鄯善县 鄱阳县 醴陵市 金东区 金乡县 金凤区 金口河区 金台区 金坛区 金坛市 金城江区 金堂县 金塔县 金安区 金寨县 金山区 金山屯区 金川区 金川县 金州区 金平区 金平苗族瑶族傣族自治县 金明区 金水区 金沙县 金湖县 金湾区 金溪县 金牛区 金秀瑶族自治县 金门县 金阳县 钟山区 钟山县 钟楼区 钟祥市 钢城区 钦北区 钦南区 钱塘区 铁东区 铁力市 铁山区 铁山港区 铁岭县 铁西区 铁锋区 铁门关市 铅山县 铜官区 铜官山区 铜山区 铜梁区 铜陵县 铜鼓县 银州区 银海区 错那县 锡山区 锡林浩特市 锦屏县 锦江区 镇原县 镇坪县 镇宁布依族苗族自治县 镇安县 镇巴县 镇平县 镇康县 镇沅彝族哈尼族拉祜族自治县 镇海区 镇赉县 镇远县 镇雄县 镜湖区 镶黄旗 长丰县 长乐区 长乐市 长兴县 长垣县 长垣市 长子县 长宁区 长宁县 长安区 长寿区 长岛县 长岭县 长武县 长汀县 长沙县 长治县 长泰区 长洲区 长海县 长清区 长白朝鲜族自治县 长葛市 长阳土家族自治县 长顺县 门头沟区 门源回族自治县 闵行区 闸北区 闻喜县 闽侯县 闽清县 阆中市 阎良区 阜南县 阜城县 阜宁县 阜平县 阜康市 阜新蒙古族自治县 防城区 阳东区 阳信县 阳原县 阳城县 阳山县 阳新县 阳明区 阳春市 阳曲县 阳朔县 阳西县 阳谷县 阳高县 阿克塞哈萨克族自治县 阿克苏市 阿克陶县 阿勒泰市 阿合奇县 阿图什市 阿坝县 阿城区 阿尔山市 阿巴嘎旗 阿拉善右旗 阿拉善左旗 阿拉尔市 阿拉山口市 阿瓦提县 阿荣旗 阿鲁科尔沁旗 陆丰市 陆川县 陆河县 陆良县 陇县 陇川县 陇西县 陈仓区 陈巴尔虎旗 陕县 陕州区 陵县 陵城区 陵川县 陵水黎族自治县 隆化县 隆回县 隆子县 隆安县 隆尧县 隆德县 隆昌县 隆昌市 隆林各族自治县 隆阳区 随县 隰县 雁塔区 雁山区 雁峰区 雁江区 雄县 雅江县 集宁区 集安市 集美区 集贤县 雨城区 雨山区 雨湖区 雨花区 雨花台区 零陵区 雷山县 雷州市 雷波县 霍城县 霍尔果斯市 霍山县 霍州市 霍林郭勒市 霍邱县 霞山区 霞浦县 霸州市 青云谱区 青冈县 青原区 青县 青山区 青山湖区 青川县 青州市 青河县 青浦区 青田县 青白江区 青神县 青秀区 青羊区 青铜峡市 青阳县 青龙满族自治县 靖宇县 靖安县 靖州苗族侗族自治县 靖江市 靖西县 靖西市 靖边县 靖远县 静乐县 静宁县 静安区 静海区 革吉县 韩城市 韶山市 项城市 顺义区 顺城区 顺平县 顺庆区 顺德区 顺昌县 顺河回族区 颍上县 颍东区 颍州区 颍泉区 额尔古纳市 额敏县 额济纳旗 饶平县 饶河县 饶阳县 馆陶县 香坊区 香格里拉县 香格里拉市 香河县 香洲区 马关县 马尔康县 马尔康市 马尾区 马山县 马村区 马边彝族自治县 马龙区 驿城区 高县 高台县 高唐县 高坪区 高安市 高密市 高州市 高平市 高昌区 高明区 高淳区 高港区 高碑店市 高要区 高要市 高邑县 高邮市 高阳县 高陵区 高青县 魏县 魏都区 鱼台县 鱼峰区 鲁山县 鲁甸县 鲅鱼圈区 鲤城区 鸠江区 鸡东县 鸡冠区 鸡泽县 鹤城区 鹤山区 鹤山市 鹤峰县 鹤庆县 鹰手营子矿区 鹿城区 鹿寨县 鹿泉区 鹿泉市 鹿邑县 麒麟区 麟游县 麦盖提县 麦积区 麻城市 麻山区 麻栗坡县 麻江县 麻章区 麻阳苗族自治县 黄埔区 黄山区 黄岛区 黄岩区 黄州区 黄平县 黄梅县 黄浦区 黄石港区 黄陂区 黄陵县 黄骅市 黄龙县 黎城县 黎川县 黎平县 黑山县 黑水县 黔江区 黔西市 黟县 鼎城区 鼎湖区 鼓楼区 齐河县 龙井市 龙亭区 龙凤区 龙华区 龙南县 龙南市 龙口市 龙圩区 龙城区 龙子湖区 龙安区 龙山区 龙山县 龙岗区 龙川县 龙州县 龙文区 龙江县 龙沙区 龙泉市 龙泉驿区 龙海区 龙港区 龙港市 龙游县 龙湖区 龙湾区 龙潭区 龙胜各族自治县 龙里县 龙门县 龙陵县 龙马潭区',
    'description': '公司注册地所在区县信息',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '注册地（国-省-市-区）',
    'data_type': 'STRING',
    'optional_value': None,
    'description': '公司营业执照上登记的地址',
    'mapper_fields': '',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '所在地区',
    'data_type': 'STRING',
    'optional_value': '华北地区 北京 北京市 天津 天津市 河北 承德市 邢台市 唐山市 保定市 衡水市 张家口市 邯郸市 廊坊市 石家庄市 沧州市 秦皇岛市 山西 大同市 吕梁市 朔州市 临汾市 晋城市 太原市 忻州市 运城市 长治市 阳泉市 晋中市 内蒙古 乌兰察布市 锡林郭勒盟 通辽市 兴安盟 阿拉善盟 乌海市 呼伦贝尔市 包头市 赤峰市 呼和浩特市 鄂尔多斯市 巴彦淖尔市 华南地区 广东 茂名市 佛山市 清远市 韶关市 广州市 中山市 湛江市 汕尾市 江门市 汕头市 惠州市 梅州市 肇庆市 揭阳市 河源市 云浮市 东莞市 深圳市 潮州市 珠海市 阳江市 广西 百色市 钦州市 梧州市 桂林市 崇左市 贵港市 来宾市 玉林市 河池市 防城港市 贺州市 柳州市 北海市 南宁市 海南 三亚市 海口市 儋州市 省直辖县级行政区划 三沙市 华东地区 上海 上海市 江苏 盐城市 南通市 宿迁市 淮安市 常州市 泰州市 镇江市 连云港市 扬州市 徐州市 无锡市 苏州市 南京市 浙江 杭州市 舟山市 湖州市 丽水市 温州市 宁波市 台州市 嘉兴市 金华市 绍兴市 衢州市 安徽 芜湖市 亳州市 六安市 滁州市 淮北市 合肥市 池州市 马鞍山市 淮南市 蚌埠市 阜阳市 宿州市 宣城市 安庆市 黄山市 铜陵市 福建 龙岩市 泉州市 厦门市 福州市 漳州市 南平市 三明市 宁德市 莆田市 山东 聊城市 莱芜市 泰安市 烟台市 济宁市 淄博市 临沂市 德州市 潍坊市 日照市 青岛市 滨州市 济南市 东营市 威海市 枣庄市 菏泽市 华中地区 江西 新余市 南昌市 宜春市 鹰潭市 赣州市 九江市 萍乡市 吉安市 景德镇市 上饶市 抚州市 河南 新乡市 平顶山市 驻马店市 漯河市 鹤壁市 濮阳市 许昌市 焦作市 周口市 郑州市 洛阳市 信阳市 三门峡市 商丘市 安阳市 南阳市 开封市 省直辖县级行政区划 湖北 襄阳市 黄石市 荆州市 咸宁市 十堰市 荆门市 黄冈市 宜昌市 武汉市 鄂州市 恩施土家族苗族自治州 孝感市 随州市 省直辖县级行政区划 湖南 常德市 湘潭市 长沙市 怀化市 岳阳市 郴州市 永州市 张家界市 株洲市 益阳市 湘西土家族苗族自治州 衡阳市 邵阳市 娄底市 东北地区 辽宁 大连市 朝阳市 辽阳市 锦州市 葫芦岛市 丹东市 盘锦市 沈阳市 铁岭市 抚顺市 阜新市 鞍山市 营口市 本溪市 吉林 延边朝鲜族自治州 白山市 四平市 吉林市 松原市 白城市 长春市 通化市 辽源市 黑龙江 大庆市 大兴安岭地区 鹤岗市 黑河市 双鸭山市 佳木斯市 鸡西市 七台河市 牡丹江市 绥化市 伊春市 齐齐哈尔市 哈尔滨市 西南地区 重庆 重庆市 四川 南充市 甘孜藏族自治州 遂宁市 攀枝花市 自贡市 阿坝藏族羌族自治州 巴中市 雅安市 内江市 达州市 宜宾市 乐山市 广元市 眉山市 广安市 泸州市 绵阳市 成都市 德阳市 凉山彝族自治州 资阳市 贵州 六盘水市 安顺市 贵阳市 遵义市 黔南布依族苗族自治州 黔东南苗族侗族自治州 黔西南布依族苗族自治州 铜仁市 毕节市 云南 昆明市 昭通市 怒江傈僳族自治州 文山壮族苗族自治州 大理白族自治州 临沧市 玉溪市 曲靖市 迪庆藏族自治州 保山市 楚雄彝族自治州 德宏傣族景颇族自治州 红河哈尼族彝族自治州 丽江市 西双版纳傣族自治州 普洱市 西藏 林芝市 昌都市 山南市 那曲市 日喀则地区 阿里地区 拉萨市 林芝地区 昌都地区 那曲地区 山南地区 西北地区 陕西 延安市 安康市 咸阳市 铜川市 宝鸡市 西安市 商洛市 渭南市 榆林市 汉中市 甘肃 酒泉市 武威市 金昌市 兰州市 嘉峪关市 临夏回族自治州 甘南藏族自治州 张掖市 平凉市 陇南市 定西市 庆阳市 天水市 白银市 青海 果洛藏族自治州 海西蒙古族藏族自治州 海北藏族自治州 玉树藏族自治州 海南藏族自治州 黄南藏族自治州 西宁市 海东市 宁夏 吴忠市 银川市 石嘴山市 中卫市 固原市 新疆 巴音郭楞蒙古自治州 乌鲁木齐市 和田地区 昌吉回族自治州 克孜勒苏柯尔克孜自治州 喀什地区 阿勒泰地区 博尔塔拉蒙古自治州 阿克苏地区 塔城地区 克拉玛依市 吐鲁番市 哈密市 伊犁哈萨克自治州 自治区直辖县级行政区划 港澳台地区 台湾 香港特别行政区 澳门特别行政区 境外',
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-全部法规名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-颁布时间',
    'data_type': 'DATE',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-生效时间',
    'data_type': 'DATE',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-法律位阶',
    'data_type': 'STRING',
    'optional_value': '业务规则及办法 规范性文件 部门规章 业务指引 业务细则 法律 内容与格式指引 通知 业务备忘录 业务办理指南 行政法规 信息披露格式指引 协议文本 问答与手册 司法解释 解释性公告及问题解答 公告 格式准则与编报规则 地方政府规章 地方性法规 答记者问及要闻 技术指引 地方工作文件',
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-法律文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-颁布部门',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '违反法规-生效情况',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-全部法规名称',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': None,
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-颁布时间',
    'data_type': 'DATE',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-生效时间',
    'data_type': 'DATE',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-法律位阶',
    'data_type': 'STRING',
    'optional_value': '业务规则及办法 规范性文件 部门规章 业务指引 业务细则 法律 内容与格式指引 通知 业务备忘录 业务办理指南 行政法规 信息披露格式指引 协议文本 问答与手册 司法解释 解释性公告及问题解答 公告 格式准则与编报规则 地方政府规章 地方性法规 答记者问及要闻 技术指引 地方工作文件 工作文件',
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-法律文号',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-颁布部门',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  },
  {
    'indicators_name': '相关法规-生效情况',
    'data_type': 'STRING',
    'optional_value': None,
    'description': None,
    'mapper_fields': '[{"label": "第一条", "value": 0, "title": "违规事项排名"}]',
    'indicators_name_alias': None
  }
]
