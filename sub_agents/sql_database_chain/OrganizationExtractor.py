import pickle
from pathlib import Path
from typing import Dict, Optional, List

from utils.db_tool import get_db_connection


class OrganizationExtractor:
    """组织信息提取器，使用单例模式确保只加载一次数据。"""

    _instance = None
    _organization_info: Dict = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(OrganizationExtractor, cls).__new__(cls)
            cls._instance._load_organization_info()
        return cls._instance

    def _create_organization_info(self) -> None:
        """当文件不存在时，创建组织信息文件"""
        try:
            print("组织信息文件不存在，正在创建...")
            save_organization_info()
            print("组织信息文件创建成功")
        except Exception as e:
            print(f"创建组织信息文件时发生错误: {e}")
            self._organization_info = {}

    def _load_organization_info(self) -> None:
        """
        从pickle文件加载组织信息。
        包含错误处理和文件路径验证。
        """
        file_path = Path('sub_agents/sql_database_chain/organization_info.pkl')

        try:
            if not file_path.exists():
                self._create_organization_info()
                if not file_path.exists():
                    raise FileNotFoundError(f"无法创建组织信息文件: {file_path}")

            with open(file_path, 'rb') as file:
                self._organization_info = pickle.load(file)
                print(f"成功加载组织信息，共 {len(self._organization_info)} 条记录")

        except (FileNotFoundError, PermissionError) as e:
            print(f"无法访问文件: {e}")
            self._organization_info = {}
        except pickle.UnpicklingError as e:
            print(f"无法解析pickle文件: {e}")
            self._organization_info = {}
        except Exception as e:
            print(f"加载组织信息时发生未知错误: {e}")
            self._organization_info = {}

    def extract_organizations(self, question: str) -> List[Dict]:
        """
        从问题中提取提到的组织信息。

        Args:
            question (str): 输入的问题文本

        Returns:
            List[Dict]: 匹配到的组织信息列表
        """
        if not question or not isinstance(question, str):
            print(f"无效的输入问题: {question}")
            return []

        extracted_orgs = []
        for org_name, org_info in self._organization_info.items():
            # 检查 org_info[0] 和 org_info[1] 是否非空且出现在 question 中
            if (org_info[0] and org_info[0] in question) or (org_info[1] and org_info[1] in question):
                extracted_orgs.append(org_info)
                print(f"找到匹配的组织: {org_name}")

        return extracted_orgs

    def get_organization_by_name(self, name: str) -> Optional[Dict]:
        """
        通过精确的组织名称获取组织信息。

        Args:
            name (str): 组织名称

        Returns:
            Optional[Dict]: 组织信息，如果未找到则返回None
        """
        return self._organization_info.get(name)

    @property
    def organization_count(self) -> int:
        """返回已加载的组织总数。"""
        return len(self._organization_info)

def save_organization_info():
    """获取并保存机构信息"""
    # 查询中介机构信息
    query = """
        SELECT
          t2.org_code AS companyCode,
          t1.org_short_name,
          t1.org_full_name AS companyName,
          t1.org_type_code_sub,
          CASE 
            WHEN t1.org_type_code_sub = '001002' THEN '国有商业银行'
            WHEN t1.org_type_code_sub = '002007' THEN '资产管理公司'
            WHEN t1.org_type_code_sub = '005001' THEN '证券公司'
            WHEN t1.org_type_code_sub = '006001' THEN '会计师事务所'
            WHEN t1.org_type_code_sub = '006002' THEN '律师事务所'
            WHEN t1.org_type_code_sub = '006003' THEN '资产评估机构'
            WHEN t1.org_type_code_sub = '006004' THEN '资信评级机构'
            ELSE '其他'
          END AS companyType
        FROM
          sa_organization t1
        JOIN
          sa_org_alias t2 ON t1.org_code = t2.org_code
        WHERE
          t1.sisdel = '0' 
          AND t2.sisdel = '0' 
          AND t1.case_show_flag = '1' 
          AND ( t1.org_type_code_sub IN ( '001002', '005001', '006001', '006002', '006003' ) 
            OR t1.org_code IN ( '100000000156779', '100000000716652' ) )
        GROUP BY t1.org_code
    """

    # 执行查询
    connection = get_db_connection('CLOUD')
    cursor = connection.cursor()
    cursor.execute(query)
    results = cursor.fetchall()

    # 创建一个字典来存储机构信息
    organization_info = {}
    for row in results:
        org_short_name = row[1]
        org_full_name = row[2]
        company_type = row[4]
        # 如果是中信建投证券，则简称替换为中信建投
        if org_short_name == '中信建投证券':
            org_short_name = '中信建投'
        organization_info[org_full_name] = (org_short_name, org_full_name, company_type)

    # 将机构信息保存到本地文件
    with open('sub_agents/sql_database_chain/organization_info.pkl', 'wb') as file:
        pickle.dump(organization_info, file, protocol=pickle.HIGHEST_PROTOCOL)

    # 关闭数据库连接
    cursor.close()
    connection.close()