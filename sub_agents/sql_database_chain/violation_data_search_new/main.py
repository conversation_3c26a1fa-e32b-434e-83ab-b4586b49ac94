from utils.load_env import logger
from sub_agents.sql_database_chain.violation_data_search_new.workflow.generic_violation_workflow import violation_chat

if __name__ == '__main__':
    import gradio as gr

    # 创建Gradio接口
    iface = gr.ChatInterface(
        violation_chat,
        chatbot=gr.Chatbot(elem_id="chatbot", render=False),
        textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
        title="违规案例库助手对话Demo",
        description="这是一个使用Gradio创建的案例库助手对话界面",
        theme="soft",
        cache_examples=True,
        css="""
        #chatbot {
            height: calc(100vh - 280px) !important;
            overflow-y: auto;
        }
        """
    )

    logger.info("违规案例AGENT")

    iface.launch(
        server_name="0.0.0.0",
        inbrowser=True,
        server_port=7862,
        debug=True
    )
