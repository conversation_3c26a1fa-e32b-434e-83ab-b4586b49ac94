"""
违规案例向量检索模块
专门处理基于文本内容的语义搜索
支持MultiQuery + Rerank优化
"""

import logging
from typing import List, Dict, Any, Optional
from langchain.retrievers.multi_query import MultiQueryRetriever
from langchain_core.prompts import PromptTemplate
from langchain_core.retrievers import BaseRetriever
from langchain_core.documents import Document
from langchain_core.callbacks import CallbackManagerForRetrieverRun

from sub_agents.sql_database_chain.violation_data_search.violation_utils import get_company_code
from utils.rerank_tools import ReRankService
import os

logger = logging.getLogger(__name__)


class ViolationVectorRetriever(BaseRetriever):
    """
    违规案例向量检索器，兼容LangChain的Retriever接口
    """

    # 声明为类字段以兼容Pydantic v2
    collection_name: str = 'violate_collection_bce_01'
    sim_threshold: float = 0.1
    company_code = None  # 可以是字符串、列表或None

    def __init__(self, collection_name: str = 'violate_collection_bce_01', sim_threshold: float = 0.1, company_code=None):
        super().__init__()
        self.collection_name = collection_name
        self.sim_threshold = sim_threshold
        self.company_code = company_code

    def _get_relevant_documents(
            self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """获取相关文档"""
        try:
            # 导入必要的模块
            from sub_agents.sql_database_chain.violation_data_search.violation_utils import (
                search_violate_collection_data,
                convert_keys_to_chinese
            )
            print(f"向量重写问题：{query}")

            # 执行向量搜索
            results = search_violate_collection_data(
                collection_name=self.collection_name,
                query=query,
                company_code=self.company_code,  # 直接传递，可以是字符串、列表或None
                sim_threshold=self.sim_threshold
            )

            # 转换为LangChain Document格式
            documents = []
            for item in results:
                # 获取向量文本内容
                vector_text = item.get('向量文本', item.get('embedding_string', ''))

                # 构建文档内容
                content = vector_text if vector_text else str(item)

                # 构建元数据
                metadata = {
                    'title': item.get('标题', item.get('title', 'N/A')),
                    'company_name': item.get('公司名称', item.get('company_name', 'N/A')),
                    'company_code': item.get('公司代码', item.get('company_code', 'N/A')),
                    'event_time': item.get('事件时间', item.get('event_time_str', 'N/A')),
                    'distance': item.get('distance', 0),
                    'punish_org': item.get('处罚机构', item.get('punish_org', 'N/A')),
                    'case_list': item.get('违规事项', item.get('case_list', 'N/A')),
                    'case_id': item.get('案例ID', item.get('case_id', 'N/A'))  # 添加案例ID
                }

                doc = Document(page_content=content, metadata=metadata)
                documents.append(doc)

            return documents

        except Exception as e:
            logger.error(f"[ViolationVectorRetriever] 检索异常: {e}")
            return []


async def execute_multi_query_rerank_search(query: str) -> Dict[str, Any]:
    """
    执行MultiQuery + Rerank的增强向量搜索

    Args:
        query: 查询语句

    Returns:
        包含搜索统计信息和格式化结果的字典
    """
    try:
        logger.info(f"[MultiQueryRerank] 开始增强向量库查询: {query}")

        # 根据用户问题获取公司代码列表
        company_code = get_company_code(query)['result']  # 返回公司代码列表

        # 1. 创建基础检索器
        base_retriever = ViolationVectorRetriever(company_code=company_code)

        # 2. 创建LLM用于生成多查询
        from utils.llm_utils import get_a_llm
        llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0.3)

        # 3. 创建MultiQueryRetriever
        MULTI_DIMENSION_QUERY_PROMPT = PromptTemplate(
            input_variables=["question"],
            template="""你是专业的违规案例检索专家，需要从多个维度生成查询来匹配不同类型的向量化文本。
        原始查询：{question}

        向量库文本类型：案例标题、违规详情、基础信息片段

        生成5个不同维度查询：
        1. 标题格式：关于XX公司XX违规的告知书/决定书
        2. 违规详情：具体违规行为、手段、影响描述
        3. 行业维度：行业名称+违规类型组合
        4. 监管维度：监管机构+处罚措施组合  
        5. 关键词：核心词汇简单组合

        要求：每行一个，无标点编号，5-20字。

        生成查询："""
        )
        multi_query_retriever = MultiQueryRetriever.from_llm(
            retriever=base_retriever,
            llm=llm,
            prompt=MULTI_DIMENSION_QUERY_PROMPT
        )

        # 4. 执行多查询检索
        documents = multi_query_retriever.invoke(query)

        if not documents:
            return {
                "search_summary": "向量库中未找到与查询内容相关的违规案例",
                "formatted_results": []
            }

        # 5. 去重处理 - 基于案例ID去重，如果没有案例ID则使用标题+公司名称+事件时间组合
        unique_documents = []
        seen_cases = set()

        for doc in documents:
            # 优先使用案例ID作为去重键
            dedup_key = doc.metadata.get('case_id', 'N/A')

            if dedup_key not in seen_cases:
                seen_cases.add(dedup_key)
                unique_documents.append(doc)

        logger.info(f"[MultiQueryRerank] 去重前文档数量: {len(documents)}, 去重后文档数量: {len(unique_documents)}")

        # 6. 准备rerank数据
        doc_texts = []
        doc_metadata = []
        for doc in unique_documents:
            # 优先使用违规内容，如果没有则使用原始内容
            try:
                # violation_content = doc.metadata.get('case_list', {}).get('case_list', {}).get('textContent', '')
                # if violation_content and violation_content.strip():
                #     rerank_text = violation_content
                # else:
                rerank_text = doc.page_content
            except (AttributeError, TypeError):
                # 如果结构不对，使用原始内容
                rerank_text = doc.page_content

            doc_texts.append(rerank_text)
            doc_metadata.append(doc.metadata)

        # 7. 执行rerank
        rerank_service = ReRankService(os.getenv('RERANK_SERVICE_HOST'))
        reranked_results = await rerank_service.rerank(query, doc_texts)

        if not reranked_results:
            logger.warning("[MultiQueryRerank] Rerank失败，使用原始结果")
            reranked_results = [
                {'index': i, 'relevance_score': 1.0 - i * 0.1, 'document': doc_texts[i]}
                for i in range(len(doc_texts))
            ]

        # 8. 格式化结果为结构化数据
        formatted_results = []
        for i, rerank_item in enumerate(reranked_results[:10], 1):  # 取前10个
            doc_index = rerank_item['index']
            relevance_score = rerank_item['relevance_score']
            metadata = doc_metadata[doc_index]

            # 处理文档内容预览
            content = rerank_item['document']
            text_preview = ""
            if content and content.strip():
                text_preview = content[:300] + "..." if len(content) > 300 else content

            # 构建结构化结果
            formatted_result = {
                "case_number": i,
                "title": metadata.get('title', 'N/A'),
                "company_name": metadata.get('company_name', 'N/A'),
                "company_code": metadata.get('company_code', 'N/A'),
                "punish_org": metadata.get('punish_org', 'N/A'),
                "case_list": metadata.get('case_list', 'N/A'),
                "event_time": metadata.get('event_time', 'N/A'),
                "relevance_score": round(relevance_score, 3),
                "content_preview": text_preview
            }

            formatted_results.append(formatted_result)

        search_summary = f"通过MultiQuery+Rerank搜索找到 {len(unique_documents)} 个相关违规案例（去重前{len(documents)}条），重排序后显示前{len(formatted_results)}条："

        logger.info(
            f"[MultiQueryRerank] 查询成功，原始结果 {len(documents)} 条，去重后 {len(unique_documents)} 条，重排序后返回 {len(formatted_results)} 条")

        return {
            "search_summary": search_summary,
            "formatted_results": formatted_results
        }

    except Exception as e:
        logger.error(f"[MultiQueryRerank] 增强向量库查询异常: {e}")
        return {
            "search_summary": f"增强向量库查询出现异常: {str(e)}",
            "formatted_results": []
        }
