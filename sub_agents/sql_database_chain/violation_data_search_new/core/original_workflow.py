"""
原始违规案例查询流程的独立模块
封装原有的完整工作流，供简化版本调用
"""
import uuid
import json
from typing import Dict, Any, Optional, Tuple

from langchain_core.messages import ToolMessage
from sub_agents.sql_database_chain.violation_data_search.violation_langgraph import violation_langgraph
from utils.load_env import logger


class OriginalViolationWorkflow:
    """原始违规案例查询工作流的封装类"""

    def __init__(self):
        self.graph = violation_langgraph

    async def execute_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        执行单个查询，返回包含 AI_AGENT_RESPONSE 和最后一个 ToolMessage 的结果

        Args:
            query: 查询问题
            **kwargs: 其他配置参数

        Returns:
            包含 AI_AGENT_RESPONSE 和 last_tool_message 的字典
        """
        try:
            logger.info(f"[OriginalWorkflow] 执行查询: {query}")

            # 配置
            config = {
                "recursion_limit": 15,
                "configurable": {
                    "thread_id": str(uuid.uuid4()),
                    "login_company": kwargs.get("login_company"),
                    **kwargs
                }
            }

            # 构建初始状态
            initial_state = {
                "messages": [("user", query)],
                "query_result_num": kwargs.get("query_result_num", 0)
            }

            # 执行原始工作流
            result = await self.graph.ainvoke(
                initial_state,
                config,
                stream_mode=["values", "custom"],
                debug=kwargs.get("debug", False)
            )

            # 解析新的返回格式（列表）
            ai_agent_response = None
            last_tool_message = None

            if isinstance(result, list):
                logger.info(f"[OriginalWorkflow] 处理列表格式返回，共 {len(result)} 项")

                # 从后往前遍历，一次性处理所有数据
                for item in reversed(result):
                    if isinstance(item, tuple) and len(item) == 2:
                        stream_mode, data = item

                        # 处理 values 数据，查找最后一个 ToolMessage（只处理第一个遇到的，即最后一个）
                        if stream_mode == "values" and last_tool_message is None:
                            if isinstance(data, dict) and "messages" in data and data["messages"]:
                                # 从后往前遍历消息，找到最后一个ToolMessage
                                for message in reversed(data["messages"]):
                                    if isinstance(message, ToolMessage):
                                        last_tool_message = message.content
                                        logger.info(f"[OriginalWorkflow] 找到最后一个 ToolMessage")
                                        break

                        # 处理 custom 数据，查找 AI_AGENT_RESPONSE
                        elif stream_mode == "custom" and ai_agent_response is None:
                            try:
                                custom_data = json.loads(data)

                                if isinstance(custom_data, dict) and "AI_AGENT_RESPONSE" in custom_data:
                                    ai_agent_response = custom_data["AI_AGENT_RESPONSE"]
                                    logger.info(f"[OriginalWorkflow] 找到 AI_AGENT_RESPONSE")
                                    break
                            except (json.JSONDecodeError, TypeError) as e:
                                logger.warning(f"[OriginalWorkflow] 解析 custom 数据失败: {e}")

            # 构建返回结果
            return {
                "AI_AGENT_RESPONSE": ai_agent_response,
                "last_tool_message": last_tool_message
            }

        except Exception as e:
            logger.error(f"[OriginalWorkflow] 查询失败: {e}")
            return {
                "AI_AGENT_RESPONSE": None,
                "last_tool_message": f"查询失败: {e}"
            }


# 创建全局实例
original_workflow = OriginalViolationWorkflow()


# 便捷函数
async def execute_violation_query(query: str, **kwargs) -> Dict[str, Any]:
    """
    便捷函数：执行违规案例查询

    Args:
        query: 查询问题
        **kwargs: 其他配置参数

    Returns:
        包含 AI_AGENT_RESPONSE 和 last_tool_message 的字典
    """
    return await original_workflow.execute_query(query, **kwargs)
