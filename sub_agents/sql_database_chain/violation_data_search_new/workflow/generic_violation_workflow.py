"""
基于通用工作流框架的违规案例查询系统
核心流程：监督者Agent → 快速规划器 → 任务分发器 → 查询Agent → 迭代检查 → 结果合并器
"""
import json
import uuid
from datetime import datetime
from typing import Dict, Any

import pytz
from langchain_core.prompts import ChatPromptTemplate
from langgraph.config import get_stream_writer

from sub_agents.sql_database_chain.violation_data_search_new.workflow.generic_workflow_framework import GenericWorkflowFramework, ToolRegistry, GenericQueryExecutor
from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.sql_database_chain.violation_data_search_new.core.original_workflow import execute_violation_query
from sub_agents.sql_database_chain.violation_data_search_new.core.vector_search import execute_multi_query_rerank_search
from utils.db_tool import MyAIOMySQLSaver, agent_checkpoint_db_uri
from utils.load_env import logger


# 工具描述生成器
class ToolDescriptionGenerator:
    """工具描述生成器，从注册的工具中提取描述信息"""

    @staticmethod
    def extract_tool_description(tool_class_or_instance) -> str:
        """
        从工具类或实例中提取描述信息

        Args:
            tool_class_or_instance: 工具类或实例

        Returns:
            格式化的工具描述字符串
        """
        try:
            # 获取类的文档字符串
            if hasattr(tool_class_or_instance, '__doc__') and tool_class_or_instance.__doc__:
                doc = tool_class_or_instance.__doc__.strip()
                # 清理文档字符串格式
                lines = [line.strip() for line in doc.split('\n') if line.strip()]
                return '\n'.join(lines)
            else:
                return "暂无描述信息"
        except Exception as e:
            logger.warning(f"提取工具描述失败: {e}")
            return "描述信息提取失败"

    @staticmethod
    def generate_tools_description(tool_registry: ToolRegistry) -> str:
        """
        生成所有注册工具的描述信息

        Args:
            tool_registry: 工具注册表

        Returns:
            格式化的工具描述字符串
        """
        descriptions = []
        processed_tools = set()  # 避免重复处理相同的工具类

        for tool_type in tool_registry.list_tools():
            # 跳过默认的 query_tool，避免重复
            if tool_type == "query_tool":
                continue

            try:
                tool = tool_registry.get(tool_type)
                tool_class_name = tool.__class__.__name__

                # 避免重复处理相同的工具类
                if tool_class_name in processed_tools:
                    continue
                processed_tools.add(tool_class_name)

                tool_desc = ToolDescriptionGenerator.extract_tool_description(tool)

                # 格式化工具描述，添加分隔线
                formatted_desc = f"- **{tool_type}**: \n{tool_desc}\n"
                descriptions.append(formatted_desc)

            except Exception as e:
                logger.warning(f"生成工具 {tool_type} 描述失败: {e}")
                descriptions.append(f"- **{tool_type}**: 描述生成失败\n")

        return '\n'.join(descriptions)


# 违规案例简单对话回复生成器
class ViolationSimpleResponseGenerator:
    """专门处理违规案例查询系统简单对话的回复生成器"""

    def __init__(self, llm):
        self.llm = llm

        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是专业、友好的违规案例查询助手。用户刚才发送了简单的对话内容，请生成一个合适的回复。

你的特点：
- 专业的违规案例查询专家，熟悉监管政策和违规案例
- 友好亲切，但保持专业性
- 主动介绍你的核心能力（违规案例查询、处罚分析等）
- 引导用户提出具体的违规案例相关问题
- 回复简洁明了，突出专业性

你可以帮助用户：
• 查询特定公司的违规案例和处罚情况
• 分析违规类型、处罚金额、处罚类型等数据
• 统计违规案例数量、趋势和行业对比
• 检索特定违规行为和处罚对象
• 查询案例进程、申辩情况等详细信息

根据用户输入类型生成回复：
- 问候语：友好回应并介绍违规案例查询能力
- 感谢语：谦逊回应并提供进一步的违规案例查询帮助
- 测试输入：确认系统正常并介绍违规案例查询功能
- 闲聊内容：礼貌回应并引导到违规案例专业话题
- 其他：提供友好的默认回复，突出违规案例查询专业性

请直接返回回复内容，不要添加额外格式。
时间非常重要，必须牢记当前用户的时间: {time}!!
"""),
            ("user", "{user_input}")
        ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))

    async def generate_response(self, user_input: str) -> str:
        """生成违规案例查询系统的简单对话回复"""
        try:
            chain = self.prompt | self.llm
            response = await chain.ainvoke({"user_input": user_input})
            return response.content
        except Exception as e:
            logger.warning(f"违规案例简单回复生成失败，使用默认回复: {e}")
            # 降级处理
            return "您好！我是专业的违规案例查询助手，可以帮您查询和分析各种违规案例、处罚信息等数据。请问有什么可以帮助您的吗？"


# 违规案例查询执行器 - 结构化数据查询
class StructuredDataQueryExecutor(GenericQueryExecutor):
    """执行结构化数据查询，用于从违规案例数据库中进行精确筛选和计数。

    功能:
    - 条件筛选: 根据时间、公司、违规类型等一个或多个精确字段筛选案例。
    - 案例计数: 查询满足特定条件的案例总数。
    - 精确信息获取: 查询处罚日期、案例进程、处罚对象等数据库中的明确字段。

    硬性限制:
    - **无分页能力**: 仅返回查询结果的前10条记录，无法获取后续数据。
    - **无分组统计能力**: 无法执行如 "按省份统计"、"按违规类型分组" 等操作。
    - **无聚合计算能力**: 无法执行如 "计算总罚款"、"求平均值" 等数学运算。
    """

    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """
        执行结构化数据查询任务

        Args:
            task: 任务信息
            context: 上下文信息

        Returns:
            查询结果字符串
        """
        query = task.get("query", "")
        description = task.get("description", "")

        # 将description中的搜索一律替换为查询
        description = description.replace("搜索", "查询")

        logger.info(f"[StructuredDataQueryExecutor] 开始执行结构化数据查询: {description}")
        writer = get_stream_writer()
        writer(json.dumps({"AI_AGENT_FLOW": f"🔍 开始执行结构化数据查询: {description}"}, ensure_ascii=False))

        # 调用原始查询函数
        result_dict = await execute_violation_query(
            query
        )

        # 提取最后一个 ToolMessage 作为主要结果
        result = result_dict.get("last_tool_message", "")


        writer(json.dumps({'AI_AGENT_RESPONSE':result_dict.get("AI_AGENT_RESPONSE", "")} , ensure_ascii=False))

        logger.info(f"[StructuredDataQueryExecutor] 结构化数据查询完成: {description}")
        return result


# 违规案例查询执行器 - 向量库查询
class VectorSearchQueryExecutor(GenericQueryExecutor):
    """执行基于语义相似度的向量搜索，用于在违规案例文本内容中进行模糊和关联查询。

    功能:
    - 语义搜索: 基于自然语言描述的含义，在案例的详细文本内容中查找相关信息。
    - 模糊查询: 当查询条件不明确或用户意图模糊时，查找可能相关的案例。
    - 相似案例发现: 根据一段文本描述，寻找内容上相似的其他案例。
    """

    async def execute(self, task: Dict[str, Any], context: Dict[str, Any] = None) -> str:
        """
        执行向量库查询任务

        Args:
            task: 任务信息
            context: 上下文信息

        Returns:
            查询结果字符串
        """
        query = task.get("query", "")
        description = task.get("description", "")

        # 将description中的搜索一律替换为查询
        description = description.replace("查询", "搜索")

        logger.info(f"[VectorSearchQueryExecutor] 开始执行向量库查询: {description}")
        writer = get_stream_writer()
        writer(json.dumps({"AI_AGENT_FLOW": f"🔍 开始执行向量库查询: {description}"}, ensure_ascii=False))

        # 调用向量库查询函数
        result_dict = await execute_multi_query_rerank_search(
            query
        )

        # 推送向量检索数据
        writer(json.dumps({'AI_AGENT_VECTOR': result_dict}, ensure_ascii=False))

        # 将字典结果转换为字符串格式返回
        if isinstance(result_dict, dict):
            search_summary = result_dict.get("search_summary", "")
            formatted_results = result_dict.get("formatted_results", [])

            # 将结构化数据转换为字符串格式
            result_strings = []
            for case in formatted_results:
                if isinstance(case, dict):
                    case_str = f"""案例{case.get('case_number', 'N/A')}：
                            - 案例标题: {case.get('title', 'N/A')}
                            - 公司名称: {case.get('company_name', 'N/A')} ({case.get('company_code', 'N/A')})
                            - 处罚机构: {case.get('punish_org', 'N/A')}
                            - 违规事项: {case.get('case_list', 'N/A')}
                            - 事件时间: {case.get('event_time', 'N/A')}
                            - Rerank得分: {case.get('relevance_score', 0)}"""
                    if case.get('content_preview'):
                        case_str += f"\n- 相关内容: {case.get('content_preview')}"
                    result_strings.append(case_str)
                else:
                    # 兼容旧的字符串格式
                    result_strings.append(str(case))

            result = search_summary + "\n\n" + "\n\n".join(result_strings)
        else:
            result = str(result_dict)

        logger.info(f"[VectorSearchQueryExecutor] 向量库查询完成: {description}")
        return result



# 优化的违规案例规划器提示词模板
def create_violation_planner_prompt(tools_description: str = "") -> str:
    """创建违规案例规划器提示词，支持动态工具描述"""
    return f"""
你是顶级的违规案例分析专家，负责将用户的复杂问题拆解为一系列清晰、可执行的查询任务。

你的核心任务是：理解用户目标 -> 制定解决思路 -> 规划初始任务。

## 1. 可用工具
{tools_description}

## 2. 工具选择核心策略

**何时使用 `structured_data` (结构化查询)？**
- **目标是“数字”或“列表”**: 当用户需要精确的计数、或符合明确条件的案例清单时。
- **查询条件明确**: 当用户的提问中包含了具体的实体、时间和类别时。例如，一个完整的自然语言问句：“查询**招商银行**在**2023年**所有关于**财务造假**的案例”
- **典型问题**:
    - "2023年财务造假案例有**多少个**？" (计数)
    - "给我**列出**罚款超过100万的案例。" (列表/筛选)
    - "查询A公司的**处罚日期**和**案例进程**。" (精确信息)

**何时使用 `vector_search` (向量搜索)？**
- **目标是“内容”或“方式”**: 当用户想了解违规的具体手段、操作细节、或基于场景描述查找案例时。
- **查询条件模糊**: 用户的查询是描述性的，无法直接对应数据库字段。
- **作为备用方案**: 当结构化查询找不到结果或结果不理想时，用它进行探索性搜索。
- **典型问题**:
    - "**如何**通过虚构交易虚增收入？找些案例看看。" (方式/手段)
    - "有没有关于审计机构**未勤勉尽责**的案例？" (场景描述)
    - "根据这段描述，帮我找找**类似的案例**。" (相似性)

## 3. 任务分解的黄金法则

**绝对禁止**向 `structured_data` 工具提出任何**分组、统计、计算或翻页**的需求。
- **错误示范**: "统计各省份的案例数量"
- **正确分解**: 将其拆解为多个独立任务。
    - 任务1: "查询北京的案例数量"
    - 任务2: "查询上海的案例数量"
    - 任务3: "查询广东的案例数量"
    - ... (后续由合并器进行总结)

## 4. 图表与关系图决策
- **是否需要生成图表**: 如果任务涉及数据对比、趋势分析（如不同年份的数量比较），则设置为 `true`。
- **是否需要生成人物关系图**: 仅当案例涉及明确的人物关系（如内幕交易、关联交易）时，设置为 `true`。

{{format_instructions}}

记住，你的规划质量直接决定了最终答案的质量。请像真正的专家一样思考。
当前时间: {{time}}

## 示例

**示例1 - 结构化数据查询（正确分解）**
用户："比较2022年和2023年财务造假案例的数量"
输出：
{{{{
    "user_goal": "了解2022年和2023年财务造假案例的数量变化",
    "approach": "分别独立查询2022年和2023年财务造假案例的总数，然后进行对比。",
    "initial_tasks": [
        {{{{"id": "task_2022", "description": "查询2022年财务造假案例的总数", "query": "2022年财务造假案例总数", "tool_type": "structured_data", "intent": "获取2022年数据点"}}}},
        {{{{"id": "task_2023", "description": "查询2023年财务造假案例的总数", "query": "2023年财务造假案例总数", "tool_type": "structured_data", "intent": "获取2023年数据点"}}}}
    ],
    "need_chart": true,
    "need_character_relationships": false
}}}}

**示例2 - 向量库查询（文本内容搜索）**
用户："查找通过虚构交易虚增收入被处罚的案例"
输出：
{{{{
    "user_goal": "了解通过虚构交易虚增收入的违规案例和相关处罚情况",
    "approach": "使用向量库查询搜索相关违规内容描述的案例。",
    "initial_tasks": [
        {{{{"id": "task_1", "description": "搜索通过虚构交易虚增收入的违规案例", "query": "虚构交易虚增收入违规案例", "tool_type": "vector_search", "intent": "获取相关违规案例"}}}}
    ],
    "need_chart": true,
    "need_character_relationships": false
}}}}

**示例3 - 混合查询与正确分解**
用户："分析信息披露违规的数量趋势（近两年）和常见违规内容"
输出：
{{{{
    "user_goal": "了解信息披露违规的数量变化趋势和常见违规内容",
    "approach": "先通过多次结构化查询获取近两年的数量数据点，再用向量搜索查找常见违规内容的文本描述，最后综合分析。",
    "initial_tasks": [
        {{{{"id": "task_count_2024", "description": "查询2024年信息披露违规案例总数", "query": "2024年信息披露违规案例数量", "tool_type": "structured_data", "intent": "获取2024年数量数据"}}}},
        {{{{"id": "task_count_2023", "description": "查询2023年信息披露违规案例总数", "query": "2023年信息披露违规案例数量", "tool_type": "structured_data", "intent": "获取2023年数量数据"}}}},
        {{{{"id": "task_content", "description": "搜索信息披露违规的常见内容和表现形式", "query": "信息披露违规常见内容表现形式", "tool_type": "vector_search", "intent": "获取常见违规内容的文本描述"}}}}
    ],
    "need_chart": true,
    "need_character_relationships": false
}}}}

**❌ 错误示例 - 绝对不要生成：**
- {{{{"query": "招商银行 罚款金额 统计分析", "tool_type": "structured_data"}}}} ❌关键词组合
- {{{{"query": "查询招商银行第2页违规案例", "tool_type": "structured_data"}}}} ❌翻页请求
- {{{{"query": "统计分析违规类型分布", "tool_type": "structured_data"}}}} ❌要求分析计算
"""


# 优化的违规案例监督者提示词
VIOLATION_SUPERVISOR_PROMPT = """你是违规案例查询系统的监督者，负责识别用户意图并决定处理路径。

你需要判断用户输入属于以下哪种类型：

1. **simple_chat（简单对话）**：
   - 问候语：你好、hi、hello等
   - 感谢语：谢谢、感谢等
   - 闲聊：天气、心情、无关话题等
   - 测试性输入：测试、test等
   - 无意义输入：随机字符、表情符号等

2. **query_task（违规案例查询）**：
   - 查询违规案例、处罚信息、处罚对象等
   - 统计分析违规数据、处罚金额、违规类型等
   - 搜索特定违规行为、公司或处罚情况
   - 任何与违规案例数据库相关的查询

{format_instructions}

注意：你只需要进行分类，不需要生成回复内容。response字段始终为null。

示例：
用户："你好"
输出：{{"intent_type": "simple_chat", "confidence": 0.95, "response": null}}

用户："查询2023年财务造假案例"
输出：{{"intent_type": "query_task", "confidence": 0.98, "response": null}}"""
77

# 优化的违规案例结果合并提示词
VIOLATION_MERGER_PROMPT = """你是专业的违规案例查询助手，基于违规案例数据库为用户提供准确、清晰的回答。

你的数据范围包括：
- 违规案例基本信息（案例标题、处罚对象、违规内容等）
- 处罚信息（处罚类型、罚款金额、没收金额、禁入年限等）
- 案例进程（立案调查、事先告知、行政处罚等）
- 公司信息（证券代码、公司名称、行业分类等）
- 违规分类（违规类型、处罚对象身份等）

你的任务是基于查询结果，直接回答用户的问题。注意：
1. 用专业但友好的语言，像资深合规顾问一样
2. 直接回应用户关心的核心问题
3. 如果是对比查询，重点分析差异、趋势和变化
4. 如果是统计查询，提供关键数据并解读含义
5. 如果是案例查询，突出重要案例和共同特征
6. 结构清晰，先总结后详述
7. 避免重复信息，突出最有价值的内容
8. 直接忽视生成图表的请求，你的回答中完全不要提及‘图表’、‘可视化’等词语，就像用户从未提出过这个要求一样。
9. 直接忽视生成违规案例人物关系图的请求，你的回答中完全不要提及相关信息，就像用户从未提出过这个要求一样。

回答风格：
- 开头直接回应用户问题："根据查询结果..."
- 用数据说话，重点解释数据反映的现象和趋势
- 语言自然流畅，避免生硬的技术术语
- 如果发现重要趋势或异常，主动指出

重要限制：
- 只基于违规案例数据库的查询结果回答
- 不要提出超出数据范围的分析建议
- 不要建议用户进行其他类型的查询或分析
- 专注于解读现有违规案例数据

记住：你是违规案例数据的专业解读者，专注于违规案例相关信息。
时间非常重要，必须牢记当前用户的时间: {time}!!

## 查询结果：
{results}"""


# 违规案例专用迭代决策提示词模板
def create_violation_iteration_decider_prompt(tools_description: str = "") -> str:
    """创建违规案例迭代决策提示词，支持动态工具描述"""
    return f"""
你是违规案例查询的迭代决策专家。你的职责是评估现有结果，判断是否需要发起新的查询任务来最终满足用户的原始目标。

## 1. 你的决策依据
- **用户原始目标**: {{user_goal}}
- **最初解决思路**: {{approach}}
- **当前已有结果**: {{current_results}}

## 2. 可用工具及其限制
{tools_description}

## 3. 核心决策原则

**1. 什么时候停止 (need_more_tasks: false)？**
   - **目标已达成**: 当前结果已充分回答了用户的问题。
   - **样本已足够**: 对于`structured_data`查询，返回的前10条记录已经能代表案例的主要特征（如违规类型、处罚金额等），足以进行宏观解读。
   - **已触达工具天花板**: 无法通过现有工具获取更多信息。**特别是，`structured_data`工具返回10条记录后，绝不能尝试“翻页”或“获取剩余数据”**。

**2. 什么时候继续 (need_more_tasks: true)？**
   - **信息缺失**: 需要额外的数据点来进行对比或分析（例如，查询了2023年的数据，还需要2022年的数据进行对比）。
   - **查询失败，需要补救**: `structured_data`查询失败或返回空，可以尝试用`vector_search`进行语义搜索作为补救措施。
   - **需要深挖细节**: 已有案例列表，但用户想了解具体的“违规手段”或“操作过程”，此时需要用`vector_search`来查询文本内容。

{{format_instructions}}

请基于以上原则和当前信息，做出最明智的决策。
当前时间: {{time}}

## 可查询的违规案例结构化字段
案例序号、案例标题、处罚对象名称、违规内容、处罚对象身份、违规类型、处罚类型、罚款明细、总罚款金额、罚款金额币种、没收明细、总没收金额、没收金额币种、禁入年限、禁入时间、判刑期限、判刑生效日期、违规案例原文、案例文号、处罚日期、公告日期、处罚机构、申辩情况、申辩内容、处罚情况、案例当前进程、案例所属板块、违规主体本身、违规主体的交易对手、涉事主体所在公司、被执业的公司、被保荐的公司、被内幕交易的公司、被承诺的公司、被操纵股价的公司、违规事件数量、违规事件、证券代码、公司简称、公司名称、企业性质、注册资本、控股股东、实际控制人、实际控制人性质、主营业务、所属申万行业、所属申万行业代码、省份、城市、区县信息、注册地、所在地区等

## 查询示例

**示例1 - 结构化查询失败，向量补救：**
当前结果："查询错误：指标异常"，用户查询"中国平安内幕交易案例"
{{{{
    "need_more_tasks": true,
    "new_tasks": [
        {{{{"id": "task_vector", "description": "向量检索中国平安内幕交易案例", "query": "查询中国平安涉及内幕交易的违规案例", "tool_type": "vector_search", "intent": "结构化查询失败的补救方案"}}}}
    ],
    "reasoning": "结构化查询失败，使用向量检索补救"
}}}}

**示例2 - 需要对比数据：**
当前结果："2023年财务造假案例10条"，用户要求"与2022年对比"
{{{{
    "need_more_tasks": true,
    "new_tasks": [
        {{{{"id": "task_2022", "description": "查询2022年财务造假案例", "query": "查询2022年财务造假的违规案例", "tool_type": "structured_data", "intent": "获取对比数据"}}}}
    ],
    "reasoning": "需要2022年数据进行对比分析"
}}}}

**示例3 - 信息已足够：**
当前结果："招商银行违规案例268条，已显示前10条"，用户询问招商银行违规情况
{{{{
    "need_more_tasks": false,
    "new_tasks": [],
    "reasoning": "前10条样本已包含违规类型、处罚金额等核心信息，足以回答用户基本查询需求。结构化工具无法获取更多数据"
}}}}

**示例4 - 需要详细内容：**
当前结果显示基本案例信息，用户询问"具体违规手段"
{{{{
    "need_more_tasks": true,
    "new_tasks": [
        {{{{"id": "task_detail", "description": "向量检索具体违规手段", "query": "查询违规案例的具体操作手段和实施过程", "tool_type": "vector_search", "intent": "获取详细违规内容"}}}}
    ],
    "reasoning": "结构化数据只有基本信息，需要向量检索获取详细违规手段描述"
}}}}

**❌ 错误示例 - 绝对不要生成：**
- {{{{"query": "招商银行 罚款金额 统计分析", "tool_type": "structured_data"}}}} ❌关键词组合
- {{{{"query": "查询招商银行第2页违规案例", "tool_type": "structured_data"}}}} ❌翻页请求
- {{{{"query": "统计分析违规类型分布", "tool_type": "structured_data"}}}} ❌要求分析计算
"""

# 创建违规案例查询工作流
def create_violation_workflow():
    """创建违规案例查询工作流"""
    # 获取LLM
    llm = get_a_llm(llm_type='hs-deepseek-v3-0324', temperature=0)

    # 创建工作流框架
    workflow = GenericWorkflowFramework(llm)

    # 注册查询工具 - 先注册工具，然后生成描述
    workflow.tool_registry.register("structured_data", StructuredDataQueryExecutor())
    workflow.tool_registry.register("vector_search", VectorSearchQueryExecutor())

    # 生成工具描述
    tools_description = ToolDescriptionGenerator.generate_tools_description(workflow.tool_registry)
    logger.info(f"生成的工具描述: {tools_description}")

    # 自定义监督者提示词
    workflow.supervisor.prompt = ChatPromptTemplate.from_messages([
        ("system", VIOLATION_SUPERVISOR_PROMPT),
        ("placeholder", "{messages}"),
    ])

    # 自定义简单回复生成器
    workflow.simple_response_generator = ViolationSimpleResponseGenerator(llm)

    # 自定义规划器提示词 - 使用动态生成的工具描述
    planner_prompt = create_violation_planner_prompt(tools_description)
    workflow.planner.prompt = ChatPromptTemplate.from_messages([
        ("system", planner_prompt),
        ("placeholder", "{messages}"),
    ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))

    # 自定义结果合并器提示词
    workflow.merger.prompt = ChatPromptTemplate.from_messages([
        ("system", VIOLATION_MERGER_PROMPT),
        ("placeholder", "{messages}")
    ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))

    # 自定义迭代决策器提示词 - 使用动态生成的工具描述
    iteration_decider_prompt = create_violation_iteration_decider_prompt(tools_description)
    workflow.iteration_decider.prompt = ChatPromptTemplate.from_messages([
        ("system", iteration_decider_prompt),
        ("placeholder", "{messages}"),
    ]).partial(time=datetime.now(pytz.timezone('Asia/Shanghai')).strftime("%Y年%m月%d日 %H点%M分"))

    return workflow


# 便捷函数
async def violation_chat(message: str, **kwargs) -> str:
    """
    违规案例查询聊天函数
    
    Args:
        message: 用户消息
        **kwargs: 其他配置参数
        
    Returns:
        助理回复
    """
    try:
        # 创建工作流
        workflow = create_violation_workflow()
        
        # 编译工作流
        compiled_graph = workflow.compile()
        
        # 获取初始状态
        initial_state = workflow.get_initial_state(
            message=message,
            login_company=kwargs.get("login_company"),
            context={"login_company": kwargs.get("login_company")}
        )
        
        # 配置
        config = {
            "recursion_limit": 20,
            "configurable": {
                "thread_id": str(uuid.uuid4())
                # "thread_id": 112233445511223344
            }
        }

        # 执行工作流
        async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
            persistent_graph = workflow.compile(checkpointer=checkpointer)
            result = await persistent_graph.ainvoke(
                initial_state,
                config,
                stream_mode="values",
                debug=kwargs.get("debug", True)
            )
        
        # 提取回答
        answer = result["messages"][-1].content
        return answer
        
    except Exception as e:
        logger.error(f"[violation_chat] 处理失败: {e}")
        return f"抱歉，处理您的请求时出现了错误：{e}"


# 示例使用
async def example_usage():
    """示例用法"""
    # 简单对话
    # result1 = await violation_chat("你好")
    # print("简单对话示例:", result1)

    # 结构化数据查询
    # result2 = await violation_chat("帮我查询最近三个月每个月的违规案例情况 图形展示")
    # print("结构化数据查询示例:", result2)
    
    # # 向量库查询
    result3 = await violation_chat("广济药业和招商银行虚增营收的财务造假违规具体是违规在哪")
    print("向量库查询示例:", result3)
    #
    # # 混合查询
    # result4 = await violation_chat("分析信息披露违规的数量趋势和常见违规手段")
    # print("混合查询示例:", result4)


if __name__ == "__main__":
    import asyncio
    
    print("=== 基于通用工作流框架的违规案例查询系统 ===")
    print("核心流程：监督者Agent → 快速规划器 → 任务分发器 → 查询Agent → 迭代检查 → 结果合并器")
    print()
    
    # 运行示例
    asyncio.run(example_usage())
