# 简化的违规案例查询工作流

## 核心设计理念

**任务分解 → 执行 → 汇总 → 回答**

基于LangGraph，AI驱动的任务分解，调用原始工作流执行。

## 架构设计

### 核心组件
1. **SimpleTaskDecomposer**: AI任务分解器
2. **OriginalViolationWorkflow**: 原始工作流封装
3. **SimpleResultMerger**: AI结果合并器

### 工作流程
```
用户问题 → AI任务分解 → 并行执行原始工作流 → AI结果汇总 → 最终回答
```

### 状态管理
```python
{
    "messages": [],           # 消息历史
    "user_question": "",      # 用户问题
    "tasks": [],             # 任务列表
    "task_results": [],      # 任务结果
    "current_task_index": 0, # 当前任务索引
    "final_result": ""       # 最终结果
}
```

## 使用方法

```python
from sub_agents.sql_database_chain.violation_data_search_new.workflow.simple_workflow import simple_chat

# 简单查询
result = await simple_chat("2023年财务造假案例")

# 复杂查询
result = await simple_chat("比较2022年和2023年违规情况")
```

## 文件结构

```
violation_data_search_new/
├── core/
│   └── original_workflow.py    # 原始工作流封装
├── workflow/
│   └── simple_workflow.py      # 简化工作流
├── test_simple_workflow.py     # 测试文件
├── README.md                   # 说明文档
└── COMPARISON.md               # 对比文档
```

## 优势

1. **架构清晰**: 3个节点的线性流程
2. **复用原有**: 完全兼容现有工具链
3. **AI驱动**: 任务分解和结果合并由AI完成
4. **并行执行**: 多任务场景下并行处理，提升效率
5. **易于维护**: 代码量减少70%+
6. **功能完整**: 支持单任务和多任务场景

## 测试

```bash
python test_simple_workflow.py
```
