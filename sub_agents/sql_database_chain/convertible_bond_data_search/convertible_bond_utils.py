import ast
import copy
import json
import os
import time
from contextlib import contextmanager
from typing import Union, <PERSON>, <PERSON><PERSON>
from typing import Dict, List, Optional

from sub_agents.sql_database_chain.OrganizationExtractor import OrganizationExtractor
from utils.db_tool import query_ingredients_by_keywords

import requests
from langchain_core.messages import ToolMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_core.runnables import RunnableLambda, RunnablePassthrough
from langchain_openai import ChatOpenAI
from langgraph.config import get_stream_writer
from langgraph.prebuilt import ToolNode

from sub_agents.sql_database_chain.convertible_bond_data_search.convertible_bond_prompt import target_list_prompt
from utils.db_tool import get_case_browser_params_from_mysql, get_association_metrics_from_mysql, \
    get_compiled_mappers_from_mysql
from utils.load_env import logger
from utils.milvus_tools import milvus_host, MilvusClient
from utils.redis_tool import remove_redis_info, get_redis_connect

MODEL_HUB_URL = os.getenv('MODEL_HUB_HOST')
compiled_mappers = get_compiled_mappers_from_mysql('3')
extractor = OrganizationExtractor()
FIELD_MAPPERS = {
    "审核结果": {
        "query_param": {"type_name": "审核结果", "case_type": "0"},
        "mapper": "ingredient.audit_result",
        "name_field": "ingredients_name"
    },
    "上市板块": {
        "query_param": {"type_name": "上市板块", "case_type": "0"},
        "mapper": "ingredient.ipo_board",
        "name_field": "ingredients_name"
    },
    "审核制度": {
        "query_param": {"type_name": "发行/审核制度", "case_type": "0"},
        "mapper": "ingredient.audit_type",
        "name_field": "ingredients_name"
    },
    "所属证监会行业（终止）": {
        "query_param": {"type_name": "证监会行业(终止)", "case_type": "0"},
        "mapper": "ingredient.industry_path",
        "name_field": "ingredients_name"
    },
    "中上协行业（新）": {
        "query_param": {"type_name": "中上协行业(新)", "case_type": "0"},
        "mapper": "ingredient.industry_capc_path",
        "name_field": "ingredients_name"
    },
    "所属申万行业": {
        "query_param": {"type_name": "申万行业", "case_type": "0"},
        "mapper": "ingredient.industry_sw_path",
        "name_field": "ingredients_name"
    },
    "注册地（省-市-区）": {
        "query_param": {"type_name": "注册地", "case_type": "0"},
        "mapper": "ingredient.area_path",
        "name_field": "ingredients_name"
    },
    "所属证监会辖区": {
        "query_param": {"type_name": "证监会辖区", "case_type": "0"},
        "mapper": "ingredient.securities_regulatory",
        "name_field": "ingredients_name"
    }
}


def filter_messages(messages: list):
    # 获取所有 type == 'human' 的下标
    human_indices = [i for i, message in enumerate(messages) if message.type == "human"]

    # 如果少于3个 'human' 消息，则返回原列表
    if len(human_indices) < 3:
        return messages

    # 获取倒数第三个 'human' 消息的下标

    third_last_human_index = human_indices[-2]

    # 返回从倒数第三个 'human' 消息开始的子列表
    return messages[third_last_human_index:]


def create_tool_node_with_fallback(tools: list):
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )


def handle_tool_error(state) -> dict:
    error = state.get("error")
    tool_calls = state["messages"][-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"错误: {repr(error)}\n 请修正错误。",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }


async def send_processing_feedback(input_text: str):
    """
    发送处理反馈消息到客户端。

    Args:
        input_text (str): 用户输入的文本。
    """
    writer = get_stream_writer()
    writer(json.dumps({"AI_AGENT_FLOW": f"正在可转债发行案例库中查询{input_text}问题"}, ensure_ascii=False))
    writer(json.dumps({"DATA_SOURCE": "3"}, ensure_ascii=False))


async def get_target_list_for_llm(input_text: str, target_list: str) -> list[Any]:
    @contextmanager
    def timer():
        start = time.time()
        yield
        end = time.time()
        print(f"Elapsed time: {end - start:.2f} seconds")

    try:
        parser = JsonOutputParser()
        prompt_template = ChatPromptTemplate.from_messages([
            HumanMessagePromptTemplate.from_template(target_list_prompt)
        ])
        llm = ChatOpenAI(
            model='hs-deepseek-v3-0324',
            temperature=0,
            max_tokens=None,
            timeout=300,
            api_key="sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320",
            base_url=MODEL_HUB_URL
        )
        chain = (
                {"input": RunnablePassthrough(), "target_list": RunnablePassthrough()}
                | prompt_template
                | llm
                | parser
        )

        with timer():
            result = await chain.ainvoke({
                "input": input_text,
                "target_list": target_list
            })
            # 发射自定义事件

            # 添加 证券代码 公司简称 公司全称 到结果中
            result.extend(['证券代码', '公司简称', '公司名称', '所属中上协行业（新）'])

            # 打印结果用于调试
            print(result)

            # 如果result大于20则返回[]
            if len(result) > 20:
                result = result[:20]

            # 使用 ast.literal_eval 解析带有单引号的 target_list
            try:
                target_list_dict = ast.literal_eval(target_list)
            except (ValueError, SyntaxError) as e:
                print(f"解析错误: {e}")
                return []  # 返回空列表或其他处理逻辑

            # 匹配结果，返回与 LLM 输出结果匹配的完整字典（键值对），然后转换为 JSON 数组
            matched_results = [item for item in target_list_dict if item.get('indicators_name') in result]

            # 返回 JSON 数组
            return matched_results
    except Exception as e:
        logger.error(f"[get_target_list_for_llm] 异常：{e}")
        return []


def vector_case_search(query: str, top_k: int = 10, sim_threshold: float = 0.55):
    """
    支持指标和成分的通用向量检索：根据类型自动路由到指标库或成分库，直接传文本即可。
    :param query: 检索文本（不用向量化）
    :param top_k: 返回数量
    :param sim_threshold: 相似度阈值
    :return: 检索结果
    """
    collection_name = "agent_knowledge_indicators"
    expr = f"case_type_name == '可转债发行'"
    default_fields = ["indicators_name", "indicators_name_alias", "data_type", "description", "optional_value", "mapper_fields"]
    client = MilvusClient(milvus_host)
    response = client.search_vector(
        collection_name=collection_name,
        vectors=query,  # 直接传文本
        expr=expr,
        top_k=top_k,
        out_fields=default_fields,
    )
    results = []
    if response.get('success') and response.get('result'):
        results = response.get('result')[0]
        # 过滤相似度
        results = [hit for hit in results if hit.get('distance', 0) > sim_threshold]
        for hit in results:
            # 删除 id 和 distance 字段
            hit.pop('id', None)  # 使用 pop 安全删除，避免 KeyError
            hit.pop('distance', None)
    return results


def vector_qa_search(user_question: str, sim_threshold: float = 0.95, out_fields=None):
    """
    检索向量库，返回评分大于等于 sim_threshold 的第一个数据的标注字段（params）。
    :param user_question: 用户问题文本（直接传文本即可）
    :param sim_threshold: 相似度阈值，默认0.95
    :param out_fields: 查询字段列表，默认返回['params']
    :return: 标注字段内容（params）或 None
    """
    client = MilvusClient(milvus_host)
    # 默认只查 params 字段
    if out_fields is None:
        out_fields = ['params']
    response = client.search_vector(
        collection_name="agent_chat_annotation_vector",
        vectors=user_question,  # 直接传文本，后端负责向量化
        metric_type="COSINE",
        top_k=1,
        out_fields=out_fields,
    )
    if response.get('success') and response.get('result'):
        hits = response['result'][0]
        for hit in hits:
            # milvus返回的距离字段有时叫 distance，有时叫 score，需兼容
            distance = hit.get('distance') or hit.get('score')
            if distance is not None and distance >= sim_threshold:
                params = json.loads(hit.get('params'))
                params.pop('caseType', None)
                logger.debug(f"找到匹配数据，相似度:{distance},标注字段:{params}")
                return params
    return None


def remove_duplicates_by_param_name(data_list):
    unique_items = {}
    for item in data_list:
        # 修改键名
        item["指标名称"] = item.pop("indicators_name", None)
        item["配置项"] = item.pop("mapper_fields", None)
        item["可选值"] = item.pop("optional_value", None)
        item["指标别名"] = item.pop("indicators_name_alias", None)
        item["指标类型"] = item.pop("data_type", None)
        item["指标解释"] = item.pop("description", None)
        # 以 "指标名称" 和 "可选值" 为依据去重
        key = (item.get('指标名称'), item.get('可选值'))
        if key not in unique_items:
            unique_items[key] = item
    # 返回去重后的对象列表
    return list(unique_items.values())


def get_company_code(question, history=None):
    """
    获取问题中的公司证券代码
    参数:
    question (str): 包含公司名称的问题
    history (list): 历史对话记录，默认为空列表
    返回:
    dict: API 返回的响应内容
    """
    domain = os.getenv('ZHIYI_HOST')
    url = domain + '/get_company_code'
    data = {
        "question": question,
        "history": history if history is not None else []
    }
    response = requests.post(url, json=data)
    # 确保请求成功
    response.raise_for_status()
    return response.json()


def format_filter_conditions(filters: Dict[str, Union[str, Dict[str, Any]]]) -> str:
    if not filters:
        return ""
    formatted_conditions = []

    for field, condition in filters.items():
        if isinstance(condition, dict) and "keywords" in condition:  # 检查字典且包含 "keywords" 键
            # 文本匹配条件 (字典形式)
            keywords = condition["keywords"]
            op = condition.get("op", "and")  # 默认 "and" 如果 "op" 没指定

            if op == "and":
                formatted_conditions.append(f"{field} 全包含关键词 '{keywords}'")
            elif op == "or":
                formatted_conditions.append(f"{field} 包含任一关键词 '{keywords}'")
        elif isinstance(condition, dict):
            # 数值范围条件
            range_conditions = []
            for operator, value in condition.items():
                if operator == "op":
                    continue  # 跳过逻辑运算符
                if operator == "gt":
                    range_conditions.append(f"{field} > {value}")
                elif operator == "lt":
                    range_conditions.append(f"{field} < {value}")
                elif operator == "gte":
                    range_conditions.append(f"{field} ≥ {value}")
                elif operator == "lte":
                    range_conditions.append(f"{field} ≤ {value}")
                elif operator == "equal":
                    range_conditions.append(f"{field} = {value}")
            # 使用 "并且" 或 "或者" 连接多个条件
            if "op" in condition:
                logical_op = condition["op"]
                if logical_op.lower() == "and":
                    range_conditions_str = " 并且 ".join(range_conditions)
                elif logical_op.lower() == "or":
                    range_conditions_str = " 或者 ".join(range_conditions)
                else:
                    range_conditions_str = f" {logical_op} ".join(range_conditions)
            else:
                range_conditions_str = " 并且 ".join(range_conditions)
            formatted_conditions.append(range_conditions_str)

    return "，".join(formatted_conditions)


def process_special_fields(filters: Optional[Dict[str, Union[str, Dict[str, Any]]]]) -> Tuple[
    Dict, Dict[str, List[Dict]]]:
    """
    处理所有特殊字段的筛选条件

    :param filters: 原始筛选条件
    :return: 更新后的筛选条件和各字段处理结果的字典
    """
    if not filters:
        return {}, {}

    updated_filters = copy.deepcopy(filters)
    results_by_field = {}

    # 遍历过滤条件中的每个字段
    for field, condition in list(filters.items()):
        # 检查字段是否在配置中
        if field in FIELD_MAPPERS and isinstance(condition, dict) and "keywords" in condition:
            keywords = [k.strip() for k in condition["keywords"].split() if k.strip()]
            if not keywords:
                continue

            # 获取字段配置
            field_config = FIELD_MAPPERS[field]

            # 执行查询
            matched_items = query_ingredients_by_keywords(**field_config["query_param"])

            # 处理匹配结果
            formatted_results = []
            matched_keywords = []

            for keyword in keywords:
                for item in matched_items:
                    # 简化的匹配逻辑，根据需要可以调整
                    name_field = field_config.get("name_field")
                    if keyword == item.get(name_field, ""):
                        formatted_results.append({
                            "mapper": field_config.get("mapper"),
                            "code": item.get("code")
                        })
                        matched_keywords.append(keyword)
                        break

            # 保存结果
            if formatted_results:
                results_by_field[field] = formatted_results

                # 更新filters，移除已匹配的关键词
                unmatched_keywords = [k for k in keywords if k not in matched_keywords]
                if not unmatched_keywords:
                    del updated_filters[field]
                else:
                    updated_filters[field]["keywords"] = " ".join(unmatched_keywords)

    return updated_filters, results_by_field


def build_request_body(query_params: Dict[str, Any], case_type: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    构建完整的请求体，支持多指标和复杂筛选条件。

    :param query_params: 查询参数字典，包含 metrics 和 filters
    :param case_type: 案例类型编码(1:IPO 2:公开增发 3:可转债发行 4:资产重组 5:配股发行 6:非公开发行 7:违规案例 8:重组标的)，用于查询mysql数据库
    :return: 构建好地请求体，如果没有结果则返回 None
    """
    target_list = get_case_browser_params_from_mysql(case_type)
    target_data = {}
    for target in target_list:
        target_data[target.get("indicators_name")] = {
            "param_type": target.get("case_type_name"),
            "param_name": target.get("indicators_name"),
            "mapper": target.get("mapper"),
            "description": target.get("description"),
            "param_alias": target.get("indicators_name_alias"),
            "data_type": target.get("data_type"),
            "optional_value": target.get("optional_value"),
            "hot_score": target.get("hot_score"),
            "is_required": target.get("is_required"),
            "sort": target.get("sort"),
            "mapper_fields": target.get("mapper_fields"),
        }

    # 从 query_params 获取要查询的指标和筛选条件
    metrics = query_params.get("metrics", [])
    filters = query_params.get("filters", {})
    sort_by = query_params.get("sort_by")
    sort_order = query_params.get("sort_order", "asc").upper()

    # 构建 quotas 列表，包含每个指标的 mapper, label, data_type 等信息
    quotas: List[Dict[str, Any]] = [
        {"label": "案例ID", "mapper": "mapper.case_id", "mapper_fields": [], "date_type": "STRING"}]
    # 把 filters 中的 key 和排序字段添加到 metrics
    if filters:
        metrics.extend([k for k in filters.keys() if k not in metrics])
    # 添加排序字段到 metrics（如果存在且不在 metrics 中）
    if sort_by and sort_by not in metrics:
        metrics.append(sort_by)
    # 将必查字段加进metrics中
    required_metrics = [k for k, v in target_data.items() if v.get("is_required") == '1']
    metrics.extend(required_metrics)
    metrics = list(dict.fromkeys(metrics))
    # 查询出全部关联指标
    association_metrics = get_association_metrics_from_mysql(case_type, metrics)
    for association_metric in association_metrics:
        metrics.extend(association_metric.get('related_params').split(" "))
    metrics = list(dict.fromkeys(metrics))

    for metric in metrics:
        # 从 target_data 中找到指标相关信息
        target_info = target_data.get(metric)
        if not target_info:
            if metric == '次数':
                return {"error": '次数指标不存在，根据返回数据条数判断即可'}
            else:
                return {
                    "error": f"指标异常: {metric}。该指标可能为模型自动生成，建议尝试替代指标进行重试，或向用户从指标列表中确认正确的 '{metric}' 指标。"}

        # 如果该指标有对应的筛选条件，构建 mapper_screens
        mapper_screens = build_mapper_screens({metric: filters[metric]}) if filters and metric in filters else None
        quotas.append(create_quota_entry(target_info, mapper_screens))

    if not quotas:
        print("未找到有效的指标")
        return {"error": "未找到有效的指标"}

    # 处理排序
    if sort_by:
        sort_mapper = next((quota["mapper"] for quota in quotas if quota["label"] == sort_by), None)
        if sort_mapper:
            sorts = [{
                "mapper": sort_mapper,
                "direction": "ASC" if sort_order == "ASC" else "DESC"
            }]
        else:
            return {"error": f"未找到排序字段: {sort_by}"}
    else:
        # 如果没有提供 sort_by，使用默认排序
        sorts = [{"mapper": "mapper.case_id", "direction": "DESC"}]

    # 构建排序和其他部分
    return {
        "title": query_params.get("title", "数据浏览器"),
        "quotas": quotas,
        "ingredients": [],
        "sorts": sorts,
        "selections": [],
    }


def build_mapper_screens(filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据 filters 构建 mapper_screens 条件。
    :param filters: 查询参数中的筛选条件部分
    :return: 构建好的 mapper_screens 结构
    """
    conditions = []
    formula_map = {"gt": "GT", "gte": "GTE", "lt": "LT", "lte": "LTE", "equal": "EQ"}

    for field, condition in filters.items():
        if isinstance(condition, dict) and "keywords" in condition:
            logic = condition.get('op', 'AND').upper()  # 文本条件的 logic 默认也是 AND，保持一致
            keywords_str = condition["keywords"]
            conditions.append({"logic": logic, "formula": "CONTAINS", "val": keywords_str})  # val 直接传递关键词字符串

        elif isinstance(condition, dict):  # 数值/时间范围条件 (保持不变)
            logic = condition.get('op', 'AND').upper()
            for formula, val in condition.items():
                if formula in formula_map:
                    conditions.append({"logic": logic, "formula": formula_map[formula], "val": val})
        elif isinstance(condition, str):
            conditions.append({"logic": "AND", "formula": "CONTAINS", "val": condition})

    return {"mode": "CUSTOM", "condition": conditions}


def create_quota_entry(target_info: Dict[str, Any], mapper_screens: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    根据目标信息和 mapper_screens 创建配额条目。
    :param target_info: 目标指标的信息
    :param mapper_screens: 指标的 mapper_screens（如果有）
    :return: 构建好的配额条目
    """
    # 处理 mapper_fields，检查是否为字符串并尝试解析为列表
    mapper_fields = target_info.get("mapper_fields") or []
    if isinstance(mapper_fields, str):
        try:
            # 如果字符串有效，则解析为列表
            mapper_fields = json.loads(mapper_fields)
        except json.JSONDecodeError:
            # 如果解析失败，保持为原始字符串或根据需求处理
            mapper_fields = []

    # 初始化拼接后的部分
    appended_values = []

    # 遍历 mapper_fields 并提取 value
    for field in mapper_fields:
        if "value" in field:
            appended_values.append(str(field["value"]))

    # 如果存在 value，将它们用 '_' 拼接并添加到 mapper 字段的末尾
    if appended_values:
        target_info["mapper"] += "." + "_".join(appended_values)

    # 构建配额条目
    quota_entry = {
        "label": target_info["param_name"],
        "mapper": target_info["mapper"],  # 使用修改后的 mapper
        "mapper_fields": mapper_fields,  # 使用转换后的 mapper_fields
        "date_type": target_info["data_type"]
    }
    # 如果target_info的sort存在切不为空,则quota_entry里面也要放入sort
    if "sort" in target_info and target_info["sort"]:
        quota_entry["sort"] = target_info["sort"]

    if mapper_screens:
        quota_entry["mapper_screens"] = mapper_screens

    return quota_entry


async def make_post_request(body):
    """发送 POST 请求并处理可能的错误。"""
    try:
        # 定义 API 的 URL 和请求头
        api_url = "https://services.easy-board.com.cn/tdb-api/tdbf/quota?page=0&size=10"
        access_token = ''
        headers = {
            "authorization": access_token,
            "Content-Type": "application/json;charset=UTF-8"
        }
        response = requests.post(api_url, headers=headers, json=body)

        # 如果状态码是 401，需要重新获取 access_token
        if response.status_code == 401:
            # 获取新的 access_token
            remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')
            new_access_token = get_access_token()
            headers.update({"authorization": new_access_token})
            response = requests.post(api_url, headers=headers, json=body)
        if response.status_code == 200:
            result = response.json()
            # 发送请求体、url、结果 信息
            await send_request_body_feedback(body, api_url, result)
            result = truncate_values(result, max_length=500)
            # 判断返回结果是否为空
            if result['total'] == 0:
                return {
                    "error": "没有找到符合当前查询条件的数据。",
                    "next_action": (
                        "请根据以下建议，尝试调整并重新生成搜索条件："
                        "1. 检查查询条件表达是否准确、完整，避免使用缩略语、简称或不规范表述，建议采用更标准、更正式的术语。"
                        "2. 适当放宽条件（如更宽泛的关键词、去掉部分过滤、扩大时间范围等）。"
                        "3. 利用优化后的新条件，重新进行数据查询。"
                        "4. 如果优化条件后尝试两次仍未获得结果，再切换为使用外部 Bing 搜索工具。"
                        "注意：LLM请主动根据上述策略修正和迭代查询"
                    )
                }
            # 处理数据
            result = {
                "案例总数": result['total'],
                "每页条数": result['size'],
                "当前页码": result['page'] + 1,
                "案例列表": [clean_dict(line) for line in result['result']],
            }
            # 调用替换函数
            result = replace_keys(result, compiled_mappers)
            logger.info(
                f"[make_post_request] 查询结果\n"
                f"案例总数{result['案例总数']},案例信息demo{result['案例列表']}")

            return result
        else:
            return {"error": f"请求失败，状态码: {response.status_code}, 信息: {response.text}"}
    except requests.exceptions.RequestException as e:
        return {"error": f"请求出错: {e}"}


def get_access_token():
    r_key = 'CASE_SEARCH_ACCESS_TOKEN'
    r = get_redis_connect()
    if r.exists(r_key):
        access_token = r.get(r_key).decode('utf-8')
    else:
        resp = requests.get(
            'https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=610abd52fac84f60&client_secret=4ea53ec5610abd52fac84f60a02bcf4c')
        access_token = resp.json().get('access_token')
        logger.info(f'get_code_map r_key: {r_key} {access_token}')
        r.set(r_key, access_token, ex=60 * 30)
    return access_token


def truncate_values(obj, max_length=100, ellipsisStr="..."):
    """
    截断字典或列表中的过长值。

    Args:
        obj: 需要处理的对象，可以是字典、列表或其它基本类型。
        max_length: 值的最大长度，默认为100。
        ellipsisStr: 截断后添加的省略号，默认为 "..."。

    Returns:
        返回一个新的对象，其中过长的值已被截断。
    """
    if isinstance(obj, dict):
        return {k: truncate_values(v, max_length, ellipsisStr) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [truncate_values(elem, max_length, ellipsisStr) for elem in obj]
    else:
        if isinstance(obj, str) and len(obj) > max_length:
            return obj[:max_length - len(ellipsisStr)] + ellipsisStr
        elif isinstance(obj, (list, dict)):
            return obj
        elif obj is None:
            return None
        elif isinstance(obj, (int, float, bool)):
            return obj
        elif len(str(obj)) > max_length:
            return str(obj)[:max_length - len(ellipsisStr)] + ellipsisStr
        else:
            return obj


async def send_request_body_feedback(request_body: Dict[str, Any], url: str, result: Dict[str, Any]):
    """
    发送请求体、url、结果 信息到客户端。

    Args:
        request_body (Dict[str, Any]): 请求体。
        url (str): 请求的 URL。
        result (Dict[str, Any]): 请求结果。
    """
    writer = get_stream_writer()
    # 创建包含所有信息的 JSON 对象，并添加 message_type
    message = {
        "AI_AGENT_RESPONSE": {
            "AI_AGENT_BODY": request_body,
            "AI_AGENT_URL": url,
            "AI_AGENT_RESULT": result
        }
    }
    # 将 JSON 对象序列化为字符串并发送
    writer(json.dumps(message, ensure_ascii=False))


def clean_dict(d):
    if not isinstance(d, dict):
        return d
    result = {}
    for k, v in d.items():
        cleaned_v = clean_dict(v)
        if cleaned_v not in ("", {}, []):
            result[k] = cleaned_v
    return result


def replace_keys(obj, mapper):
    """
    将字典中的键名替换为中文

    Args:
        obj: 要处理的对象（字典、列表或其他类型）
        mapper: 映射字典，键是原始路径（如 'company.securities_code.newest'），值是中文键名

    Returns:
        处理后的对象，键名已替换为中文
    """
    # 预处理映射字典，提取字段映射
    field_mappers = {}
    for path, value in mapper.items():
        parts = path.split('.')
        if len(parts) >= 2:
            # 对于形如 'company.securities_code.newest' 的路径，提取 'securities_code'
            if len(parts) >= 3 and parts[-1] == 'newest':
                field_key = parts[-2]  # 取倒数第二个部分
                field_mappers[field_key] = value
            # 对于形如 'company.securities_code' 的路径，提取 'securities_code'
            else:
                field_key = parts[-1]  # 取最后一个部分
                field_mappers[field_key] = value

    # 处理函数
    def process(obj):
        # 处理列表
        if isinstance(obj, list):
            return [process(item) for item in obj]

        # 处理字典
        if isinstance(obj, dict):
            result = {}

            for k, v in obj.items():
                # 特殊处理 'newest' 键
                if k == 'newest':
                    result['最新'] = v
                else:
                    # 检查是否有字段映射
                    new_key = k
                    if k in field_mappers:
                        new_key = field_mappers[k]

                    # 递归处理值
                    result[new_key] = process(v)

            return result

        # 其他类型直接返回
        return obj

    return process(obj)
