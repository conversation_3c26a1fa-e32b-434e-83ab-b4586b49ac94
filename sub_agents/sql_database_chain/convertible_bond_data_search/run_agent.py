import traceback
import uuid

from utils.load_env import logger
from sub_agents.sql_database_chain.convertible_bond_data_search.convertible_bond_langgraph import convertible_bond_langgraph


async def chat(message, history):
    try:
        # 配置
        config = {
            "recursion_limit": 15,
            "configurable": {
                "thread_id": str(uuid.uuid4())
            }
        }

        # 启动对话
        answer = await convertible_bond_langgraph.ainvoke(
            {"messages": ("user", message)},
            config,
            stream_mode="values",
            debug=True
        )

        answer = answer["messages"][-1].content
        return answer
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{e}"


if __name__ == '__main__':
    import gradio as gr

    # 创建Gradio接口
    iface = gr.ChatInterface(
        chat,
        chatbot=gr.Chatbot(elem_id="chatbot", render=False),
        textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
        title="可转债案例库助手对话Demo",
        description="这是一个使用Gradio创建的案例库助手对话界面",
        theme="soft",
        cache_examples=True,
        css="""
        #chatbot {
            height: calc(100vh - 280px) !important;
            overflow-y: auto;
        }
        """
    )

    logger.info("可转债案例AGENT")

    iface.launch(
        server_name="0.0.0.0",
        inbrowser=True,
        server_port=7862,
        debug=True
    )
