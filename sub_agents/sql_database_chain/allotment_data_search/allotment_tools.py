import asyncio
import json
from typing import List, Dict, Any, Union, Optional, Annotated

from langchain_core.messages import ToolMessage
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.config import get_stream_writer
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from sub_agents.sql_database_chain.allotment_data_search.allotment_utils import \
    send_processing_feedback, get_target_list_for_llm, remove_duplicates_by_param_name, get_company_code, \
    format_filter_conditions, build_request_body, make_post_request, vector_case_search, extractor, \
    process_special_fields, vector_qa_search
from utils.db_tool import get_case_browser_params_from_mysql
from utils.load_env import logger
from utils.my_bing_search import search


@tool
async def fetch_target_info(input_text: str):
    """从用户输入中提取【配股】相关的候选指标名称极其解释。

    参数:
    input_text (str): 用户输入的文本 HumanMessage

    返回:
    str: 指标列表，结果以字符串形式返回。
    """
    logger.debug(f"[配股] 用户输入：{input_text}")

    # 发送流程消息
    await send_processing_feedback(input_text)
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 获取参数列表 (1:IPO 2:公开增发 3:可转债发行 4:资产重组 5:配股发行 6:非公开发行 7:违规案例 8:重组标的)
    target_list = get_case_browser_params_from_mysql('5')
    filtered_target_list = [
        {
            **{field: item.get(field) for field in
               ["indicators_name", "data_type", "optional_value", "description", "mapper_fields", "indicators_name_alias"]}
        }
        for item in target_list
    ]

    # 并行执行任务 - 立即开始最耗时的异步任务，让它在后台运行
    llm_target_task = asyncio.create_task(
        get_target_list_for_llm(input_text, str(filtered_target_list))
    )

    # 同步执行向量搜索（因为search_data是同步函数）
    mv_target = vector_case_search(query=input_text)
    annotated_params = vector_qa_search(input_text)
    logger.debug('[配股发行]向量库选取的指标：' + str(mv_target))
    # 此时才等待最耗时的llm_target任务结果，让它有最大的后台运行时间
    llm_target = await llm_target_task
    logger.debug('[配股发行]大模型选取的指标：' + str(llm_target))
    # 合并结果并去重
    combined_results = llm_target + mv_target
    unique_combined_results = remove_duplicates_by_param_name(combined_results)

    # 提取证券代码
    company_code = get_company_code(input_text)['result']
    # 提取中介机构
    mediator_organizations = extractor.extract_organizations(input_text)

    result_dict = {
        "指标列表": unique_combined_results,
        **({"参考入参": annotated_params} if annotated_params else {}),
        **({"证券代码": company_code} if company_code else {}),
        **({"中介机构": mediator_organizations} if mediator_organizations else {})
    }
    return json.dumps(result_dict, ensure_ascii=False)


@tool
async def query_data(metrics: List[str], filters: Optional[Dict[str, Union[str, Dict[str, Any]]]] = None,
                     sort_by: Optional[str] = "首次方案公告日", sort_order: str = 'desc',
                     tool_call_id: Annotated[str, InjectedToolCallId] = None,
                     state: Annotated[dict, InjectedState] = None) -> Command:
    """
    查询数据的方法，支持多指标和复杂筛选条件，在使用本方法前需要调用fetch_target_info。

    :param metrics: List[str] 要查询的指标名称列表
    :param filters: Dict[str, Union[str, Dict[str, Any]]] 筛选条件(必填)
        文本类型:  使用字典表示关键词匹配，{"keywords": "空格分隔的关键词", "op": "and/or", }，"and"全包含，"or"或包含。
        数值/时间类型: 使用字典表示范围 {"equal/gt/lt/gte/lte": 值, "op": "and/or"}
    :param sort_by: Optional[str] 排序字段，默认使用时间金额类型指标
    :param sort_order: str 排序方式，'asc'升序或'desc'降序，默认'desc'
    :return: 查询结果字典，包含查询到的数据、总数、当前页码等
    示例:
    metrics = ["首次方案公告日", "案例最新进程"]
    filters = {
        "最新方案公告日": {"gt": "2024-03-01", "lt": "2023-12-31", "op": "or"},
        "证券代码": {"keywords": "000000 000001", "op": "or"},
        "案例名称": {"keywords": "价值 在线", "op": "and"},
        "销售额": {"gt": num, "lt": num, "op": "and"}
    }
    result = query_data(metrics, filters, sort_by="销售额", sort_order="desc")
    """
    # 格式化筛选条件
    formatted_conditions = format_filter_conditions(filters) if filters else ""
    filters_description = f"[{formatted_conditions}]" if formatted_conditions else ""

    # 发送流程消息
    writer = get_stream_writer()
    writer(
        json.dumps({"AI_AGENT_FLOW": f"正在为您精准筛选符合{filters_description}条件的数据..."}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟
    # 发送标注消息
    writer(
        json.dumps({
            "AI_AGENT_ORIGINAL_PARAMS": {
                "caseType": "5",
                "metrics": metrics,
                "filters": filters,
                "sort_by": sort_by,
                "sort_order": sort_order
            }
        }, ensure_ascii=False)
    )
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 获取当前的 query_result_num
    query_result_num = state.get('query_result_num', 0)
    query_result_num += 1
    # 判断是否发送query_result_num
    if query_result_num > 1:
        writer(json.dumps({"QUERY_RESULT_NUM": query_result_num}, ensure_ascii=False))
        await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 构建请求体
    metrics.extend(['案例名称', '首次方案公告日', '最新方案公告日', '案例最新进程', '审核结果', '配售对象'])

    # 处理成分
    updated_filters, results_by_field = process_special_fields(filters)
    logger.debug(f"Updated filters: {updated_filters}")
    logger.debug(f"Results by field: {results_by_field}")
    query_params = {
        "metrics": metrics,
        "filters": updated_filters,
        "sort_by": sort_by,
        "sort_order": sort_order
    }
    body = build_request_body(query_params, "5")

    if not body or 'error' in body:
        return Command(
            update={
                # 更新query_result_num
                "query_result_num": query_result_num,
                # 同时更新消息历史
                "messages": [ToolMessage(content=str(body), tool_call_id=tool_call_id)]
            }
        )

    # 处理成分
    if 'ingredients' not in body:
        body['ingredients'] = []
    body['ingredients'].append({"code": "2", "mapper": "ingredient.case_type"})
    body['logic'] = 'AND'
    if results_by_field:
        for field, values in results_by_field.items():
            body['ingredients'].extend(values)

    logger.debug('[配股发行]构造的请求体：' + str(body))
    result = await make_post_request(body)

    writer(json.dumps({"QUERY_RESULT": result}, ensure_ascii=False, indent=2))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    return Command(
        update={
            # 更新violation_num
            "query_result_num": query_result_num,
            # 同时更新消息历史
            "messages": [ToolMessage(content=str(result), tool_call_id=tool_call_id)]
        }
    )


@tool
async def bing_search(query: str) -> str:
    """
    提供Bing搜索功能，用于处理用户提出的问题。生成查询内容覆盖更全面的信息。
    在以下情况启用此功能：
    1.当其他工具查询失败或结果不适用时
    2.根据判断，其他工具不适合处理当前问题。
    3.行业分析，概念统计

    Args:
        query: 搜索内容。

    Returns:
        返回Bing搜索的结果
    """
    # 发送流程消息
    writer = get_stream_writer()
    writer(
        json.dumps({"AI_AGENT_FLOW": f"正在网页中查询[{query}]..."}, ensure_ascii=False))
    writer(json.dumps({"DATA_SOURCE": "BING"}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    # 限制查询的域名,注意结尾的空格
    site = (" site:finance.eastmoney.com OR site:caam.org.cn OR site:stats.gov.cn OR site:cs.com.cn OR site:cls.cn "
            "OR site:csrc.gov.cn OR site:sse.com.cn OR site:szse.cn OR site:bse.cn OR site:pbc.gov.cn OR site:mof.gov.cn "
            "OR site:ndrc.gov.cn OR site:sasac.gov.cn OR site:neeq.com.cn OR site:finance.caixin.com OR site:cfi.cn "
            "OR site:cninfo.com.cn OR site:jjckb.xinhuanet.com OR site:jiemian.com OR site:zqrb.cn "
            "OR site:finance.sina.com.cn OR site:people.com.cn OR site:cnstock.com OR site:news.qq.com "
            "OR site:miit.gov.cn OR site:cnr.cn OR site:gov.cn OR site:financialnews.com.cn OR site:finance.china.com.cn "
            "OR site:gmw.cn OR site:chnfund.com OR site:thepaper.cn OR site:sohu.com")

    query += site

    results = search.results(query, 10)
    writer(
        json.dumps({"AI_AGENT_BING_DATA": results}, ensure_ascii=False))
    await asyncio.sleep(0.1)  # 让出给事件循环而不实际延迟

    all_results = {
        "query_results": str(results),
    }

    return str(all_results)  # 返回一个包含所有查询结果的字典字符串


allotment_tools = [fetch_target_info, query_data, bing_search]
