import json
import traceback
from typing import Optional

from langchain.chains.llm import <PERSON><PERSON>hai<PERSON>
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain_core.runnables import RunnableConfig, RunnableSequence
from langchain_core.tools import tool
from langchain_deepseek import ChatDeepSeek

from common_tools.common_agent_tools import bing_search
from sub_agents.rep.ann.rep_ann_utils import query_ann_list
from sub_agents.rep.laws.rep_laws_utils import query_laws_vector
from utils.llm_utils import get_a_llm
from utils.load_env import logger

# llm = ChatDeepSeek(llm_type='hs-deepseek-r1')
llm = ChatDeepSeek(model='hs-deepseek-r1',
                   api_base="https://model-hub.valueonline.cn/v1",
                   api_key='sk-1kYMbM00yy6Cr2o1C60a1fCc6a1e48859868655f30EaE320')


@tool
def get_laws_info(keywords: list, law_range: Optional[str], config: RunnableConfig) -> str:
    """
    根据回购业务法规相关的关键词采用向量检索关联的中国法规

    :param keywords 根据用户问题检索相关法规法条的关键词。注意：
                     - keywords 应尽量贴近法规中的正式表述。
                     - 如果用户问题中关键词不够准确（如“敏感期”），需要扩展相关术语。
                     - 使用此工具检索时，建议结合具体场景和法规上下文对关键词进行改写扩展。

    :param law_range: 法规所属的板块，多个取值用','拼接，可选值：深交所主板,深交所创业板,上交所主板,上交所科创板,北交所。
                      默认值为深交所主板,深交所创业板,上交所主板,上交所科创板,北交所
    :return: 查询出的法规
    """
    try:
        result = query_laws_vector(keywords, law_range, config)
        logger.info(f'get_laws_info, result:{result}]')
        if not result:
            return '未找到相关法律或规定'
        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
def get_ann_list(config: RunnableConfig,
                 keywords: list[str],
                 code_name: Optional[str] = '',
                 sector: Optional[str] = '') -> str:
    """
    查询用户问题相关联的公告
    :param keywords 从用户问题中提取回购业务的关键词，应贴近法规中的正式表述，必填，关键词本身字数最好为2个字，关键词总数量为5个以下
    :param code_name: 公司代码或名称，非必填
    :param sector: 公告所属的板块，非必填，取值范围：深交所主板、深交所创业板、上交所主板、上交所科创板、北交所
    :return: 查询出的公告
    """
    try:
        result = query_ann_list(code_name, sector, keywords, config)
        if not result:
            return '未找到相关公告'
        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
def deepThinking(user_query: str) -> str:
    """
    这个工具用于对用户的问题进行深度思考和分析，并生成一个完整的执行步骤。
    它将生成一个分析计划，包括：
        1.  对用户问题的理解。
        2.  详细的分析框架，即解决问题的步骤和关键信息点。
        3.  需要查询的信息类型（例如：法律法规、市场数据等），并明确数据来源（法规、公告、bing）。

    Args:
        user_query: 用户提出的问题。

    Returns:
        一个JSON字符串，包含分析计划。  JSON 结构如下：
        {
            "understanding": "对用户问题的理解的总结",
            "analysis_framework": [
                {"step": 1, "description": "解决问题的第一个步骤", "required_information": [{"type": "信息类型1", "source": "法规"}, {"type": "信息类型2", "source": "案例"}]},
                {"step": 2, "description": "解决问题的第二个步骤", "required_information": [{"type": "信息类型3", "source": "公告"}]}
                # ... 更多步骤
            ],
            "information_needs": [{"type": "信息类型1", "source": "法规"}, {"type": "信息类型2", "source": "bing"}, {"type": "信息类型3", "source": "公告"}, ...]
        }
    """
    human_message_prompt_template = HumanMessagePromptTemplate.from_template("{user_query}")
    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content=("""
            <role>
    你是精通回购相关业务的业务专家，精通回购业务所有的专业知识。
    针对用户关于回购决策、实施细节、法规合规等各个方面的问题进行分析，找出若干种适合回答用户问题的函数，并根据提供的python函数说明选择出对应的函数名以及填写好的参数。
    若用户问题不能让你清晰的选择工具，请主动询问更多细节以澄清需求。
    </role>
    
    <principle>
    遵循以下逻辑来处理用户问题：
    1. 问题分析：深入理解用户问题的核心，拆解出来用户需求的本质和细节（如信息披露、股东权益、回购、收购等）。
    2. 检索相关资料：
        - 思考如何能够满足用户的需求，设计规划和具体的执行步骤。
        - 尽可能根据需求提供更为深入的数据检索，需要数据支持的时候，按照 法规与流程 > 公告 > 案例 的优先级进行检索，用来支撑回答的可信程度。
        - 关于回购案例的全流程步骤、需要的文件等详细信息，可以协调 RepLawsAgent 获取，以获得更好的回答效果。
        - 如果用户的问题涉及多个方面，可以调用多个函数并补充好函数对应的参数。
    </principle>
    
    <key_guidelines>
    - 仔细解析用户请求，考虑查询的主题、复杂度和潜在影响
    - 基于函数的功能说明，可以选择若干个合适的函数
    - 在选择函数时，将用户问题中包含的模糊时间段（如“最近一周”、“最近一个月”、“今年”）转换为精确的日期
    - 确保所有时间转换准确且基于：{datetime.now().strftime("%Y年%m月%d日 %H点%M分")}
    - 若不能确定选择什么函数则禁止返回json格式，否则必须仅返回json格式
    - JSON格式必须为无格式的JSON，回答内容将直接进行Python的对象转换
    - 对用户进行反问时，禁止回答选择函数相关操作，根据函数说明让用户补充相关内容即可
    </key_guidelines>
    
    <available_function>
    {
        'get_ann_list': {
            'Function Description ': '获取市场上回购相关的公告数据',
            'Parameters': {
                'keywords': {
                    'Description': '从用户问题中提取回购业务的关键词，应贴近法规中的正式表述',
                    'type': 'list',
                    'Required': 'Y',
                    'Additional Requirements': '关键词字数建议2个字，关键词总数量为5个及以下',
                },
                'code_name': {
                    'Description': '公告所属的公司代码或名称',
                    'type': 'str',
                    'Required': 'N',
                },
                'sector': {
                    'Description': '公告所属的板块',
                    'type': 'str',
                    'Required': 'N',
                    'Additional Requirements': '取值范围：深交所主板、深交所创业板、上交所主板、上交所科创板、北交所',
                },
            }
        },
        'get_laws_info': {
            'Function Description ': '获取回购业务相关的法律法规',
            'Parameters': {
                'keywords': {
                    'Description': '根据用户问题检索相关法规法条的关键词',
                    'type': 'list',
                    'Required': 'Y',
                    'Additional Requirements': '建议结合具体场景对关键词进行改写或扩展',
                },
                'law_range': {
                    'Description': '法规所属的板块',
                    'type': 'str',
                    'Required': 'N',
                    'Additional Requirements': '取值范围：深交所主板、深交所创业板、上交所主板、上交所科创板、北交所',
                },
            }
        },
        'get_process_guide': {
            'Function Description ': '获取回购业务过程的相关指引操作',
            'Parameters': {
                'sector_name': {
                    'Description': '法规所属的板块',
                    'type': 'str',
                    'Required': 'N',
                    'Additional Requirements': '可选值：创业板,深主板,沪主板,科创板,北交所',
                },
            }
        },
    }
    </available_function>
    
    <output_example>
    {
        'get_ann_list': {
            'keywords': ['首次', '注销'],
            'code_name': None,
            'sector': '深交所主板',
        },
        'get_laws_info': {
            'keywords': ['首次', '注销'],
            'law_range': '深交所主板',
        },
        'get_process_guide': {
            'sector_name': '深主板',
        }
    }
    </output_example>
            """)),
        human_message_prompt_template
    ])

    chain = prompt | llm
    try:
        result = chain.invoke({"user_query": user_query})
        return result  # 直接返回结果，不做校验
    except (json.JSONDecodeError, ValueError) as e:
        return f"Error generating analysis plan: Invalid JSON format or other error. Please review the prompt and/or model output: {e}\n Model output: {result}"


r1_tools = [get_laws_info, get_ann_list, bing_search, deepThinking]
