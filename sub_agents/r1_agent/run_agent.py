# -*- coding: utf-8 -*-
"""
@Project ：agentTools
@File    ：run_agent.py
@Date    ：2024/12/16 下午2:44
@Desc    ：
"""
import asyncio
import traceback
import uuid
from typing import Annotated

from langchain_core.runnables import Runnable, RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START
from langgraph.graph.message import AnyMessage, add_messages
from langgraph.prebuilt import tools_condition
from typing_extensions import TypedDict

from sub_agents.r1_agent.r1_agent import r1_assistant_runnable
from sub_agents.r1_agent.r1_tools import r1_tools
from utils.load_env import logger

logger.info("回购案例AGENT")
from sub_agents.graph_utils import create_tool_node_with_fallback


class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]


class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable

    def __call__(self, state: State, config: RunnableConfig):
        while True:
            configuration = config.get("configurable", {})
            passenger_id = configuration.get("passenger_id", None)
            state = {**state, "user_info": passenger_id}
            result = self.runnable.invoke(state)
            # If the LLM happens to return an empty response, we will re-prompt it
            # for an actual response.
            if not result.tool_calls and (
                    not result.content
                    or isinstance(result.content, list)
                    and not result.content[0].get("text")
            ):
                messages = state["messages"] + [("user", "Respond with a real output.")]
                state = {**state, "messages": messages}
            else:
                break
        return {"messages": result}


builder = StateGraph(State)
builder.add_node("assistant", Assistant(r1_assistant_runnable))
builder.add_node("tools", create_tool_node_with_fallback(r1_tools))
builder.add_edge(START, "assistant")
builder.add_conditional_edges(
    "assistant",
    tools_condition,
)
builder.add_edge("tools", "assistant")

memory = MemorySaver()
allotment_graph = builder.compile(checkpointer=memory)

import gradio as gr


async def view_main(user_input):
    config = {
        "recursion_limit": 10,
        "configurable": {
            "thread_id": str(uuid.uuid4())
        }
    }

    try:
        answer = await allotment_graph.ainvoke(
            {"messages": ("user", user_input)},
            config,
            stream_mode="values", debug=True
        )
        answer = answer["messages"][-1].content
        return answer
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{e}"


def chat(message, history):
    bot_message = asyncio.run(view_main(message))
    return bot_message


# 创建Gradio接口
iface = gr.ChatInterface(
    chat,
    chatbot=gr.Chatbot(elem_id="chatbot", render=False),
    textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
    title="回购案例库助手对话Demo",
    description="这是一个使用Gradio创建的案例库助手对话界面",
    theme="soft",
    cache_examples=True,
    css="""
    #chatbot {
        height: calc(100vh - 280px) !important;
        overflow-y: auto;
    }
    """
)

if __name__ == '__main__':
    iface.launch(server_name="0.0.0.0",
                 inbrowser=True,
                 server_port=7862, debug=True)
