import base64
import json
import time

import pandas as pd
import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from bs4 import BeautifulSoup

access_token = ""

ENCRYPT_KEY = "hNkYmsBUvrTd3C3o"
IV = "mLZT7OIx1qOHZaPX"


def decrypt_data(encrypt_data: str) -> str:
    iv = base64.b64encode(IV.encode('utf-8')).decode('utf-8')
    encrypt_key = base64.b64encode(ENCRYPT_KEY.encode('utf-8')).decode('utf-8')
    key = ENCRYPT_KEY + IV
    rtn = encrypt_data.replace(key, "")
    try:
        key = base64.b64decode(encrypt_key)
        iv_bytes = base64.b64decode(iv)
        cipher = AES.new(key, AES.MODE_CBC, iv_bytes)
        encrypted = base64.b64decode(rtn)
        decrypted = cipher.decrypt(encrypted)
        original = unpad(decrypted, AES.block_size)
        return original.decode('utf-8')
    except Exception as e:
        print(f"AES decrypt error, caused by: {e}")
        return ''


def get_access_token():
    resp = requests.get(f'https://services.valueonline.cn/oauth/token'
                        f'?grant_type=client_credentials'
                        f'&response_type=token'
                        f'&client_id=610abd52fac84f60'
                        f'&client_secret=4ea53ec5610abd52fac84f60a02bcf4c')
    return resp.json().get('access_token')


def get_question_list():
    global access_token
    access_token = get_access_token()
    headers = {
        "authorization": access_token,
        "Content-Type": "application/json;charset=UTF-8",
        "x-tenant-info": "417SOVzTq-Dn1SHP0e4KyRPuVyZo1hMM-KCUDbN9HYnB22OdvISBfT-d4zl0nqc8F-CwzRTQt3mcHrsAaM-lXsYBVJOBfkWLSphq7l7cOQ0HL5IeqHHhuJjaBupwPp-Lzqv7NsTD1dj3HD7eCz6uHtwkNrlpllAB01OaZ-1HKkTaj2A5hhQMmfo1EpfcQyHiR4R_src7fBAXvg_GSXsoATFjhmUbgQf034o0OS1ixtiQH0Sewgnlxss6jVneS8X2x2alk_wFXl2F3_QWEOnzQL6iNBq5DY0bHB-iezfiBF0YL1OmA2do8K6zsFQIjoL5D3QUz02KeRbMqjIiTfDMzH2yNxDQnaCaHP9IQsqhuzDj2PXFPxx7-P61WE3ZS9JY7X_3B2K9PyPPsTDSEetpq6K_yc4Mt5h7hOQoqDYdBV4="
    }

    data = {
        "condition": {
            "selId": "",
            "keyAnd": "",
            "key": "",
            "keyNot": "",
            "stockBoardSelect": "04",
            "srcSelect": "",
            "askDateTime": [],
            "businessTypes": "745233697178926983",
            "showInvalidFlag": "1",
            "searchFlag": "true"
        },
        "orderByName": "",
        "orderByOrder": "",
        "start": 101,
        "startRow": 100,
        "pageSize": "100"
    }
    response = requests.post("https://services.easy-board.com.cn/compliance/complianceInit/complianceSearch",
                             headers=headers, json=data)
    if response.status_code == 200:
        try:
            res_results_str = decrypt_data(response.json())
            res_results = json.loads(res_results_str)
            data_list = res_results.get('result').get('complianceSearchTable').get('data')
            if data_list:
                qa_list = []
                for item in data_list:
                    question = BeautifulSoup(item.get('questionContent') , 'lxml').get_text(strip=True)
                    answer = BeautifulSoup(item.get('answerContent') , 'lxml').get_text(strip=True)
                    qa_list.append({'问题': question, '答案': answer})
                return qa_list
            else:
                print("返回数据中 data 列表为空。")
                return []
        except Exception as e:
            print(f"解析报错：{str(e)}")
            return []
        res_result = response.json().get('result')
        return []

    elif response.status_code == 401:
        time.sleep(1)
        return get_question_list()
    else:
        raise Exception(f"Error occurred while making HTTP request: {response.status_code}")


def save_to_excel(data, filename="questions_answers1.xlsx"):
    """
    将问题和答案数据保存到 Excel 文件中。

    Args:
        data (list): 包含字典的列表，每个字典包含 '问题' 和 '答案' 键。
        filename (str): Excel 文件名，默认为 "questions_answers1.xlsx"。
    """
    if not data:
        print("没有数据可以写入 Excel 文件。")
        return

    df = pd.DataFrame(data)
    try:
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已成功保存到 Excel 文件: {filename}")
    except Exception as e:
        print(f"保存到 Excel 文件时发生错误: {e}")


if __name__ == "__main__":
    try:
        questions_answers_data = get_question_list()
        if questions_answers_data:
            save_to_excel(questions_answers_data)
        else:
            print("未能获取到问题和答案数据，无法保存到 Excel 文件。")
    except Exception as e:
        print(f"程序运行过程中发生错误: {e}")