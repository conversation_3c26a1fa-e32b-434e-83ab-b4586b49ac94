# -*- coding: utf-8 -*-
"""
@Project ：agentTools
@File    ：run_agent.py
@Date    ：2025/01/14 13:47
@Desc    ：关于公司股份回购相关流程引导Demo
"""
import os
import uuid
import asyncio
import traceback
import gradio as gr
from typing import Annotated

from utils.load_env import logger
from langgraph.constants import START
from langgraph.graph import StateGraph
from typing_extensions import TypedDict
from langgraph.prebuilt import tools_condition
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph.message import AnyMessage, add_messages
from langchain_core.runnables import Runnable, RunnableConfig
from sub_agents.graph_utils import create_tool_node_with_fallback
from sub_agents.business_opportunity_data_search.business_opportunity_tools import business_opportunity_tools
from sub_agents.business_opportunity_data_search.business_opportunity_agent import business_opportunity_assistant_runnable

logger.info("商机提醒AGENT")

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]


class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable

    def __call__(self, state: State, config: RunnableConfig):
        while True:
            configuration = config.get("configurable", {})
            passenger_id = configuration.get("passenger_id", None)
            state = {**state, "user_info": passenger_id}
            result = self.runnable.invoke(state)
            # If the LLM happens to return an empty response, we will re-prompt it for an actual response.
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                messages = state["messages"] + [("user", "Respond with a real output.")]
                state = {**state, "messages": messages}
            else:
                break
        return {"messages": result}

builder = StateGraph(State)
builder.add_node("business_opportunity", Assistant(business_opportunity_assistant_runnable))
builder.add_node("tools", create_tool_node_with_fallback(business_opportunity_tools))
builder.add_edge(START, "business_opportunity")
builder.add_edge("tools", "business_opportunity")
builder.add_conditional_edges("business_opportunity", tools_condition)
memory = MemorySaver()
business_opportunity_graph = builder.compile(checkpointer=memory)
# 生成图片
# if os.getenv('env', 'test') == 'test':
    # business_opportunity_graph.get_graph(xray=True).draw_mermaid_png(
    #     output_file_path='sub_agents/business_opportunity_data_search/business_opportunity_agent_graph.builder.png')

async def view_main(user_input):
    config = {
        "recursion_limit": 10,
        "configurable": {
            "thread_id": str(uuid.uuid4())
        }
    }

    try:
        answer = await business_opportunity_graph.ainvoke(
            {"messages": ("user", user_input)},
            config,
            stream_mode="values",
            debug=True
        )
        answer = answer["messages"][-1].content
        # saveDocx(answer)
        return answer
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{e}"

def chat(message, history):
    bot_message = asyncio.run(view_main(message))
    return bot_message

# 创建Gradio接口
iface = gr.ChatInterface(
    chat,
    type='messages',
    chatbot=gr.Chatbot(elem_id="chatbot", type='messages', render=False),
    textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
    title="关于公司股份回购相关流程引导Demo",
    description="这是一个使用Gradio创建的商机挖掘助手对话界面(目前仅用于生成'关于公司股份回购相关流程引导文档')",
    theme="soft",
    cache_examples=True,
    css="""
    #chatbot {
        height: calc(100vh - 280px) !important;
        overflow-y: auto;
    }
    """
)

if __name__ == '__main__':
    iface.launch(server_name="0.0.0.0",
                 inbrowser=True,
                 server_port=7862, debug=True)
