from langchain_core.tools import tool
from common_tools.common_agent_tools import bing_search
from utils.db_tool import get_case_company_by_condition, get_law_name_by_id, \
    get_law_text_name_by_law_text, get_rep_overview_data_by_company_code, \
    get_rep_process_data_by_case_id, get_trad_date, get_company_data, get_company_trade_data
from utils.load_env import logger

@tool
def get_company_message(company_code: str, date: str):
    """根据输入信息查询公司信息和行情数据。
    :param company_code: str  证券代码
    :param date: str  日期
    :return 查询结果字典，包含查询到的数据，结果以字符串形式返回。
        {
            'company_data': {}, # 公司基本信息
            'company_trade_data': [] # 公司行情数据
        }
    示例:
    company_code = "000001"
    date = "2024-01-01"
    result = get_company_message(company_code, date)
    """
    logger.info(f"[商机提醒助手-get_company_message] 输入信息：company_code({company_code}) date({date})")
    company_data = get_company_data(company_code=company_code,date=date)
    company_trade_data = get_company_trade_data(company_code=company_code,date=date)
    # 计算近30个交易日股票收盘价均价
    close_prices = [data['收盘价'] for data in company_trade_data]
    company_data['近30个交易日股票收盘价均价'] = round(sum(close_prices) / len(close_prices), 2)
    if not company_data:
        return "未查询到相关公司信息"
    result = {
        'company_data': company_data,
        'company_trade_data': company_trade_data
    }
    return str(result)

@tool
def get_case_company_message(same_plate_company: dict,same_industry_company: dict,same_orgform_company: dict,same_area_company: dict,same_scale_company: dict):
    """根据get_same_case_company工具的结果和用户输入信息查询同板块同行业同企业性质同地区同规模已完成回购的公司基本信息、回购前后行情数据和回购案例信息，在使用本方法前需要调用get_same_case_company。
    :param same_plate_company: dict  同板块公司信息
    :param same_industry_company: dict  同行业公司信息
    :param same_orgform_company: dict  同企业性质公司信息
    :param same_area_company: dict  同地区公司信息
    :param same_scale_company: dict  同规模公司信息
    :return 查询结果字典，包含查询到的数据，结果以字符串形式返回。
        {
            "same_plate_rep_case": {}, #同板块已完成回购的公司信息
            "same_industry_rep_case": {}, #同申万行业已完成回购的公司信息
            "same_orgform_rep_case": {}, #同企业性质已完成回购的公司信息
            "same_area_rep_case": {}, #同地区已完成回购的公司信息
            "same_scale_rep_case": {} #同规模已完成回购的公司信息
        }
    """
    logger.info(f"[商机提醒助手-get_case_company_message] 输入信息：same_plate_company({same_plate_company}) "
                f"same_industry_company({same_industry_company}) same_orgform_company({same_orgform_company}) "
                f"same_area_company({same_area_company}) same_scale_company({same_scale_company})")

    # 获取同板块案例数据
    # 获取回购起始日的前一个交易日
    same_plate_repurstartdate = get_trad_date(same_plate_company['repurstartdate'], 'before')
    # 获取回购截止日的后一个交易日
    same_plate_repurenddate = get_trad_date(same_plate_company['repurenddate'], 'after')
    # 获取同板块回购前公司数据
    same_plate_before_company_data = get_company_data(company_code=same_plate_company['company_code'], date=same_plate_repurstartdate)
    same_plate_before_trade_data = get_company_trade_data(company_code=same_plate_company['company_code'], date=same_plate_repurstartdate)
    # 获取同板块回购后公司数据
    same_plate_after_company_data = get_company_data(company_code=same_plate_company['company_code'], date=same_plate_repurenddate)
    same_plate_after_trade_data = get_company_trade_data(company_code=same_plate_company['company_code'], date=same_plate_repurenddate)
    # 获取同板块回购案例数据
    same_plate_rep_overview_data = get_rep_overview_data_by_company_code(company_code=same_plate_company['company_code'])
    same_plate_rep_process_data = get_rep_process_data_by_case_id(case_id=same_plate_rep_overview_data.get('case_id'))

    if same_plate_before_company_data and same_plate_before_trade_data and same_plate_after_company_data and same_plate_after_trade_data:
        same_plate_before_close_prices = [data['收盘价'] for data in same_plate_before_trade_data]
        same_plate_after_close_prices = [data['收盘价'] for data in same_plate_after_trade_data]
        same_plate_company_data = {
            '证券代码': same_plate_before_company_data['证券代码'],
            '公司名称': same_plate_before_company_data['公司名称'],
        }
        same_plate_before_trade_data = {
            '最近一期每股净资产': same_plate_before_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_plate_before_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(same_plate_before_close_prices) / len(same_plate_before_close_prices), 2),
        }
        same_plate_after_trade_data = {
            '最近一期每股净资产': same_plate_after_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_plate_after_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(same_plate_after_close_prices) / len(same_plate_after_close_prices), 2),
        }
        same_plate_rep_case = {
            '基本信息': same_plate_company_data,
            '回购前': same_plate_before_trade_data,
            '回购后': same_plate_after_trade_data,
            '回购概况': same_plate_rep_overview_data,
        }
    else:
        same_plate_rep_case = '暂无同板块已完成回购的公司信息'

    # 获取同行业案例数据
    # 获取回购起始日的前一个交易日
    same_industry_repurstartdate = get_trad_date(same_industry_company['repurstartdate'], 'before')
    # 获取回购截止日的后一个交易日
    same_industry_repurenddate = get_trad_date(same_industry_company['repurenddate'], 'after')
    # 获取同行业回购前公司数据
    same_industry_before_company_data = get_company_data(company_code=same_industry_company['company_code'], date=same_industry_repurstartdate)
    same_industry_before_trade_data = get_company_trade_data(company_code=same_industry_company['company_code'], date=same_industry_repurstartdate)
    # 获取同行业回购后公司数据
    same_industry_after_company_data = get_company_data(company_code=same_industry_company['company_code'], date=same_industry_repurenddate)
    same_industry_after_trade_data = get_company_trade_data(company_code=same_industry_company['company_code'], date=same_industry_repurenddate)
    # 获取同行业回购案例数据
    same_industry_rep_overview_data = get_rep_overview_data_by_company_code(company_code=same_industry_company['company_code'])
    same_industry_rep_process_data = get_rep_process_data_by_case_id(case_id=same_industry_rep_overview_data.get('case_id'))

    if same_industry_before_company_data and same_industry_before_trade_data and same_industry_after_company_data and same_industry_after_trade_data:
        before_close_prices = [data['收盘价'] for data in same_industry_before_trade_data]
        after_close_prices = [data['收盘价'] for data in same_industry_after_trade_data]
        same_industry_company_data = {
            '证券代码': same_industry_before_company_data['证券代码'],
            '公司名称': same_industry_before_company_data['公司名称'],
        }
        same_industry_before_trade_data = {
            '最近一期每股净资产': same_industry_before_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_industry_before_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(before_close_prices) / len(before_close_prices), 2),
        }
        same_industry_after_trade_data = {
            '最近一期每股净资产': same_industry_after_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_industry_after_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(after_close_prices) / len(after_close_prices), 2),
        }
        same_industry_rep_case = {
            '基本信息': same_industry_company_data,
            '回购前': same_industry_before_trade_data,
            '回购后': same_industry_after_trade_data,
            '回购概况': same_industry_rep_overview_data,
        }
    else:
        same_industry_rep_case = '暂无同行业已完成回购的公司信息'

    # 获取同企业性质案例数据
    # 获取回购起始日的前一个交易日
    same_orgform_repurstartdate = get_trad_date(same_orgform_company['repurstartdate'], 'before')
    # 获取回购截止日的后一个交易日
    same_orgform_repurenddate = get_trad_date(same_orgform_company['repurenddate'], 'after')
    # 获取同企业性质回购前公司数据
    same_orgform_before_company_data = get_company_data(company_code=same_orgform_company['company_code'], date=same_orgform_repurstartdate)
    same_orgform_before_trade_data = get_company_trade_data(company_code=same_orgform_company['company_code'], date=same_orgform_repurstartdate)
    # 获取同企业性质回购后公司数据
    same_orgform_after_company_data = get_company_data(company_code=same_orgform_company['company_code'], date=same_orgform_repurenddate)
    same_orgform_after_trade_data = get_company_trade_data(company_code=same_orgform_company['company_code'], date=same_orgform_repurenddate)
    # 获取同企业性质回购案例数据
    same_orgform_rep_overview_data = get_rep_overview_data_by_company_code(company_code=same_orgform_company['company_code'])
    same_orgform_rep_process_data = get_rep_process_data_by_case_id(case_id=same_orgform_rep_overview_data.get('case_id'))

    if same_orgform_before_company_data and same_orgform_before_trade_data and same_orgform_after_company_data and same_orgform_after_trade_data:
        before_close_prices = [data['收盘价'] for data in same_orgform_before_trade_data]
        after_close_prices = [data['收盘价'] for data in same_orgform_after_trade_data]
        same_orgform_company_data = {
            '证券代码': same_orgform_before_company_data['证券代码'],
            '公司名称': same_orgform_before_company_data['公司名称'],
        }
        same_orgform_before_trade_data = {
            '最近一期每股净资产': same_orgform_before_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_orgform_before_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(before_close_prices) / len(before_close_prices), 2),
        }
        same_orgform_after_trade_data = {
            '最近一期每股净资产': same_orgform_after_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_orgform_after_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(after_close_prices) / len(after_close_prices), 2),
        }
        same_orgform_rep_case = {
            '基本信息': same_orgform_company_data,
            '回购前': same_orgform_before_trade_data,
            '回购后': same_orgform_after_trade_data,
            '回购概况': same_orgform_rep_overview_data,
        }
    else:
        same_orgform_rep_case = '暂无同企业性质已完成回购的公司信息'

    # 获取同地区案例数据
    # 获取回购起始日的前一个交易日
    same_area_repurstartdate = get_trad_date(same_area_company['repurstartdate'], 'before')
    # 获取回购截止日的后一个交易日
    same_area_repurenddate = get_trad_date(same_area_company['repurenddate'], 'after')
    # 获取同地区回购前公司数据
    same_area_before_company_data = get_company_data(company_code=same_area_company['company_code'], date=same_area_repurstartdate)
    same_area_before_trade_data = get_company_trade_data(company_code=same_area_company['company_code'], date=same_area_repurstartdate)
    # 获取同地区回购后公司数据
    same_area_after_company_data = get_company_data(company_code=same_area_company['company_code'], date=same_area_repurenddate)
    same_area_after_trade_data = get_company_trade_data(company_code=same_area_company['company_code'], date=same_area_repurenddate)
    # 获取同地区回购案例数据
    same_area_rep_overview_data = get_rep_overview_data_by_company_code(company_code=same_area_company['company_code'])
    same_area_rep_process_data = get_rep_process_data_by_case_id(case_id=same_area_rep_overview_data.get('case_id'))

    if same_area_before_company_data and same_area_before_trade_data and same_area_after_company_data and same_area_after_trade_data:
        before_close_prices = [data['收盘价'] for data in same_area_before_trade_data]
        after_close_prices = [data['收盘价'] for data in same_area_after_trade_data]
        same_area_company_data = {
            '证券代码': same_area_before_company_data['证券代码'],
            '公司名称': same_area_before_company_data['公司名称'],
        }
        same_area_before_trade_data = {
            '最近一期每股净资产': same_area_before_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_area_before_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(before_close_prices) / len(before_close_prices), 2),
        }
        same_area_after_trade_data = {
            '最近一期每股净资产': same_area_after_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_area_after_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(after_close_prices) / len(after_close_prices), 2),
        }
        same_area_rep_case = {
            '基本信息': same_area_company_data,
            '回购前': same_area_before_trade_data,
            '回购后': same_area_after_trade_data,
            '回购概况': same_area_rep_overview_data,
        }
    else:
        same_area_rep_case = '暂无同地区已完成回购的公司信息'

    # 获取同规模案例数据
    # 获取回购起始日的前一个交易日
    same_scale_repurstartdate = get_trad_date(same_scale_company['repurstartdate'], 'before')
    # 获取回购截止日的后一个交易日
    same_scale_repurenddate = get_trad_date(same_scale_company['repurenddate'], 'after')
    # 获取同规模回购前公司数据
    same_scale_before_company_data = get_company_data(company_code=same_scale_company['company_code'], date=same_scale_repurstartdate)
    same_scale_before_trade_data = get_company_trade_data(company_code=same_scale_company['company_code'], date=same_scale_repurstartdate)
    # 获取同规模回购后公司数据
    same_scale_after_company_data = get_company_data(company_code=same_scale_company['company_code'], date=same_scale_repurenddate)
    same_scale_after_trade_data = get_company_trade_data(company_code=same_scale_company['company_code'], date=same_scale_repurenddate)
    # 获取同规模回购案例数据
    same_scale_rep_overview_data = get_rep_overview_data_by_company_code(company_code=same_scale_company['company_code'])
    same_scale_rep_process_data = get_rep_process_data_by_case_id(case_id=same_scale_rep_overview_data.get('case_id'))

    if same_scale_before_company_data and same_scale_before_trade_data and same_scale_after_company_data and same_scale_after_trade_data:
        before_close_prices = [data['收盘价'] for data in same_scale_before_trade_data]
        after_close_prices = [data['收盘价'] for data in same_scale_after_trade_data]
        same_scale_company_data = {
            '证券代码': same_scale_before_company_data['证券代码'],
            '公司名称': same_scale_before_company_data['公司名称'],
        }
        same_scale_before_trade_data = {
            '最近一期每股净资产': same_scale_before_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_scale_before_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(before_close_prices) / len(before_close_prices), 2),
        }
        same_scale_after_trade_data = {
            '最近一期每股净资产': same_scale_after_company_data['最近一期每股净资产'],
            '最近一年最高收盘价': same_scale_after_company_data['最近一年最高收盘价'],
            '近30个交易日股票收盘价均价': round(sum(after_close_prices) / len(after_close_prices), 2),
        }
        same_scale_rep_case = {
            '基本信息': same_scale_company_data,
            '回购前': same_scale_before_trade_data,
            '回购后': same_scale_after_trade_data,
            '回购概况': same_scale_rep_overview_data,
        }
    else:
        same_scale_rep_case = '暂无同规模已完成回购的公司信息'

    result = {
        "same_plate_rep_case": same_plate_rep_case,
        "same_industry_rep_case": same_industry_rep_case,
        "same_orgform_rep_case": same_orgform_rep_case,
        "same_area_rep_case": same_area_rep_case,
        "same_scale_rep_case": same_scale_rep_case
    }

    return str(result)

@tool
def get_same_case_companies(belongs_plate: str, industry_sw_2021_code3: str,
                                   industry_sw_2021_code2: str, industry_sw_2021_code1: str,
                                   province_code: str, city_code: str, area_code: str,
                                   orgform_code: str, market_value: float, date: str):
    """根据get_same_scale_company工具的结果和用户输入信息查询同板块同行业同企业性质同地区同规模已完成回购的公司数据，在使用本方法前需要调用get_company_message。
        :param belongs_plate: str  所属板块编码
        :param industry_sw_2021_code3: str  申万三级行业编码
        :param industry_sw_2021_code2: str  申万二级行业编码
        :param industry_sw_2021_code1: str  申万一级行业编码
        :param province_code: str  省级编码
        :param city_code: str  市级编码
        :param area_code: str  地区编码
        :param orgform_code: str  企业性质编码
        :param market_value: float  市值
        :param date: str  日期
        :return 查询结果字典，包含查询到的数据，结果以字符串形式返回。
            {
                "same_plate_companies": [], #同板块已完成回购的公司信息
                "same_industry_companies": [], #同申万行业已完成回购的公司信息
                "same_orgform_companies": [], #同企业性质已完成回购的公司信息
                "same_area_companies": [], #同地区已完成回购的公司信息
                "same_scale_companies": [] #同规模已完成回购的公司信息
            }
        """
    logger.info(f"[商机提醒助手-get_same_case_companies] 输入信息：belongs_plate({belongs_plate}) "
                f"industry_sw_2021_code3({industry_sw_2021_code3}) industry_sw_2021_code2({industry_sw_2021_code2}) "
                f"industry_sw_2021_code1({industry_sw_2021_code1}) province_code({province_code}) city_code({city_code}) "
                f"area_code({area_code}) orgform_code({orgform_code}) market_value({market_value}) date({date})")

    # 获取同板块案例数据
    same_plate_companies = get_case_company_by_condition(plate=belongs_plate,trade_date=date)

    # 获取同行业案例数据
    same_industry_companies = []
    def fetch_and_update_industry_results(**kwargs):
        nonlocal same_industry_companies
        companies = get_case_company_by_condition(**kwargs)
        if companies:
            remaining_slots = 10 - len(same_industry_companies)
            if remaining_slots > 0:
                same_industry_companies.extend(companies[:remaining_slots])
        return len(same_industry_companies) < 10

    if fetch_and_update_industry_results(industry3=industry_sw_2021_code3, trade_date=date):
        if fetch_and_update_industry_results(industry2=industry_sw_2021_code2, trade_date=date):
            fetch_and_update_industry_results(industry1=industry_sw_2021_code1, trade_date=date)

    # 获取同企业性质案例数据
    same_orgform_companies = get_case_company_by_condition(orgform_code=orgform_code,trade_date=date)

    # 获取同地区案例数据
    same_area_companies = []
    def fetch_and_update_area_results(**kwargs):
        nonlocal same_area_companies
        companies = get_case_company_by_condition(**kwargs)
        if companies:
            remaining_slots = 10 - len(same_area_companies)
            if remaining_slots > 0:
                same_area_companies.extend(companies[:remaining_slots])
        return len(same_area_companies) < 10

    if fetch_and_update_area_results(area_code=area_code, trade_date=date):
        if fetch_and_update_area_results(city_code=city_code, trade_date=date):
            fetch_and_update_area_results(province_code=province_code, trade_date=date)

    # 获取同规模案例数据
    same_scale_companies = get_case_company_by_condition(market_value=market_value,trade_date=date)

    result = {
        "same_plate_companies": same_plate_companies,
        "same_industry_companies": same_industry_companies,
        "same_orgform_companies": same_orgform_companies,
        "same_area_companies": same_area_companies,
        "same_scale_companies": same_scale_companies
    }
    return str(result)


@tool
def get_rep_case_message(company_code: str):
    """查询同板块同行业同规模已完成回购的公司的回购案例信息，在使用本方法前需要调用get_same_scale_company_message。
    :param company_code: str  同板块同行业同规模已完成回购的公司的证券代码
    :return 查询结果字典，包含查询到的数据，结果以字符串形式返回。
        {
            "rep_overview_data": 回购概况,
            "rep_process_data": 回购进程,
        }
    示例:
    company_code = "000001"
    result = get_rep_case_message(company_code)
    """
    logger.info(f"[商机提醒助手-get_rep_overview_data] 证券代码：{company_code}")
    rep_overview_data = get_rep_overview_data_by_company_code(company_code=company_code)
    rep_process_data = get_rep_process_data_by_case_id(case_id=rep_overview_data.get('case_id'))
    result = {
        "rep_overview_data": rep_overview_data,
        "rep_process_data": rep_process_data,
    }
    return str(result)

@tool
def get_law_message(law_id: str, law_text: str):
    """根据输入信息查询法规名称和法条信息。
    :param law_id: str  法规id
    :param law_text: str  法条id
    :return 查询结果字典，包含查询到的数据，结果以字符串形式返回。
        {
            "law_message": 法规标题,
            "law_text_message": 法条内容列表,
        }
    示例:
    law_id = "123456"
    law_text = "789012"
    result = get_law_message(law_id, law_text)
    """
    logger.info(f"[商机提醒助手-get_law_message] 法规id：{law_id} 法条id：{law_text}")
    if not law_id:
        return "无相关法律信息"
    law_message = get_law_name_by_id(law_id=law_id)
    law_text_message = get_law_text_name_by_law_text(law_text=law_text)
    law_info = {
        "law_message": law_message,
        "law_text_message": law_text_message
    }
    return str(law_info)

business_opportunity_tools = [get_company_message, get_same_case_companies, get_case_company_message,  bing_search]
