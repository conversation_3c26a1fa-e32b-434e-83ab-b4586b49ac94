import os

from colorama import Fore, Style

from utils.load_env import logger

if not os.getenv('AGENT_TOOLS_API_INITED'):
    logger.info(f"""Business agent initialization begins\n
    {Fore.WHITE}
  _______    ________    ______      ________   _______    ______   ___   __    _________  
/_______/\\  /_______/\\  /_____/\\    /_______/\\ /______/\\  /_____/\\ /__/\\ /__/\\ /________/\\ 
\\::: _  \\ \\ \\__.::._\\/  \\:::__\\/    \\::: _  \\ \\\\::::__\\/__\\::::_\\/_\\::\\_\\\\  \\ \\\\__.::.__\\/ 
 \\::(_)  \\/_   \\::\\ \\      /: /      \\::(_)  \\ \\\\:\\ /____/\\\\:\\/___/\\\\:. `-\\  \\ \\  \\::\\ \\   
  \\::  _  \\ \\  _\\::\\ \\__  /::/___     \\:: __  \\ \\\\:\\\\_  _\\/ \\::___\\/_\\:. _    \\ \\  \\::\\ \\  
   \\::(_)  \\ \\/__\\::\\__/\\/_:/____/\\    \\:.\\ \\  \\ \\\\:\\_\\ \\ \\  \\:\\____/\\\\. \\`-\\  \\ \\  \\::\\ \\ 
    \\_______\\/\\________\\/\\_______\\/     \\__\\/\\__\\/ \\_____\\/   \\_____\\/ \\__\\/ \\__\\/   \\__\\/           
    {Style.RESET_ALL}
    """)
else:
    logger.info(f"Business agent initialization begins")
