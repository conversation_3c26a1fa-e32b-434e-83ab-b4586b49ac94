import json
import time
import uuid
from datetime import datetime
import concurrent.futures

import pandas as pd
import requests

def chat_with_api(user_input, _user_id, url: str, request_num=0):
    api_url = f"http://127.0.0.1:8000/rep/{url}?user_input={user_input}&user_id={_user_id}"
    response = requests.post(api_url)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"!!! 非 200 状态码 !!! 状态码: {response.status_code}") # 打印状态码
        print(f"!!! 响应头 !!!: {response.headers}") # 打印响应头
        if request_num > 2:
            raise Exception(f"Error occurred while making HTTP request: {response.status_code}")
        else:
            request_num += 1
            return chat_with_api(user_input, _user_id, url, request_num=request_num)

def process_question(item, index):
    user_id = "TUR_" + str(uuid.uuid4())
    print(f"\n======问题 {index+1} ======\n{item}\n")
    try:
        answer = {'q': item}
        chat_result = chat_with_api(item, user_id, 'chat')
        print(f"\n======回答 {index+1} ======\n{str(chat_result)}\n")
        answer['a1'] = chat_result
        return answer
    except Exception as e:
        print(f"\n======问题报错 {index+1} ======\n{item}\ncaused by: {e}")
        return {'q': item, 'a1': 'error'}

if __name__ == '__main__':
    # 读取 Excel 文件
    df = pd.read_excel('questions_answers.xlsx')
    # 获取第1列数据
    question_list = df.iloc[:, 0]
    # 遍历并调用接口
    answer_list = []

    with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
        futures = [executor.submit(process_question, item, index) for index, item in enumerate(question_list)]
        for future in concurrent.futures.as_completed(futures):
            answer_list.append(future.result())

    if answer_list:
        df_result = pd.DataFrame(answer_list, columns=['q', 'a1'])
        df_result.rename(columns={'q': '问题', 'a1': '回答'}, inplace=True)
        filename = f"compliance_qa_r1_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
        df_result.to_excel(filename, index=False)
    print("完成！")
