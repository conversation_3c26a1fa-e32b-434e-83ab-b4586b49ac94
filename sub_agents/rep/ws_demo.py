import asyncio
import json

import websockets.client


async def chat_with_api_ws(user_input, _user_id, url_path_param: str, request_num=0):
    ws_uri = f"ws://localhost:8000/rep/ws/{url_path_param}"

    try:
        async with websockets.client.connect(ws_uri) as websocket:
            message = json.dumps({
                "user_input": user_input,
                "user_id": _user_id
            })

            print(f"[{_user_id}] 发送消息：{message}")
            await websocket.send(message)

            full_response = ""

            while True:
                try:
                    response = await websocket.recv()
                    print(f"[{_user_id}] 收到消息：{response}")

                    try:
                        response_json = json.loads(response)
                        if "summary" in response_json:
                            chunk_content = response_json.get("summary", "")
                            if chunk_content == "start":
                                print(f"[{_user_id}] AI Agent 开始思考...")
                            elif chunk_content == "stop":
                                print(f"[{_user_id}] AI Agent 思考结束。\n")
                            else:
                                print(chunk_content, end="")
                                full_response += chunk_content
                        elif "is_end" in response_json and response_json["is_end"] == "yes":
                            break

                    except json.JSONDecodeError:
                        print(f"[{_user_id}] 收到非JSON消息: {response}")

                except websockets.exceptions.ConnectionClosed as e:
                    print(f"[{_user_id}] WebSocket 连接关闭: {e.code} - {e.reason}")
                    break
                except Exception as e:
                    print(f"[{_user_id}] 接收消息时发生错误: {str(e)}")
                    break

            return full_response

    except Exception as e:
        print(f"[{_user_id}] WebSocket 连接失败: {str(e)}")
        return "error: 连接失败或通信错误"

async def process_question(user_id): # process_question 直接接收 user_id
    question = "回购股份注销是否算现金分红？如果算，应该怎么操作？" # 写死问题
    print(f"\n======用户 {user_id} 提问 ======\n{question}\n")
    try:
        answer = {'q': question}
        chat_result = await chat_with_api_ws(question, user_id, 'ws')
        print(f"\n======用户 {user_id} 回答 ======\n{str(chat_result)}\n")
        answer['a1'] = chat_result
        return answer
    except Exception as e:
        print(f"\n======用户 {user_id} 提问报错 ======\n{question}\ncaused by: {e}")
        return {'q': question, 'a1': 'error'}

async def main():
    num_concurrent_tasks = 2  # 设置并发任务数量
    user_ids = [f"user_9{i+1}" for i in range(num_concurrent_tasks)] # 动态生成 user_id 列表

    tasks = [process_question(user_id) for user_id in user_ids] # process_question 现在直接传 user_id
    await asyncio.gather(*tasks)

    print("\n===== 并发测试完成！=====")

if __name__ == '__main__':
    asyncio.run(main())