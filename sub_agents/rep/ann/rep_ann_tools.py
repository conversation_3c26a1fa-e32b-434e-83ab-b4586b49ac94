import time
import traceback
from typing import Optional

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from sub_agents.rep.ann.rep_ann_utils import query_ann_list
from sub_agents.rep.ann.rep_ann_ws import __send_ann_result
from utils.load_env import logger


@tool
async def get_ann_list(config: RunnableConfig,
                       keywords: list[str],
                       declare_type: list[str],
                       code_name: Optional[str] = '',
                       date_range: list[str] = None) -> str:
    """
    查询用户问题相关联的公告
    :param keywords 从用户问题中提取回购业务的关键词，应贴近法规中的正式表述，禁止提取与declare_type参数中取值范围相近的词汇；关键词本身字数最好为2个字，关键词总数量为5个以下；必填
    :param declare_type: 从用户问题中提取出对应的业务分类；取值范围：回购提议,回购方案,前十名公众股东名册,回购报告书,首次回购,回购实施进展,通知债权人,期间过半仍未实施回购,回购实施结果暨股份变动,延期实施回购,已回购股份注销,激励股份回购注销；必填，可多选，多个值使用','隔开'
    :param code_name: 公司代码或名称；非必填
    :param date_range: 查询公告的起止日期；必填；填写格式为["yyyy-MM-dd", "yyyy-MM-dd"]；若问题未提及日期则默认传 ["2024-01-01", ""]
    :return: 查询出的公告
    """
    start_time = time.time()
    logger.info(
        f"开始执行get_ann_list函数，参数：keywords={keywords}, declare_type={declare_type}, code_name={code_name}")
    try:
        result = await query_ann_list(code_name, declare_type, keywords, date_range, config)
        if not result:
            logger.info(f"get_ann_list执行完成，未找到相关公告，耗时：{time.time() - start_time:.2f}秒")
            return '未找到相关公告'

        # 给客户端发送消息
        await __send_ann_result(result, keywords, config)
        # 返回结果
        logger.info(f"get_ann_list执行完成，成功返回结果，耗时：{time.time() - start_time:.2f}秒")
        return str({"公告检索结果": result})

    except Exception as e:
        logger.error(f"get_ann_list执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


rep_ann_tool_list = [get_ann_list]
