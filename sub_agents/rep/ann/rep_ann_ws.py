import json

from langchain_core.runnables import RunnableConfig

from sub_agents.rep.enum.ws_msg_enum import ToolsType
from sub_agents.rep.utils.web_utils import send_websocket_msg


async def __send_ann_result(result_ann, keywords, config: RunnableConfig):
    """
    把结果用ws消息发送出去
    """
    a_send = []
    if result_ann:
        for item in result_ann:
            if item.get("公告ID"):
                a_send.append({
                    'noticeId': item.get("公告ID"),
                    'content': item.get("公告概要"),
                    'keywords': ' '.join(keywords)
                })

    if a_send:
        final_send = {
            ToolsType.ANN.value: a_send,
        }

        await send_websocket_msg(json.dumps(final_send, ensure_ascii=False), config)
