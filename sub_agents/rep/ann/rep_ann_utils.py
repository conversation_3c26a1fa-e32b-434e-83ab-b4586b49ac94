import asyncio
import time
from typing import Optional, List

import aiohttp
from bs4 import BeautifulSoup
from langchain_core.runnables import RunnableConfig
from pydantic.v1 import Field, BaseModel

from sub_agents.rep.utils.web_utils import get_access_token
from utils.redis_tool import remove_redis_info
from utils.rerank_tools import ReRankService, rerank_service_host

TOKEN_REDIS_KEY_NAME = 'REP_AGENT_ACCESS_TOKEN'


class RepAnnCondition(BaseModel):
    companySearchCode: Optional[str] = Field(alias='公司代码或名称', default='')
    boardSelectShow: Optional[str] = Field(alias='所属板块', default='',
                                           description='069001001001=上交所主板, 069001001006=上交所科创版, 069001002001,069001002003=深交所主板, 069001002002=深交所创业板, *********=北交所')
    keyTitle: Optional[str] = Field(alias='标题包含以下任一关键词（以空格区分）', default='')
    keyAndTitle: Optional[str] = Field(alias='标题包含以下全部关键词（以空格区分）', default='')
    keyNotTitle: Optional[str] = Field(alias='标题不包含以下全部关键词', default='')
    keyContent: Optional[str] = Field(alias='全文包含以下任一关键词（以空格区分）', default='')
    keyAndContent: Optional[str] = Field(alias='全文包含以下全部关键词（以空格区分）', default='')
    keyNotContent: Optional[str] = Field(alias='全文不包含以下全部关键词（以空格区分）', default='')
    declareNewType: Optional[str] = Field(alias='业务分类', default='')
    showFlag: Optional[str] = Field(default='1')
    isAllEqual: Optional[str] = Field(default='0')
    checkCompanyBoolean: Optional[bool] = Field(default=False)
    searchFromPlate: Optional[str] = Field(default='1')
    nearSearch: Optional[str] = Field(default='0')
    searchFlg: Optional[str] = Field(default='new', )
    aggWithCompany: Optional[str] = Field(default='1', description='同公司聚合展示 1=聚合，0=不聚合')
    dateRange: Optional[list] = Field(default=[], description='公告起止日期')


class RepAnnQuery(BaseModel):
    condition: Optional[RepAnnCondition] = Field(alias='查询条件', default={})
    currentPage: Optional[int] = Field(default=1)
    start: Optional[int] = Field(default=1)
    startRow: Optional[int] = Field(default=0)
    pageSize: Optional[int] = Field(default=10)
    argumentsLength: Optional[int] = Field(default=2)
    orderByName: Optional[str] = Field(default='6')
    orderByOrder: Optional[str] = Field(default='desc')


class RepAnnouncementInfo(BaseModel):
    公告ID: Optional[str] = Field(alias='id')
    公司代码: Optional[str] = Field(alias='code')
    公司名称: Optional[str] = Field(alias='companyShortName')
    公告标题: Optional[str] = Field(alias='originTitle')
    公告类别: Optional[str] = Field(alias='declareSourceName')
    公告概要: Optional[str] = Field(alias='content')
    发布日期: Optional[str] = Field(alias='publishDate')


ANN_SECTOR_RANGE_MAP = {
    "深交所主板": "069001002001,069001002003",
    "深交所创业板": "069001002002",
    "上交所主板": "069001001001",
    "上交所科创板": "069001001006",
    "北交所": "*********"
}

DECLARE_TYPE_MAP = {
    '回购提议': '746761292324080893',
    '回购方案': '24181902543262314',
    '前十名公众股东名册': '24181902543262316',
    '回购报告书': '24181902543262320',
    '首次回购': '24181902543262328',
    '回购实施进展': '24181902543262322',
    '通知债权人': '24181902543262324',
    '期间过半仍未实施回购': '24181902543262326',
    '回购实施结果暨股份变动': '746761292300315928',
    '延期实施回购': '458629827277283850',
    '已回购股份注销': '746761292300315929',
    '激励股份回购注销': '24181902543262431',
}


# 保持原有的 RepAnnCondition, RepAnnQuery, RepAnnouncementInfo 类和常量定义不变...

async def http_call_list(data):
    """异步HTTP调用获取公告列表"""
    access_token = get_access_token(TOKEN_REDIS_KEY_NAME)
    headers = {
        "authorization": access_token,
        "Content-Type": "application/json;charset=UTF-8"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    "https://services.easy-board.com.cn/announcement/announcementInit/noticeIndex",
                    headers=headers,
                    json=data
            ) as response:
                if response.status == 200:
                    res_result = await response.json()
                    return res_result.get('result', {}).get('queryList')
                elif response.status == 401:
                    # token过期，清除缓存重新获取
                    remove_redis_info(TOKEN_REDIS_KEY_NAME)
                    time.sleep(1)
                    return await http_call_list(data)
                else:
                    raise Exception(f"Error occurred while making HTTP request: {response.status}")
    except Exception as e:
        raise Exception(f"HTTP request failed: {str(e)}")


async def query_ann_list(code_name: Optional[str] = '',
                         declare_type: List[str] = None,
                         keywords: List[str] = None,
                         date_range: List[str] = None,
                         config: RunnableConfig = None):
    """
    异步查询用户问题相关联的公告
    """
    if not date_range:
        date_range = ["2024-01-01", ""]
    if keywords is None:
        keywords = []

    result = []
    declare_type_list = []
    if declare_type:
        declare_type_list = [DECLARE_TYPE_MAP.get(x) for x in declare_type if x and DECLARE_TYPE_MAP.get(x)]

    if keywords:
        keyword_text = " ".join(keywords)
        # 根据关键词和业务类型的数量，选择交叉调用还是共同调用
        if len(keywords) * len(declare_type_list) > 10:
            condition_instance = RepAnnCondition()
            condition_instance.companySearchCode = code_name
            condition_instance.keyContent = keyword_text
            condition_instance.dateRange = date_range
            condition_instance.declareNewType = ','.join(declare_type_list)
            query_instance = RepAnnQuery()
            query_instance.pageSize = 20
            query_instance.condition = condition_instance
            c_res = await http_call_list(query_instance.dict())
        else:
            c_res = []
            tasks = []
            for kw in keywords:
                for dt in declare_type_list:
                    condition_instance = RepAnnCondition()
                    condition_instance.companySearchCode = code_name
                    condition_instance.keyContent = kw
                    condition_instance.declareNewType = dt
                    condition_instance.dateRange = date_range
                    query_instance = RepAnnQuery()
                    query_instance.pageSize = 5
                    query_instance.condition = condition_instance
                    tasks.append(http_call_list(query_instance.dict()))

            # 并发执行所有请求
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, list):
                    c_res.extend(result)

        result = await __process_data(c_res, keyword_text)
    return result


async def __process_data(data: list, keyword_text: str) -> list:
    """异步处理公告数据"""
    if not data:
        return []

    ann_list = [RepAnnouncementInfo(**line).dict() for line in data]
    # 处理'公告概要'的HTML标签
    for ann in ann_list:
        if ann['公告概要']:
            text = BeautifulSoup(ann['公告概要'], 'lxml').get_text(strip=True)
            ann['公告概要'] = text

    filter_ann_list = []
    # 对请求结果进行评分，并选出几个最相关的
    documents = [item.get('公告概要') for item in ann_list]

    # 重排序
    reranker = ReRankService(rerank_service_host)
    ranked_documents = await reranker.rerank(keyword_text, documents)

    # 取出排名前 5 的 index
    if ranked_documents:
        ranked_documents = ranked_documents[:5]
        filter_ann_list = [ann_list[item.get('index')] for item in ranked_documents]
    return filter_ann_list
