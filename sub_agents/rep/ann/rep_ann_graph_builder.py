import json

from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.ann.rep_ann_agent import rep_ann_agent_runnable
from sub_agents.rep.ann.rep_ann_tools import rep_ann_tool_list
from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.general.rep_biz_state_def import RepAnnAgentState
from sub_agents.rep.utils.web_utils import send_websocket_msg


class RepAnnAgent:
    def __init__(self):
        self.runnable = rep_ann_agent_runnable

    async def __call__(self, _state: RepAnnAgentState, config: RunnableConfig):
        await send_websocket_msg(json.dumps({RepWSParam.FLOW_PROCESS.value: "公告助手正在检索"},
                                            ensure_ascii=False), config)
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_ann_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_ann_agent_messages": result}


tool_node = ToolNode(rep_ann_tool_list, messages_key='rep_ann_agent_messages')

builder = StateGraph(RepAnnAgentState)
builder.add_node("rep_ann_agent", RepAnnAgent())
builder.add_node("rep_ann_tools", tool_node)

builder.add_edge(START, "rep_ann_agent")
builder.add_edge("rep_ann_agent", "rep_ann_tools")
builder.add_edge("rep_ann_tools", END)

rep_ann_graph = builder.compile()

# if os.getenv('env', 'test') == 'test':
#     rep_ann_graph.get_graph(xray=True).draw_mermaid_png(output_file_path='sub_agents/rep/ann/rep_ann_graph.builder.png')
