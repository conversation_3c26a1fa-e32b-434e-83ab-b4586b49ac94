import os
from typing import Literal

from langchain_deepseek import ChatDeepSeek
from langchain_openai import ChatOpenAI

LLM_HUB = os.getenv('MODEL_HUB_HOST')

API_KEY = "sk-7XYtE83ltdpER8T16f963859333c47728e86F0C8F9105a74"


def get_a_llm(llm_type: Literal[
    "4o", "deepseek-chat", "deepseek-reasoner", "hs-deepseek-v3-0324", "hs-deepseek-r1"] = "hs-deepseek-v3-0324",
              temperature: float = 0,
              parallel_tool_calls: Literal[True, False] = True):
    """
    @param llm_type: llm类型
    @param temperature: 温度默认0，严谨，每次输出一致
    @param parallel_tool_calls: function_call场景下，是否支持并行工具调用，默认False
    """
    if llm_type in ["deepseek-reasoner", "hs-deepseek-r1"]:
        return ChatDeepSeek(model='hs-deepseek-r1', api_base=LLM_HUB, api_key=API_KEY, streaming=True)
    if parallel_tool_calls:
        # 不传即为默认并行
        return ChatOpenAI(model=llm_type, temperature=temperature,
                          max_tokens=None, timeout=300, api_key=API_KEY, base_url=LLM_HUB)
    else:
        return ChatOpenAI(model=llm_type, temperature=temperature,
                          model_kwargs={"parallel_tool_calls": parallel_tool_calls},
                          max_tokens=None, timeout=300, api_key=API_KEY, base_url=LLM_HUB)
