from langchain_core.messages import RemoveMessage


def add_remove_msgs(messages: list, keep_num: int):
    """
    想删除队列的消息，必须添加remove消息，在返回后才能被真正删除
    :param messages: 消息队列
    :param keep_num: 保留的对话次数；用户问题消息 + 之后跟这次问题相关的消息 = 一次对话
    """
    remove_msgs = []
    # 获取所有 type == 'human' 的下标
    human_indices = [i for i, message in enumerate(messages) if message.type == "human"]
    keep_human_min_index = 0
    if len(human_indices) >= keep_num:
        # 获取需要保留的 'human' 消息的最小下标
        keep_human_min_index = human_indices[0 - keep_num]

    for idx, msg in enumerate(messages):
        if idx < keep_human_min_index:
            # 比最小下标小的消息直接添加到删除列表中
            remove_msgs.append(RemoveMessage(id=msg.id))
        else:
            # 历史消息记录只保留human消息及相关的最后一条消息，中间消息全部删除
            if msg.type == 'human':
                if remove_msgs:
                    remove_msgs.pop()
            else:
                remove_msgs.append(RemoveMessage(id=msg.id))
    messages.extend(remove_msgs)
    return messages
