import requests

from utils.load_env import logger
from utils.redis_tool import get_redis_connect

CLIENT_ID = '610abd52fac84f60'
CLIENT_SECRET = '4ea53ec5610abd52fac84f60a02bcf4c'

CLIENT_ID_TEST = '41754692d898a8e4'
CLIENT_SECRET_TEST = '8f933b7341754692d898a8e43cf9c86a'


def get_access_token(r_key, env: str = 'prod'):
    """获取网关访问令牌"""
    r = get_redis_connect()

    if r.exists(r_key):
        access_token = r.get(r_key).decode('utf-8')
    else:
        client_id = CLIENT_ID_TEST
        client_secret = CLIENT_SECRET_TEST
        req_url = 'https://services-dev.valueonline.cn'
        # 环境判断
        if env == 'prod':
            client_id = CLIENT_ID
            client_secret = CLIENT_SECRET
            req_url = 'https://services.valueonline.cn'
        # 发送请求
        resp = requests.get(f'{req_url}/oauth/token'
                            f'?grant_type=client_credentials'
                            f'&response_type=token'
                            f'&client_id={client_id}'
                            f'&client_secret={client_secret}')
        access_token = resp.json().get('access_token')
        logger.info(f'get_code_map r_key: {r_key} {access_token}')
        r.set(r_key, access_token, ex=60 * 30)
    r.close()
    return access_token


async def send_websocket_msg(content: str, config: dict):
    """
    给客户端发送消息。
    :param content: 文本内容
    :param config: 配置信息，包含 manager 和 websocket。
    """
    if config:
        manager = config.get('configurable', {}).get('manager')
        websocket = config.get('configurable', {}).get('websocket')
        if manager and websocket:
            try:
                await manager.send_personal_message(content, websocket)
            except Exception as e:
                logger.error(f"REP send_websocket_msg ERROR: {e}")
