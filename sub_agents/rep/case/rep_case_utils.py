import asyncio
import datetime
from typing import Optional, List

import aiohttp
from pydantic import BaseModel, Field

from sub_agents.rep.utils.web_utils import get_access_token
from utils.redis_tool import remove_redis_info

TOKEN_REDIS_KEY_NAME = 'REP_AGENT_ACCESS_TOKEN'


def build_con_dto_list(params: dict) -> list:
    """
    获取接口请求体
    """
    # 处理最新公告日
    if not params.get('noticedateArr') or len(params.get('noticedateArr')) < 2:
        noticedateArr = []
        today = datetime.date.today()
        year_ago = today.replace(year=today.year - 1)
        noticedateArr.append(year_ago.strftime('%Y-%m-%d'))
        noticedateArr.append(today.strftime('%Y-%m-%d'))
        params['noticedateArr'] = noticedateArr

    result = [
        {
            "dateType": "text",
            "esName": "id",
            "label": "id",
            "labelName": "id",
            "labelValue": "",
            "textList": []
        },
        {
            "dateType": "text",
            "esName": "rep_company_code",
            "label": "证券简称/代码",
            "labelName": "companycodename",
            "labelValue": params.get('companycodename', ''),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_repurprogress_text",
            "label": "当前回购进程",
            "labelName": "treeRepurprogress",
            "labelValue": ','.join(params.get('treeRepurprogress', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_belongs_plate_cn",
            "label": "所属板块",
            "labelName": "treeBelPlate",
            "labelValue": ','.join(params.get('treeBelPlate', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_custom_share_type_cn",
            "label": "回购股份种类",
            "labelName": "treeRepShareType",
            "labelValue": ','.join(params.get('treeRepShareType', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_objective_purpose_cn",
            "label": "回购目的",
            "labelName": "objectivePurpose",
            "labelValue": ','.join(params.get('objectivePurpose', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_repuramount_source_cn",
            "label": "回购股份资金来源",
            "labelName": "treeRepuramountSource",
            "labelValue": ','.join(params.get('treeRepuramountSource', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "dropDown",
            "esName": "rep_reptype_cn",
            "label": "回购类型",
            "labelName": "repType",
            "labelValue": ','.join(params.get('repType', [])),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
        {
            "dateType": "date",
            "esName": "rep_noticedate",
            "label": "首次公告日",
            "labelName": "noticedateArr",
            "labelValue": "",
            "textList": [
                {
                    "logic": "or",
                    "timeStart": params.get('noticedateArr')[0],
                    "timeEnd": params.get('noticedateArr')[1],
                }
            ]
        },
        {
            "dateType": "text",
            "esName": "rep_reptitle",
            "label": "案例标题",
            "labelName": "reptitle",
            "labelValue": "",
            "textList": []
        },
        {
            "dateType": "time",
            "esName": "rep_repuramountlower",
            "label": "回购金额下限",
            "labelName": "repuramountlower",
            "labelValue": "",
            "textList": [
                {
                    "logic": "or",
                    "timeStart": params.get('repuramountlowerArr')[0] if params.get('repuramountlowerArr') and len(
                        params.get('repuramountlowerArr')) > 0 else '',
                    "timeEnd": params.get('repuramountlowerArr')[1] if params.get('repuramountlowerArr') and len(
                        params.get('repuramountlowerArr')) > 1 else '',
                }
            ]
        },
        {
            "dateType": "time",
            "esName": "rep_repuramountlimit",
            "label": "回购金额上限",
            "labelName": "repuramountlimit",
            "labelValue": "",
            "textList": [
                {
                    "logic": "or",
                    "timeStart": params.get('repuramountlimitArr')[0] if params.get('repuramountlimitArr') and len(
                        params.get('repuramountlimitArr')) > 0 else '',
                    "timeEnd": params.get('repuramountlimitArr')[1] if params.get('repuramountlimitArr') and len(
                        params.get('repuramountlimitArr')) > 1 else '',
                }
            ]
        },
        {
            "dateType": "time",
            "esName": "rep_thirty_avg_compare",
            "label": "回购价格上限占董事会决议前30个交易日均价比例",
            "labelName": "thirtyAvgCompare",
            "labelValue": "",
            "textList": []
        },
        {
            "dateType": "time",
            "esName": "rep_stock_increase",
            "label": "距首次披露日股价涨跌幅",
            "labelName": "stockIncrease",
            "labelValue": "",
            "textList": []
        },
        {
            "dateType": "dropDown",
            "esName": "rep_rep_label_cn",
            "label": "热点关注",
            "labelName": "replabel",
            "labelValue": params.get('replabel', ''),
            "textList": [
                {
                    "logic": "or",
                    "timeEnd": "",
                    "timeStart": ""
                }
            ]
        },
    ]

    return result


class RepurchaseQuery(BaseModel):
    companycodename: Optional[str] = Field(alias='公司名称或代码')
    repWayType: Optional[str] = Field(alias='回购方式',
                                      description='集中竞价,要约；单选')
    repType: Optional[List[str]] = Field(alias='回购类型',
                                         description='"股权激励注销回购,普通回购,业绩承诺注销回购；可选多值')
    treeRepurprogress: Optional[List[str]] = Field(alias='回购进程',
                                                   description='回购提议,董事会预案,股东大会通过,股东大会否决,实施中,终止实施,完成实施；可选多值')
    treeBelPlate: Optional[List[str]] = Field(alias='所属板块',
                                              description='上交所主板,深交所主板,深交所中小板,深交所创业板,上交所科创板；可选多值')
    treeRepShareType: Optional[List[str]] = Field(alias='回购股份种类',
                                                  description='有条件限售股,无限售条件的A股流通股,B股,H股；可选多值')
    objectivePurpose: Optional[List[str]] = Field(alias='回购目的',
                                                  description='维护公司价值及股东权益,为维护公司价值及股东权益-减少注册资本,为维护公司价值及股东权益-出售,员工持股计划,股权激励,调整股权结构,转换上市公司发行的可转换为股票的公司债券,'
                                                              '跌破最近一期每股净资产,连续20个交易日内股票收盘价跌幅累计达到20%,低于最近一年股票最高收盘价格的50%；可选多值')  # 热点关注选项
    treeRepuramountSource: Optional[List[str]] = Field(alias='回购股份资金来源',
                                                       description='自有资金,自筹资金,发行优先股,发行债券,回购专项贷款,发行普通股取得的超募资金,发行普通股取得的募投项目节余资金,发行普通股取得的永久补充流动资金,其他；可选多值')
    noticedateArr: Optional[List[str]] = Field(alias='首次公告日',
                                               description="时间区间，数组长度为2或0,格式:['2024-01-01','2024-01-31']")
    repuramountlowerArr: Optional[List[str]] = Field(alias='回购资金下限（万元）',
                                                     description="数字区间，数组长度为2或0；格式:['0', '5000']，代表查询回购资金下限为0至5000万元的案例")
    repuramountlimitArr: Optional[List[str]] = Field(alias='回购资金上限（万元）',
                                                     description="数字区间，数组长度为2或0；格式:['5000', '10000']，代表查询回购资金上限为5000万元至10000万元的案例")


class RepurchaseCaseForm(BaseModel):
    案例ID: Optional[str] = Field(alias='id')
    公司代码及名称: Optional[str] = Field(alias='companycodename')
    案例标题: Optional[str] = Field(alias='reptitle')
    当前回购进程: Optional[str] = Field(alias='treeRepurprogress')
    回购目的: Optional[str] = Field(alias='objectivePurpose')
    拟回购资金总额上限_万元: Optional[str] = Field(alias='repuramountlimit')
    拟回购资金总额下限_万元: Optional[str] = Field(alias='repuramountlower')
    回购价格上限占董事会决议前30个交易日均价比例: Optional[str] = Field(alias='thirtyAvgCompare')
    距首次披露日股价涨跌幅: Optional[str] = Field(alias='stockIncrease')
    首次公告日: Optional[str] = Field(alias='noticedateArr')
    案例链接: Optional[str] = Field(alias='case_detail_url')
    最近市场表现: Optional[str] = Field(alias='replabel')


async def http_call_list(data=None) -> dict:
    # 从 回购目的 提取 热点关注
    if data.get('objectivePurpose'):
        replabelArr = []
        objectivePurposeArr = []
        for item in data.get('objectivePurpose', []):
            if item == '跌破最近一期每股净资产' or item == '连续20个交易日内股票收盘价跌幅累计达到20%' or item == '低于最近一年股票最高收盘价格的50%':
                replabelArr.append(item)
            else:
                objectivePurposeArr.append(item)
        data['replabel'] = ','.join(replabelArr)
        data['objectivePurpose'] = objectivePurposeArr

    req_body = {
        "conditionDtoList": build_con_dto_list(data),
        "page": 0,
        "size": 10,
        "sortBy": "rep_noticedate",
        "sortOrder": "DESC"
    }
    api_domain = 'https://services.easy-board.com.cn'
    api_url = (f"{api_domain}/reorganization/RepList/selectRepCaseList"
               f"?page={req_body['page']}"
               f"&size={req_body['size']}")
    access_token = get_access_token(TOKEN_REDIS_KEY_NAME)  # 假设 get_access_token 是同步的，如果它是异步的，需要 await
    headers = {
        "authorization": access_token,
        "Content-Type": "application/json;charset=UTF-8"
    }
    async with aiohttp.ClientSession() as session:
        async with session.post(api_url, headers=headers, json=req_body) as response:
            if response.status == 200:
                result = await response.json()  # await response.json()
                result = result.get('result')
                case_list = result.get('data')
                if case_list:
                    # 拼接案例详情的链接
                    # for case in case_list:
                    #     case['case_detail_url'] = (f"{api_domain}/ui/reorganization/repCaseDetails"
                    #                                f"?caseId={str(case.get('id')).replace('rep_', '')}"
                    #                                f"&access_token={access_token}")
                    return {
                        'total': result.get('total'),
                        'case_list': [RepurchaseCaseForm(**line).dict() for line in case_list]
                    }

                return {'total': 0, 'case_list': []}

            elif response.status == 401:
                # access_token = get_access_token()将此键放到了redis
                remove_redis_info(TOKEN_REDIS_KEY_NAME)  # 假设 remove_redis_info 是同步的，如果它是异步的，需要 await
                await asyncio.sleep(1)  # 使用 asyncio.sleep
                return await http_call_list(data)  # 使用 await 递归调用
            else:
                raise Exception(
                    f"Error occurred while making HTTP request: {response.status}: {await response.text()}")  #
