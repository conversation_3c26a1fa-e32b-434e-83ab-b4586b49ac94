import json

from langchain_core.runnables import RunnableConfig

from sub_agents.rep.enum.ws_msg_enum import ToolsType
from sub_agents.rep.utils.web_utils import send_websocket_msg


async def __send_vio_case_result(result_vio, config: RunnableConfig):
    """
    把结果用ws消息发送出去
    """
    v_send = []
    try:
        vio_obj = json.loads(result_vio)
        vio_case = vio_obj.get('部分违规案例数据') or vio_obj.get('违规案例数据')
        v_send = [item.get("v_id") for item in vio_case if item.get("v_id")]
    except Exception as e:
        print(e)

    if v_send:
        final_send = {
            ToolsType.VIO_CASE.value: v_send,
        }

        await send_websocket_msg(json.dumps(final_send, ensure_ascii=False), config)


async def __send_rep_case_result(result_rep, config: RunnableConfig):
    """
    把结果用ws消息发送出去
    """
    r_send = []
    if result_rep:
        for item in result_rep:
            if item.get("案例ID"):
                r_send.append(item.get("案例ID"))

    if r_send:
        final_send = {
            ToolsType.REP_CASE.value: r_send,
        }

        await send_websocket_msg(json.dumps(final_send, ensure_ascii=False), config)
