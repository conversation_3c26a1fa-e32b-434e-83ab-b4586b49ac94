import json
from typing import Any
from typing import Dict, List, Optional

import requests

from utils.load_env import logger
from utils.redis_tool import remove_redis_info, get_redis_connect


def get_access_token():
    """获取数据浏览器访问令牌"""
    r_key = 'CASE_SEARCH_ACCESS_TOKEN'
    r = get_redis_connect()

    if r.exists(r_key):
        access_token = r.get(r_key).decode('utf-8')
    else:
        resp = requests.get(
            'https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=610abd52fac84f60&client_secret=4ea53ec5610abd52fac84f60a02bcf4c')
        access_token = resp.json().get('access_token')
        logger.info(f'get_code_map r_key: {r_key} {access_token}')
        r.set(r_key, access_token, ex=60 * 30)
    r.close()
    return access_token


def build_mapper():
    mapper = {}
    # 加载JSON文件数据
    with open('sub_agents/rep/case/data/merged_data.json', 'r', encoding='utf-8') as file:
        local_dict = json.load(file)

    # 定义一个递归函数，用于深度遍历嵌套结构
    def traverse_children(children):
        for child in children:
            if isinstance(child, dict):  # 确保 child 是字典类型
                # 检查并提取 mapper 和 param_name
                if 'mapper' in child and 'param_name' in child:
                    key = child['mapper']
                    mapper[key] = child['param_name']
                # 如果还有子层级，递归调用自身
                if 'children_id' in child:
                    traverse_children(child['children_id'])

    # 开始处理传入的 local_dict
    for item in local_dict:
        if 'children_id' in item:
            traverse_children(item['children_id'])
    # 添加案例名称
    mapper['principal.case_title'] = '案例名称'
    return mapper


# 预编译字典路，避免重复的 split 操作
compiled_mappers = build_mapper()


def load_target_data(file_path: str) -> Dict[str, Any]:
    """
    从 JSON 文件中加载所有的指标和相关数据。
    :param file_path: target.json 文件的路径
    :return: 包含指标的字典
    """
    with open(file_path, 'r', encoding='utf-8') as file:
        return json.load(file)


def build_mapper_screens(filters: Dict[str, Any]) -> Dict[str, Any]:
    """
    根据 filters 构建 mapper_screens 条件。
    :param filters: 查询参数中的筛选条件部分
    :return: 构建好的 mapper_screens 结构
    """
    conditions = []
    formula_map = {"gt": "GT", "gte": "GTE", "lt": "LT", "lte": "LTE", "equal": "EQ"}

    for field, condition in filters.items():
        if isinstance(condition, dict):
            logic = condition.get('op', 'AND').upper()
            for formula, val in condition.items():
                if formula in formula_map:
                    conditions.append({"logic": logic, "formula": formula_map[formula], "val": val})
        else:
            # 文本包含字符串字段的条件
            conditions.append({"logic": "AND", "formula": "CONTAINS", "val": condition})

    return {"mode": "CUSTOM", "condition": conditions}


def create_quota_entry(target_info: Dict[str, Any], mapper_screens: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    根据目标信息和 mapper_screens 创建配额条目。
    :param target_info: 目标指标的信息
    :param mapper_screens: 指标的 mapper_screens（如果有）
    :return: 构建好的配额条目
    """
    # 处理 mapper_fields，检查是否为字符串并尝试解析为列表
    mapper_fields = target_info.get("mapper_fields", "[]")
    if isinstance(mapper_fields, str):
        try:
            # 如果字符串有效，则解析为列表
            mapper_fields = json.loads(mapper_fields)
        except json.JSONDecodeError:
            # 如果解析失败，保持为原始字符串或根据需求处理
            mapper_fields = []

    # 初始化拼接后的部分
    appended_values = []

    # 遍历 mapper_fields 并提取 value
    for field in mapper_fields:
        if "value" in field:
            appended_values.append(str(field["value"]))

    # 如果存在 value，将它们用 '_' 拼接并添加到 mapper 字段的末尾
    if appended_values:
        target_info["mapper"] += "." + "_".join(appended_values)

    # 构建配额条目
    quota_entry = {
        "label": target_info["param_name"],
        "mapper": target_info["mapper"],  # 使用修改后的 mapper
        "mapper_fields": mapper_fields,  # 使用转换后的 mapper_fields
        "date_type": target_info["data_type"]
    }

    if mapper_screens:
        quota_entry["mapper_screens"] = mapper_screens

    return quota_entry


def get_default_sorts(case_type: str) -> List[Dict[str, str]]:
    """根据类型返回默认的排序配置"""
    sort_configs = {
        'inquiry_letter': [
            {"mapper": "principal.serial", "direction": "DESC"},
            {"mapper": "principal.question_serial", "direction": "ASC"}
        ],
        'target_asset': [{"mapper": "principal.serial", "direction": "DESC"}],
        'violation': [{"mapper": "principal.sort", "direction": "DESC"}]
    }
    return sort_configs.get(case_type, [{"mapper": "mapper.case_id", "direction": "DESC"}])


def build_request_body(query_params: Dict[str, Any], target_file_path: str, case_type: Optional[str] = '') -> \
        Optional[Dict[str, Any]]:
    """
    构建完整的请求体，支持多指标和复杂筛选条件。
    :param query_params: 查询参数字典，包含 metrics 和 filters
    :param target_file_path: 包含所有指标数据的 JSON 文件路径
    :param case_type : 案例数据浏览器类型，用于获取默认排序配置
    :return: 构建好地请求体，如果没有结果则返回 None
    """
    try:
        # 加载所有的指标数据
        target_data = load_target_data(target_file_path)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.error(f"[build_request_body] 加载目标数据时出错: {str(e)}")
        return None

    # 从 query_params 获取要查询的指标和筛选条件
    metrics = query_params.get("metrics", [])
    filters = query_params.get("filters", {})
    sort_by = query_params.get("sort_by")
    sort_order = query_params.get("sort_order", "asc").upper()

    # 构建 quotas 列表，包含每个指标的 mapper, label, data_type 等信息
    quotas: List[Dict[str, Any]] = [
        {"label": "案例ID", "mapper": "mapper.case_id", "mapper_fields": [], "date_type": "STRING"}]
    # 把 filters 中的 key 和排序字段添加到 metrics
    if filters:
        metrics.extend([k for k in filters.keys() if k not in metrics])

    # 添加排序字段到 metrics（如果存在且不在 metrics 中）
    if sort_by and sort_by not in metrics:
        metrics.append(sort_by)

    # 对指标去重
    metrics = list(dict.fromkeys(metrics))

    for metric in metrics:
        # 从 target_data 中找到指标相关信息
        target_info = target_data.get(metric)
        if not target_info:
            # return {"error": f"未在目标数据中找到指定的指标: {metric}。检测到该指标可能为模型自行生成，请向用户确认正确的 '{metric}' 指标或建议替代指标。"}
            # return {"error": f"指标异常: {metric}。检测到该指标为模型自行生成，优先尝试寻找已存在的替代指标，进行重试"}
            if metric == '次数':
                return {"error": '次数指标不存在，根据返回数据条数判断即可'}
            else:
                return {
                    "error": f"指标异常: {metric}。该指标可能为模型自动生成，建议尝试替代指标进行重试，或向用户从指标列表中确认正确的 '{metric}' 指标。"}

        # 如果该指标有对应的筛选条件，构建 mapper_screens
        mapper_screens = build_mapper_screens({metric: filters[metric]}) if filters and metric in filters else None
        quotas.append(create_quota_entry(target_info, mapper_screens))

        # 如果 target_info 中存在关联字段（relatedFields），则处理这些字段
        related_fields = target_info["related_params"].split(" ") if target_info.get("related_params", "") else []
        for related_field in (field for field in related_fields if field and field not in metrics):
            related_target_info = target_data.get(related_field)
            if related_target_info:
                quotas.append(create_quota_entry(related_target_info))
            else:
                print(f"在目标数据中未找到关联字段: {related_field}")
                return {"error": f"在目标数据中未找到关联字段: {related_field}"}

    if not quotas:
        print("未找到有效的指标")
        return {"error": "未找到有效的指标"}

    # 处理排序
    if sort_by:
        sort_mapper = next((quota["mapper"] for quota in quotas if quota["label"] == sort_by), None)
        if sort_mapper:
            sorts = [{
                "mapper": sort_mapper,
                "direction": "ASC" if sort_order == "ASC" else "DESC"
            }]
        else:
            return {"error": f"未找到排序字段: {sort_by}"}
    else:
        # 如果没有提供 sort_by，使用默认排序
        sorts = get_default_sorts(case_type)

    # 构建排序和其他部分
    return {
        "title": query_params.get("title", "数据浏览器"),
        "quotas": quotas,
        "ingredients": [],
        "sorts": sorts,
        "selections": [],
        **({'questions': []} if case_type == "questions" else {})
    }


def clean_dict(d):
    if not isinstance(d, dict):
        return d
    result = {}
    for k, v in d.items():
        cleaned_v = clean_dict(v)
        if cleaned_v not in ("", {}, []):
            result[k] = cleaned_v
    return result


def replace_keys(obj, mapper):
    """
    将字典中的键名替换为中文

    Args:
        obj: 要处理的对象（字典、列表或其他类型）
        mapper: 映射字典，键是原始路径（如 'company.securities_code.newest'），值是中文键名

    Returns:
        处理后的对象，键名已替换为中文
    """
    # 预处理映射字典，提取字段映射
    field_mappers = {}
    for path, value in mapper.items():
        parts = path.split('.')
        if len(parts) >= 2:
            # 对于形如 'company.securities_code.newest' 的路径，提取 'securities_code'
            if len(parts) >= 3 and parts[-1] == 'newest':
                field_key = parts[-2]  # 取倒数第二个部分
                field_mappers[field_key] = value
            # 对于形如 'company.securities_code' 的路径，提取 'securities_code'
            else:
                field_key = parts[-1]  # 取最后一个部分
                field_mappers[field_key] = value

    # 处理函数
    def process(obj):
        # 处理列表
        if isinstance(obj, list):
            return [process(item) for item in obj]

        # 处理字典
        if isinstance(obj, dict):
            result = {}

            for k, v in obj.items():
                # 特殊处理 'newest' 键
                if k == 'newest':
                    result['最新'] = v
                else:
                    # 检查是否有字段映射
                    new_key = k
                    if k in field_mappers:
                        new_key = field_mappers[k]

                    # 递归处理值
                    result[new_key] = process(v)

            return result

        # 其他类型直接返回
        return obj

    return process(obj)


def make_post_request(body, type: Optional[str] = ''):
    """发送 POST 请求并处理可能的错误。"""
    try:
        # 定义 API 的 URL 和请求头
        if type == 'inquiry_letter':
            api_url = "https://services.easy-board.com.cn/tdb-api/tdbl/quota?page=0&size=10"
        elif type == 'target_asset':
            api_url = "https://services.easy-board.com.cn/tdb-api/tdbt/quota?page=0&size=10"
        elif type == 'violation':
            api_url = "https://services.easy-board.com.cn/tdb-api/tdbv/quota?page=0&size=10"
        else:
            api_url = "https://services.easy-board.com.cn/tdb-api/tdbf/quota?page=0&size=10"
        access_token = get_access_token()
        headers = {
            "authorization": access_token,
            "Content-Type": "application/json;charset=UTF-8"
        }

        response = requests.post(api_url, headers=headers, json=body)

        # 如果状态码是 401，需要重新获取 access_token
        if response.status_code == 401:
            # 获取新的 access_token
            remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')
            new_access_token = get_access_token()
            headers.update({"authorization": new_access_token})
            response = requests.post(api_url, headers=headers, json=body)

        if response.status_code == 200:
            result = response.json()

            # 发送请求体、url、结果 信息
            # asyncio.run(send_request_body_feedback(body, api_url, result, config))

            # 判断返回结果是否为空
            if result['total'] == 0:
                return {"error": "未找到符合条件的案例，请检查指标后重试"}

            # 处理数据
            result = {
                "案例总数": result['total'],
                "每页条数": result['size'],
                "当前页码": result['page'] + 1,
                "案例列表": [clean_dict(line) for line in result['result']],
            }

            # 调用替换函数
            result = replace_keys(result, compiled_mappers)
            logger.info(
                f"[make_post_request] {type}查询结果\n"
                f"案例总数{result['案例总数']},案例信息demo{result['案例列表']}")

            return result
        else:
            return {"error": f"请求失败，状态码: {response.status_code}, 信息: {response.text}"}
    except requests.exceptions.RequestException as e:
        return {"error": f"请求出错: {e}"}
