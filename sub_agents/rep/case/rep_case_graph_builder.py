import json

from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.case.rep_case_agent import rep_case_agent_runnable
from sub_agents.rep.case.rep_case_tools import rep_case_tool_list
from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.general.rep_biz_state_def import RepCaseAgentState
from sub_agents.rep.utils.web_utils import send_websocket_msg


class RepCaseAgent:
    def __init__(self):
        self.runnable = rep_case_agent_runnable

    async def __call__(self, _state: RepCaseAgentState, config: RunnableConfig):
        await send_websocket_msg(json.dumps({RepWSParam.FLOW_PROCESS.value: "案例助手正在检索"},
                                            ensure_ascii=False), config)
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_case_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_case_agent_messages": result}


tool_node = ToolNode(rep_case_tool_list, messages_key='rep_case_agent_messages')

builder = StateGraph(RepCaseAgentState)
builder.add_node("rep_case_agent", RepCaseAgent())
builder.add_node("rep_case_tools", tool_node)

builder.add_edge(START, "rep_case_agent")
builder.add_edge("rep_case_agent", "rep_case_tools")
builder.add_edge("rep_case_tools", END)

rep_case_graph = builder.compile()
#
# if os.getenv('env', 'test') == 'test':
#     rep_case_graph.get_graph(xray=True).draw_mermaid_png(
#         output_file_path='sub_agents/rep/case/rep_case_graph.builder.png')
