import json
import time
import traceback

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

import sub_agents.rep.case.violation_case as violation_case_utils
from sub_agents.rep.case.rep_case_utils import http_call_list, RepurchaseQuery
from sub_agents.rep.case.rep_case_ws import __send_vio_case_result, __send_rep_case_result
from utils.load_env import logger

IGNORE_KEYS = ['id', 'bid', 'subjectId', 'caseId', 'investRemark', 'repeateAccount', 'impDetailId', 'repCaseId']


@tool
async def get_case_list(query: RepurchaseQuery, config: RunnableConfig) -> str:
    """
    获取回购报告列表
    :param query 详细的查询条件
    :return:
    - 符合条件的案例总数
    - 案例列表:
      * 案例标题: str
      * 公司代码及名称 str
      * 当前回购进程 str
      * 回购目的: str
      * 拟回购资金总额上限: str
      * 拟回购资金总额下限: str
      * 回购价格上限占董事会决议前30个交易日均价比例: str
      * 距首次披露日股价涨跌幅: str
      * 首次公告日: str
      * 案例链接: str
    """
    start_time = time.time()
    logger.info(f"开始执行get_case_list函数，查询条件：{query}")

    if isinstance(query, dict):
        query = RepurchaseQuery(**query)
    if query.repWayType and "要约" in query.repWayType:
        logger.info(f"get_case_list执行完成，暂无要约方式回购案例，耗时：{time.time() - start_time:.2f}秒")
        return json.dumps({'案例总数': 0, '案例列表': [], '说明': '目前市场暂无要约方式进行回购'})

    _query = {k: v for k, v in query.dict().items() if v}
    try:
        response = await http_call_list(_query)
        results = {
            '符合条件的案例总数': response.get('total'),
            '符合条件的部分案例': response.get('case_list'),
        }

        # 给客户端发送消息
        await __send_rep_case_result(response.get('case_list'), config)

        logger.info(
            f"get_case_list执行完成，成功返回{results['符合条件的案例总数']}条案例，耗时：{time.time() - start_time:.2f}秒")
        return json.dumps(results, ensure_ascii=False)

    except Exception as e:
        logger.error(f"get_case_list执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        traceback.print_exc()
        return json.dumps({
            "案例总数": 0,
            "案例列表": [],
            '错误信息': f"抱歉，处理您的请求时出现了错误。{str(e)}"}, ensure_ascii=False)


@tool
async def get_violation_case(keywords: list[str], config: RunnableConfig) -> str:
    """
    根据用户的问题，查询回购相关的违规案例信息，用户提及违规时调用
    :param keywords: 根据用户输入的问题提取出的回购违规案例的关键词，关键词字数控制在2个字
    :return:
    """
    start_time = time.time()
    logger.info(f"开始执行get_violation_case函数，查询内容：{str(keywords)}")
    try:
        result = await violation_case_utils.get_violation_case(keywords)

        # 给客户端发送消息
        await __send_vio_case_result(result, config)

        logger.info(f"get_violation_case执行完成，成功返回结果，耗时：{time.time() - start_time:.2f}秒")
        return str({"违规案例检索结果": result})
    except Exception as e:
        logger.error(f"get_violation_case执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


rep_case_tool_list = [get_case_list, get_violation_case]
