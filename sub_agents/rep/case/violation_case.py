# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：violation_case.py 
@Date    ：2025/1/13 下午2:11 
<AUTHOR>
@Desc    ：
"""
import json

from bs4 import BeautifulSoup

from sub_agents.rep.case.external_utils.case_utils import build_request_body, make_post_request
from utils.load_env import logger

violation_file_path = 'sub_agents/rep/case/data/违规案例.json'


async def get_violation_case(keywords: list[str]) -> str:
    """
    使用数据浏览器，查询回购相关的违规案例信息
    :param keywords: 根据用户输入的问题提取出的回购违规案例的关键词
    :return:
    """
    try:
        metrics = ['案例ID', '案例序号', '证券代码', '公司简称', '案例标题', '处罚对象名称',
                   '处罚对象身份', '违规类型', '处罚日期']
        filters = {'违规类型': '回购'}
        sort_by = '处罚日期'
        sort_order = 'desc'
        # 构建请求体
        query_params = {
            "metrics": metrics,
            "filters": filters,
            "sort_by": sort_by,
            "sort_order": sort_order
        }
        body = build_request_body(query_params, violation_file_path)

        if not body or 'error' in body:
            return str(body)  # 如果请求体为空，直接返回结果
        if body['quotas']:
            # 将违规内容拼接到查询里（在此拼接原因是因为此版本不支持数据的OR操作）
            body['quotas'].append({
                'date_type': 'STRING',
                'label': '违规内容',
                'mapper': 'principal.violate_content',
                'mapper_fields': [],
                'mapper_screens': {
                    'condition': [
                        {'formula': 'CONTAINS', 'logic': 'OR', 'val': ' '.join(keywords)}
                    ],
                    'mode': 'CUSTOM'
                }
            })

        logger.info(f"[回购违规案例] 构造的请求体：{body}\n")
        result = make_post_request(body, 'violation')
        return json.dumps(process_data(result), ensure_ascii=False)
    except Exception as e:
        logger.error(f"发生错误: {e}")
        return f"违规案例检索发生错误{e}"


def process_data(data: dict) -> dict:
    total = data.get('案例总数', 0) or 0
    case_list = data.get('案例列表', []) or []
    data_list = []
    for item in case_list:
        v_id = (item.get('mapper') or {}).get('vid') or ''
        company_code = ((item.get('company') or {}).get('证券代码') or {}).get('最新') or ''
        principal = item.get('principal') or {}
        v_obj_name = principal.get('处罚对象名称') or ''
        v_obj_identity = principal.get('处罚对象身份') or ''
        v_title = principal.get('violate_title') or ''
        v_date = principal.get('处罚日期') or ''
        v_type = (principal.get('违规类型') or {}).get('1') or ''
        v_content = principal.get('违规内容') or ''
        soup = BeautifulSoup(v_content, 'lxml')
        data_list.append({
            'v_id': v_id,
            '证券代码': company_code,
            '违规标题': v_title,
            '处罚对象名称': v_obj_name,
            '处罚对象身份': v_obj_identity,
            '处罚日期': v_date,
            '违规内容': soup.get_text(),
            '违规类型': v_type
        })
    # 处理数据中的字典值
    del data['案例总数']
    del data['案例列表']
    del data['每页条数']
    del data['当前页码']
    # 赋值
    data['符合条件的违规案例总数'] = total
    data['部分违规案例数据' if total > len(data_list) else '违规案例数据'] = data_list
    return data
