from datetime import datetime

from langchain_core.prompts import ChatPromptTemplate

from sub_agents.rep.calc.rep_calc_tools import rep_calc_tool_list
from sub_agents.rep.utils.llm_utils import get_a_llm

prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
    <role>
    你是一个专注于是否符合进行回购条件测算分析的AI专家。你的任务是根据用户的问题测算某公司对应交易日期是否可以进行股份回购，回答用户的问题。
    </role>

    当前时间: {time}
    当前公司信息: {curr_com_info}
     """),
    ("placeholder", "{rep_calc_agent_messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm()

rep_calc_agent_runnable = prompt_template | llm.bind_tools(rep_calc_tool_list)
