from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.calc.rep_calc_agent import rep_calc_agent_runnable
from sub_agents.rep.calc.rep_calc_tools import rep_calc_tool_list
from sub_agents.rep.general.rep_biz_state_def import RepCalcAgentState


class RepCalcAgent:
    def __init__(self):
        self.runnable = rep_calc_agent_runnable

    async def __call__(self, _state: RepCalcAgentState, config: RunnableConfig):
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(
                {**_state, "curr_com_info": config.get('configurable', {}).get("curr_com_info")},
                config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_calc_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_calc_agent_messages": result}


tool_node = ToolNode(rep_calc_tool_list, messages_key='rep_calc_agent_messages')

builder = StateGraph(RepCalcAgentState)
builder.add_node("rep_calc_agent", RepCalcAgent())
builder.add_node("rep_calc_tools", tool_node)

builder.add_edge(START, "rep_calc_agent")
builder.add_edge("rep_calc_agent", "rep_calc_tools")
builder.add_edge("rep_calc_tools", END)

rep_calc_graph = builder.compile()

# if os.getenv('env', 'test') == 'test':
#     rep_calc_graph.get_graph(xray=True).draw_mermaid_png(
#         output_file_path='sub_agents/rep/calc/rep_calc_graph.builder.png')
