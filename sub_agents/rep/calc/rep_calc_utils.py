import time

import requests

from sub_agents.rep.utils.web_utils import get_access_token
from utils.redis_tool import remove_redis_info

TOKEN_REDIS_KEY_NAME = 'REP_AGENT_ACCESS_TOKEN'


def http_call_calc(data=None):
    access_token = get_access_token(TOKEN_REDIS_KEY_NAME)
    url = "https://services.easy-board.com.cn/capital-cloud-api/aiCaseInfo/getRepCaseList"
    response = requests.post(
        f"{url}?access_token={access_token}",
        json=data)
    if response.status_code == 200:
        results = response.json().get('result')
        results = {'calc_res': '符合' if results.get('calculateResult') else '不符合',
                   'calc_source': results.get('calculateSource')}
        return results

    elif response.status_code == 401:
        # access_token = get_access_token()将此键放到了redis
        remove_redis_info(TOKEN_REDIS_KEY_NAME)
        time.sleep(1)
        return http_call_calc(data)
    else:
        raise Exception(f"Error occurred while making HTTP request: {response.status_code}")
