import json
from typing import Optional

from langchain_core.tools import tool
from pydantic.v1 import BaseModel, Field

from sub_agents.rep.calc.rep_calc_utils import http_call_calc
from utils.company_utils import confirm_company


class RepCalculateQuery(BaseModel):
    companyCode: Optional[str] = Field(alias='公司代码或公司名称，必填')
    tradeDate: Optional[str] = Field(alias='交易日期，默认为今天')


class RepCalculateForm(BaseModel):
    交易日期: Optional[str] = Field(alias='trade_date')
    收盘价_元: Optional[str] = Field(alias='closing_price')
    对应每股净资产: Optional[str] = Field(alias='net_assets_per_share')
    连续20个交易日内公司股票收盘价累计跌幅_小数: Optional[str] = Field(alias='accumulated_amplitude')
    最近1年股票最高收盘价_元: Optional[str] = Field(alias='recent_max_price')
    收盘价占最近1年股票最高收盘价比例_小数: Optional[str] = Field(alias='new_ratio_max')
    符合的回购条件: Optional[str] = Field(alias='eligible')


@tool
def get_calc_result(query: RepCalculateQuery) -> str:
    """
    获取回购的测算结果
    :param query 详细的查询条件
    :return:
    - 是否符合回购条件
    - 符合回购条件对应的数据列表:
      * 交易日期: str
      * 收盘价_元: str
      * 对应每股净资产: str
      * 连续20个交易日内公司股票收盘价累计跌幅_小数: str
      * 最近1年股票最高收盘价_元: str
      * 收盘价占最近1年股票最高收盘价比例_小数: str
      * 符合的回购条件: str
    """
    if isinstance(query, dict):
        query = RepCalculateQuery(**query)
    # 从数据库查询公司代码
    companies = confirm_company(query.__getattribute__('companyCode'))
    if not companies:
        return '未获取到查询公司，请尝试更换公司'
    company = companies[0]
    company_code = company.get('company_code')
    _query = {k: v for k, v in query.dict().items() if v}
    _query.update({"companyCode": company_code})

    try:
        response = http_call_calc(_query)
        results = {
            '是否符合回购条件': response.get('calc_res'),
            '符合回购条件对应的数据列表': [RepCalculateForm(**line).dict() for line in
                                           response.get('calc_source')] if response.get('calc_source') else [],
            '说明': '此测算结果仅应用于《上市公司股份回购规则》中第二条规定的“为维护公司价值及股东权益所必需”的回购股份行为',
        }
        return json.dumps(results, ensure_ascii=False)

    except Exception as e:
        return json.dumps({
            '是否符合回购条件': 0,
            '符合回购条件对应的数据列表': [],
            '说明': '此测算结果仅应用于《上市公司股份回购规则》中第二条规定的“为维护公司价值及股东权益所必需”的回购股份行为',
            '错误信息': f"抱歉，处理您的请求时出现了错误。{str(e)}"}, ensure_ascii=False)


rep_calc_tool_list = [get_calc_result]
