from typing import Sequence, Literal

from langchain_core.messages import ToolMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph

from sub_agents.rep.ann.rep_ann_graph_builder import rep_ann_graph
from sub_agents.rep.case.rep_case_graph_builder import rep_case_graph
from sub_agents.rep.design.rep_design_graph_builder import rep_design_graph
from sub_agents.rep.general.reflector_agent import RepReflector
from sub_agents.rep.general.rep_biz_agent_def import ASST_AGENT_MAP
from sub_agents.rep.general.rep_biz_state_def import RepGenAgentState
from sub_agents.rep.general.rep_gen_agent import RepGenAgent
from sub_agents.rep.general.spokesman_agent import RepSpokesman
from sub_agents.rep.intro.rep_intro_graph_builder import rep_intro_graph
from sub_agents.rep.laws.rep_laws_graph_builder import rep_laws_graph
from utils.load_env import logger

logger.info("Loading File")

rep_builder = StateGraph(RepGenAgentState)


# define node
async def rep_return_gen_node(_state: RepGenAgentState) -> dict:
    logger.info("进入汇总节点啦，rep_return_gen_node")
    if not (_state["messages"] and _state["messages"][-1].type == "ai"):
        return _state
    # 获取最新AIMessage的tool_call_id
    # 获取tools_calls中的所有id
    tool_calls = _state["messages"][-1].tool_calls
    messages_result = []
    if tool_calls:
        for tool_call in tool_calls:
            tool_call_id = tool_call["id"]
            tool_call_name = tool_call["name"]

            # 获取对应代理的消息列表
            tool_call_messages = _state[f'{ASST_AGENT_MAP.get(tool_call_name)}_messages']
            if tool_call_messages:
                # 找到biz_tools_id对应业务代理HumanMessage后的ToolMessage
                # 并把其中的content拼接后形成一个新的ToolMessage返回
                found_human = False
                tool_call_message_content = ''
                for tool_call_message in tool_call_messages:
                    if (tool_call_message.type == 'human' and hasattr(tool_call_message, 'biz_tools_id')
                            and tool_call_message.biz_tools_id == tool_call_id):
                        found_human = True
                        continue
                    if found_human and tool_call_message.type == 'tool':
                        tool_call_message_content += '\n' + tool_call_message.content
                messages_result.append(
                    ToolMessage(
                        content=tool_call_message_content,
                        tool_call_id=tool_call_id,
                    )
                )
    _state['messages'] = messages_result
    logger.info("离开汇总节点啦，rep_return_gen_node")
    return _state


# add_node
rep_builder.add_node("RepGenAgent", RepGenAgent())

rep_builder.add_node("RepReturnGen", rep_return_gen_node)
rep_builder.add_node("RepReflector", RepReflector())
rep_builder.add_node("RepSpokesman", RepSpokesman())

rep_builder.add_node("ToRepCaseAgent", rep_case_graph)
rep_builder.add_node("ToRepAnnAgent", rep_ann_graph)
rep_builder.add_node("ToRepDesignAgent", rep_design_graph)
rep_builder.add_node("ToRepLawsAgent", rep_laws_graph)
rep_builder.add_node("ToRepIntroAgent", rep_intro_graph)

# add_edge
rep_builder.add_edge(START, "RepGenAgent")
rep_builder.add_edge("RepReturnGen", "RepReflector")

rep_builder.add_edge("ToRepCaseAgent", "RepReturnGen")
rep_builder.add_edge("ToRepAnnAgent", "RepReturnGen")
rep_builder.add_edge("ToRepDesignAgent", "RepReturnGen")
rep_builder.add_edge("ToRepLawsAgent", "RepReturnGen")
rep_builder.add_edge("ToRepIntroAgent", "RepReturnGen")

rep_builder.add_edge("RepSpokesman", END)


# add_conditional_edges
async def route_to_nodes(state: RepGenAgentState) -> Sequence[str]:
    if not state["which"]:
        state["which"] = ["RepReflector"]

    return state["which"]


nodes_name_list = ["RepReflector"] + list(ASST_AGENT_MAP.keys())
rep_builder.add_conditional_edges(
    "RepGenAgent",
    route_to_nodes,
    nodes_name_list,
)


async def route_sum_to_gen(_state: RepGenAgentState) -> Literal["RepGenAgent", "RepSpokesman"]:
    """
    判断总结node走向
    :return: - RepGenAgent
             - RepSpokesman
    """
    if _state["which"] == ["RepGenAgent"]:
        return 'RepGenAgent'
    else:
        return 'RepSpokesman'


rep_builder.add_conditional_edges("RepReflector", route_sum_to_gen)

# 生成图片
rep_gen_agent_graph = rep_builder.compile(checkpointer=MemorySaver())
# rep_gen_agent_graph.get_graph(xray=True).draw_mermaid_png(
#     output_file_path='sub_agents/rep/general/rep_gen_agent_graph.builder.png')
