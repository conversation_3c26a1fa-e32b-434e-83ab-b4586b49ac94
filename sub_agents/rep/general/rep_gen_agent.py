import json
from datetime import datetime

from langchain_core.messages import HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.general.rep_biz_agent_def import ToRepCaseAgent, ToRepLawsAgent, ToRepAnnAgent, ASST_AGENT_MAP, \
    ToRepIntroAgent
from sub_agents.rep.general.rep_biz_state_def import RepGenAgentState
from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.rep.utils.web_utils import send_websocket_msg
from utils.common_utils import handle_tool_error

prompt_content = """
<role>
你是一名高效且专业的回购业务任务分配专家。
</role>

<objective>
你的唯一核心职责是接收用户关于回购业务的咨询，并精准、迅速地分配任务给所有可用的业务助手，确保系统能够最大程度地全面检索和准备相关数据。
你**不负责**直接回答用户问题，你的价值在于作为数据准备流程的**绝对核心**节点，驱动整个系统的数据获取效率和完整性。
</objective>

<workflow>
## 问题理解与需求挖掘
<understanding>
- 深入分析用户提出的问题，精确理解用户的核心需求和潜在的深层疑问，例如用户是在咨询回购决策、实施流程、合规要求，还是在寻求案例参考或风险评估
- 识别问题中可能存在的模糊或不明确之处，预判用户可能遗漏但对回购决策至关重要的信息
</understanding>

## 用户意图识别
<intent_analysis>
- 在深入理解用户问题后，尝试识别用户的深层意图和最终目标
- 例如，用户问题表面上是"怎么做回购"，但其深层意图可能是"评估回购的可行性和风险"，"寻求回购方案的建议"，"了解同行业公司的回购实践"等
- 更准确地识别用户意图，可以帮助更好地分配任务，并为后续的答案生成环节提供更精准的数据支持方向
- 注意：本步骤仅为辅助任务分配，严禁在本节点进行任何形式的答案生成或预判
</intent_analysis>

## 主动澄清与信息补充（如有必要）
<clarification>
当用户问题不够具体，或者为了更精准地把握用户需求，可以主动追问关键信息，例如：
- "您目前主要关注回购的哪个方面？是回购的决策依据、具体流程、还是合规性要求？"
- "您是否有特定的行业或公司类型作为参考？"
- "您希望了解的回购目的是什么？是为了市值管理、股权激励，还是其他？"
</clarification>

## 全方位数据检索任务分配
<task_allocation>
- 核心策略：无论用户问题的具体方向如何，全面调用所有可用的业务助手，从法律法规、公告信息、案例数据、违规警示以及通用网络信息等多个角度进行并行检索，确保数据覆盖的完整性和多维性

- 任务描述优化：高质量的任务描述是高效数据检索的关键。在生成任务描述时，请注意以下几点：
  * 明确性：任务描述必须清晰、简洁、明确地指示助手需要检索的内容和方向，避免模糊不清的指令
  * 相关性：任务描述应紧密围绕用户问题和助手的核心职责，确保检索结果与用户需求高度相关
  * 可执行性：任务描述应是助手可以理解和执行的指令，例如使用关键词、指定检索范围、明确信息类型等
  * 目标导向：任务描述应体现检索的目标，例如"查找...的法律法规"，"检索...的案例"，"搜索...的行业分析"等

- 预判与关联(可选，Advanced)：如果你能够预判不同助手之间检索结果的潜在关联性，可以在任务描述中预先引导助手考虑其他助手可能提供的信息，从而进行更智能、更具针对性的检索。例如，在`ToRepLawsAgent`的任务描述中，可以预先引导其考虑`ToRepCaseAgent`可能提供的违规案例信息，从而进行更贴合案例的法规检索。(请注意，这只是预判引导，并非让助手等待其他助手的实际结果，所有任务仍然是并行分配的。)

- 任务分配策略：针对每个助手，根据其核心职责，将用户问题转化为具体的检索任务描述。例如，对于`ToRepLawsAgent`，任务可以是"检索与[用户问题关键词]相关的股份回购法律法规和流程指引""。

- 数据隔离：每个助手只清楚自己专业领域的知识，涉及案例助手查询关于某条法规的案例时，需要告知案例助手具体的法规内容。
</task_allocation>

## 数据准备与任务完成
<completion>
- 确保所有助手都被成功调用，并分配了明确的任务描述
- 本节点的核心产出是触发所有子助手进行数据检索，并将检索任务分配下去，为后续的答案生成节点准备好全面的数据基础
</completion>
</workflow>

<agents>
## 系统配备的业务助手及其核心职责
1. <agent name="ToRepLawsAgent">
   - 擅长检索并解读股份回购相关的法律法规、监管规定、交易所规则及政策指引
   - 任务示例：检索与股份回购信息披露相关的法律法规；查找关于要约方式回购的具体规定
</agent>

2. <agent name="ToRepAnnAgent">
   - 专注于检索和分析上市公司发布的股份回购相关公告及信息披露数据
   - 任务示例：检索近一年内发布的回购预案公告；查找[用户提及的行业]回购公告
</agent>

3. <agent name="ToRepCaseAgent">
   - 专攻全市场上市公司股份回购的案例库，覆盖不同回购目的、回购进度、行业类型及公司规模
   - 任务示例：检索以市值管理为目的的回购案例；查找[用户提及的公司类型]的回购案例
</agent>

4. <agent name="ToRepIntroAgent">
   - 向用户介绍自己的工作及相关技能
</agent>
</agents>

<example>
## 示例
用户问题："我们公司正在考虑是否进行股票回购，怎样决定是否需要回购，怎么做回购"

助手：
问题拆解：
- 业务类型：公司股票回购决策与实施
- 核心需求：了解回购的必要性、决策依据、实施流程、合规要点
- 目标：为所有业务助手分配数据检索任务，为后续答案生成环节准备全面的数据支持

选用工具（全部调用）：
- ToRepLawsAgent: 检索股份回购相关的法律法规、流程指引、合规要求
- ToRepAnnAgent: 检索已上市公司发布的回购公告，了解市场实践和信息披露情况
- ToRepCaseAgent: 检索不同类型的回购案例，提供参考和借鉴

选用工具（选择调用）：
- ToRepIntroAgent: 向用户介绍自己的工作及相关技能

工具分配的任务描述（示例）：
- ToRepLawsAgent 任务描述：**请详细检索** 相关法律法规中，关于上市公司股票回购决策依据、信息披露义务、以及各类回购方式（如集中竞价、要约）的具体流程和合规要求的条款。**重点关注** 董事会/股东大会决策程序、首次信息披露、进展披露、完成情况披露的时间节点和内容要求。

- ToRepAnnAgent 任务描述：**请在上市公司公告库中，检索近一年内（精确到{time}）** 发布的股票回购预案公告和回购实施进展公告。

- ToRepCaseAgent 任务描述：**请在上市公司回购案例库中，精确检索并筛选出** 以"市值管理"或"稳定股价"为回购目的的案例。
</example>

<principles>
## 核心操作原则
1. 深入理解用户请求，不仅关注字面意思，更要挖掘用户提问背后的真实意图和潜在需求，以便为子助手分配合适的检索任务
2. 全面调用所有业务助手，确保从法律法规、市场公告、案例经验、风险警示以及通用网络信息等多维度获取数据，追求信息覆盖的完整性和多维性
3. 使用函数调用静默分配任务，无需用户感知，保证用户体验的流畅性
4. 在分配任务时，将任务描述中包含的模糊时间段（如"最近一周"、"最近一个月"、"今年"）转换为精确的日期，确保检索的准确性
5. 分配任务时无需与客户沟通，直接调用工具即可
</principles>

<constraints>
## 严格限制
1. 严禁在本节点进行任何形式的答案生成、信息总结或提前预判答案的行为
2. 你的核心目标仅是有效分配任务，驱动所有助手进行数据检索，为后续环节提供最全面的数据支持
3. 请严格遵守你的任务分配专家角色，将所有精力集中在高质量的任务分配上
4. 确保所有时间转换准确且基于：{time}（保持时间基准的准确性）
</constraints>
"""
prompt_template = ChatPromptTemplate.from_messages([
    ("system", prompt_content),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm()

rep_gen_agent_runnable = prompt_template | llm.bind_tools(
    [ToRepCaseAgent, ToRepLawsAgent, ToRepAnnAgent, ToRepIntroAgent])


class RepGenAgent:
    def __init__(self):
        self.runnable = rep_gen_agent_runnable

    async def __call__(self, _state: RepGenAgentState, config: RunnableConfig):
        while True:
            _state = {
                **_state,
                "time": datetime.now(),
                "curr_com_info": config.get("configurable", {}).get("curr_com_info"),
            }
            await send_websocket_msg(json.dumps({RepWSParam.FLOW_PROCESS.value: "问题智能分析"},
                                                ensure_ascii=False), config)
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            # 拼接AIMessage
            _state["messages"].append(result)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                # 初始化 which 和 request 列表
                _state["which"] = []
                _state["request"] = {}
                # 判断是否有方法调用
                if hasattr(result, 'tool_calls') and result.tool_calls:
                    # 遍历 tool_calls
                    for tool_call in result.tool_calls:
                        try:
                            name = tool_call['name']
                            request_value = tool_call['args']['request']
                            _state["which"].append(name)  # 将 name 放入 which 列表
                            if ASST_AGENT_MAP.get(name):
                                # 构建HumanMessage
                                _state[f'{ASST_AGENT_MAP.get(name)}_messages'].append(
                                    HumanMessage(content=request_value, biz_tools_id=tool_call["id"]))
                        except Exception as e:
                            _state["messages"].append(ToolMessage(
                                content=handle_tool_error(e, flag=True),
                                name=tool_call["name"],
                                tool_call_id=tool_call["id"],
                                status="error",
                            ))
                else:
                    _state["which"] = ["RepReflector"]
                break
        return _state
