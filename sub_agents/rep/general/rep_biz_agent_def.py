from pydantic import BaseModel, Field

ASST_AGENT_MAP = {
    'ToRepCaseAgent': 'rep_case_agent',
    'ToRepAnnAgent': 'rep_ann_agent',
    'ToRepDesignAgent': 'rep_design_agent',
    'ToRepLawsAgent': 'rep_laws_agent',
    'ToRepIntroAgent': 'rep_intro_agent',
}


class RepGenAgent(BaseModel):
    """
    数据补充助手：当现有数据无法有效支持回答用户关于回购的问题时，
    可调用此助手请求更相关、准确或充分的数据。
    适用于以下情况：
        - 数据不相关：工具返回的数据与用户问题关联性低，无法直接用于回答。
        - 数据不支持回答：工具返回的数据类型或内容，无法直接转化为对用户问题的解答。
    """
    request: str = Field(
        ...,
        description="""
        数据补充请求指令：请清晰、具体地描述你需要助手补充的数据请求。
        请详细说明以下内容，以便助手能够准确理解你的需求并返回有效数据：
            - 问题描述： 简要描述当前数据存在的问题 (不相关/不支持回答)。
            - 期望数据描述： 详细描述你期望助手返回的**具体**数据内容、类型或特征。例如：
                *  如果数据不相关，请指明你希望获取的**相关主题**或**信息领域**的数据。
            - 补充信息（可选 - 但强烈建议提供，以提高数据补充的准确性和效率）：
                *  提供**关键词**、**搜索参数**或其他有助于助手更快更准定位到所需数据的线索。
                *  如果已知**更可靠的数据来源**，可以提供相关信息，引导助手从特定来源获取数据。

        **示例：**

        1.  **数据不相关 示例：**
            request='''
            问题描述：当前工具返回的是通用的产品介绍，与用户咨询的回购政策无关。
            期望数据描述：请查询并返回 **详细的回购政策条款**，特别是关于 **不满意回购的条件、流程和用户需要提供的材料**。
            补充信息：关键词： "不满意回购政策", "退款流程", "回购协议"。
            '''
            
        """
    )


class ToRepCaseAgent(BaseModel):
    """
    用于将工作转移到专门处理回购案例的助手。
    职责:
    - 查询符合条件的回购案例信息
    """
    request: str = Field(
        ...,  # 这表示该字段是必需的
        description="查询回购案例信息"
    )


class ToRepLawsAgent(BaseModel):
    """
    用于将工作转移到专门处理回购法规、回购方案流程指引的助手。
    职责:
    - 查询对应的回购法规信息
    """
    request: str = Field(
        ...,  # 这表示该字段是必需的
        description="此助手主要作用为查询与用户问题相关的股份回购相关法律及规定或回购方案流程指引"
    )


class ToRepDesignAgent(BaseModel):
    """
    用于将工作转移到专门进行股份方案设计的助手。
    职责:
    - 为用户提供一份详细的股份回购方案
    """
    request: str = Field(
        ...,  # 这表示该字段是必需的
        description="解答回购股份方案相关的问题，或协助用户制作回购股份方案"
    )


class ToRepAnnAgent(BaseModel):
    """
    用于将工作转移到专门进行公告查询的助手。
    职责:
    - 为用户提供股份回购公告相关数据
    """
    request: str = Field(
        ...,  # 这表示该字段是必需的
        description="根据用户提出的问题，查询出相关的公告信息"
    )


class ToRepIntroAgent(BaseModel):
    """
    获取自己的工作和技能介绍。
    职责:
    - 获取自己的工作和技能介绍
    """
    request: str = Field(
        ...,  # 这表示该字段是必需的
        description="获取自己的工作和技能介绍"
    )
