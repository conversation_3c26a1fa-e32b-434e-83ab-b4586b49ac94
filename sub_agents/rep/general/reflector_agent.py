import json
from datetime import datetime

from langchain_core.messages import AIMessage, RemoveMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.general.rep_biz_agent_def import RepGenAgent, ASST_AGENT_MAP
from sub_agents.rep.general.rep_biz_state_def import RepGenAgentState
from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.rep.utils.web_utils import send_websocket_msg

prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
<role>专业质量审查员</role>

<objective>对工具查询参考数据进行反思审查，确保为用户回复提供高质量支撑</objective>

<context>
- "问题"指用户最新提出的问题
- 工具只能获取数据，没有总结分析能力
- 当前时间是{time}，必要时将其纳入判断
</context>

<process>
1. <background_review>
   - 回顾用户对话内容，理解完整意图和上下文
   - 分析工具返回的查询结果，理解获取的数据内容
</background_review>

2. <critical_reflection>
   - 进行问题支持度评估：综合所有工具返回数据，评估**是否整体足以支持**回答用户问题
   - 即使部分工具未返回数据，只要**整体数据有效支持**，仍应视为通过
   - 明确判断：**整体数据是否支持回答问题**
   - 输出你的反思结果
</critical_reflection>

3. <recommendation>
   - 如数据不足：总结关键信息缺失，提供重新查询方向或关键词
   - 如数据充分：明确表示现有数据质量良好，可用于生成用户回复
   - 只在**确认整体数据不足**时，才考虑通知获取缺失数据
   - 为后续节点提供决策依据
</recommendation>
</process>

<principles>
- 用户隐私：保护用户隐私和敏感信息
- 数据合规：遵守公司数据安全和合规政策
- 客观中立：分析数据时保持中立客观立场
- 专业态度：始终保持专业严谨的反思态度
- 时效性考量：在必要时考虑信息的时效性
</principles>
     """),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm()

rep_reflector_runnable = prompt_template | llm.bind_tools([RepGenAgent])


class RepReflector:
    def __init__(self):
        self.runnable = rep_reflector_runnable

    async def __call__(self, _state: RepGenAgentState, config: RunnableConfig):
        while True:
            # 初始化参数
            _state = {
                **_state,
                "time": datetime.now(),
                "curr_com_info": config.get("configurable", {}).get("curr_com_info"),
            }
            # 反思次数达到2次就直接返回给RepSpokesman节点
            if _state['reflector_num'] == 2:
                _state["which"] = ['RepSpokesman']
                _state["request"] = {}
                return {
                    f"{msg_name}_messages": [RemoveMessage(id=m.id) for m in _state[f"{msg_name}_messages"]]
                    for msg_name in ASST_AGENT_MAP.values()
                }
            # 利用大模型判断节点走向
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or (isinstance(result.content, list) and not result.content[0].get("text"))):
                _state["messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                # 初始化 which 和 request 列表
                _state["which"] = []
                _state["request"] = {}
                break
        # 如果有工具调用说明需要回到RepGenAgent节点
        if result.tool_calls:
            _state['reflector_num'] += 1
            await send_websocket_msg(
                json.dumps({RepWSParam.FLOW_PROCESS.value: f"信息校核与补全({_state['reflector_num']})"},
                           ensure_ascii=False), config)
            _state["messages"].append(AIMessage(
                content=f"检测到内容缺失，请根据缺失内容补充后重新回答，缺失内容如下：\n{result.tool_calls[0]['args']['request']}"))
            _state['which'] = ['RepGenAgent']
            return _state
        else:
            # 删除业务agent消息记录
            return {
                **{
                    f"{msg_name}_messages": [RemoveMessage(id=m.id) for m in _state[f"{msg_name}_messages"]]
                    for msg_name in ASST_AGENT_MAP.values()
                },
                "messages": result
            }
