from datetime import datetime

from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.rep.general.rep_biz_state_def import RepGenAgentState
from sub_agents.rep.utils.llm_utils import get_a_llm
from sub_agents.rep.utils.msg_utils import add_remove_msgs

prompt_template = ChatPromptTemplate.from_messages([
    ("user",
     """
<role>
您是一位回购业务问答专家，请根据提供的参考资料，给出用户满意的答案。
</role>

<task>
<evaluation>
仔细评估参考资料与用户问题的相关性、信息匹配度和权威性。
资料不足时：直接回复用户，清晰指出资料不足的原因，并引导用户提供更详细的信息（如业务场景、关键词等），以便重新获取资料。
资料充足时：直接回答用户问题。
</evaluation>

<answer_requirements>
<clear_answer>
先明确回答用户问题，让用户快速理解核心内容，然后展开阐述。
</clear_answer>

<reference_analysis>
只能引用**拥有的参考资料**中提到的数据。
**务必区分出用户问题及相关资料中普通回购，回购出售，回购减持等操作**，回答时按照相应操作进行回答。
多引用资料以增强可信度。
**注意区分各板块及市场**的法规差异。
**注意区分"金额"、"数量"等上限和下限条件**，避免混淆。
**注意区分"自然日"和"交易日"**。
注意识别资料的日期顺序，**日期离当前时间越近，说明资料越重要**。
**仔细甄别法规中的相关条件（如"除外"，"例外"等条件）**，避免出现条件混淆。
总结法规其主要内容，不引用具体条款号，**规避法规"第X条"类似文本**，例如：‘《法规名称》：主要内容’。请用类似格式总结其他条款，但不提供具体条款号。。
你的历史回答有可能是错的，请每次对用户提出的问题重新进行思考。
</reference_analysis>

<supplementary>
对简短答案客观问题，补充1-2句相关信息，如补充信息时，**优先选择提供与问题相关的背景知识、行业趋势、或实践建议**。
提及资料来源时，提示用户来源于"易董"资料库。
当需要引导用户进行其他资料查询时，建议用户使用"易董"资料库进行查询。
</supplementary>

<formatting>
选择合适的格式（如 Markdown、列表、表格），确保回答内容清晰、易读。可使用标题、粗体、分隔线等增强可读性。
</formatting>

<tone>
用通俗易懂的语言，以专家视角进行拟人化、流畅的回答。
</tone>

<timeliness>
优先引用时间更近的法规资料。当前时间：{time}。
</timeliness>

<closing>
在回答末尾，添加一句简洁专业的结束语，以巧妙展现你作为回购业务咨询专家的智能性。例如：我还可以帮你什么什么（基于"问题"和"拥有的参考资料"），如果有需要的话欢迎向我提问。
</closing>
</answer_requirements>
</task>

<final_review>
相信你自己的能力，突破自己，努力努力能产生更好的结果。
**仔细思考确定这就是你的最终回答吗**，请仔细对照上述流程，对你的目标保持专注，坚持不懈将取得卓越的成就。
</final_review>
     """),
    ("placeholder", "{spokesman_msgs}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm(llm_type='hs-deepseek-r1')

rep_spokesman_runnable = prompt_template | llm


class RepSpokesman:
    def __init__(self):
        self.runnable = rep_spokesman_runnable

    async def __call__(self, _state: RepGenAgentState, config: RunnableConfig):
        # 管理历史消息（保留4条聊天记录）
        _state["messages"] = add_remove_msgs(_state["messages"], 4)
        # 找最近一次human消息索引
        last_conv_idx = -1
        for i in range(len(_state["messages"]) - 1, -1, -1):
            if _state["messages"][i].type == 'human':
                last_conv_idx = i
                break
        # 拼接对话
        spokesman_msgs = []
        last_conv_msgs = {"问题": "", "拥有的参考资料": []}
        for index, msg in enumerate(_state["messages"]):
            if index < last_conv_idx:
                # 拼接历史记录
                spokesman_msgs.append(msg)
            else:
                # 拼接本次对话
                if msg.content and ((not hasattr(msg, 'status')) or (hasattr(msg, 'status') and msg.status != 'error')):
                    if msg.type == 'human':
                        last_conv_msgs['问题'] = msg.content
                    elif msg.type == 'tool':
                        last_conv_msgs['拥有的参考资料'].append(msg.content)
        spokesman_msgs.append(HumanMessage(content=str(last_conv_msgs)))
        spokesman_state = {'spokesman_msgs': spokesman_msgs}
        result = await self.runnable.ainvoke(spokesman_state, config)
        _state["messages"].append(result)
        return _state
