from typing import TypedDict, Annotated, Optional, List

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages


class RepAnnAgentState(TypedDict):
    rep_ann_agent_messages: Annotated[list[AnyMessage], add_messages]


class RepCaseAgentState(TypedDict):
    rep_case_agent_messages: Annotated[list[AnyMessage], add_messages]


class RepDesignAgentState(TypedDict):
    rep_design_agent_messages: Annotated[list[AnyMessage], add_messages]


class RepLawsAgentState(TypedDict):
    rep_laws_agent_messages: Annotated[list[AnyMessage], add_messages]


class RepIntroState(TypedDict):
    rep_intro_agent_messages: Annotated[list[AnyMessage], add_messages]


class RepGenAgentState(RepAnnAgentState, RepCaseAgentState, RepDesignAgentState,
                       RepLawsAgentState, RepIntroState):
    messages: Annotated[list[AnyMessage], add_messages]
    curr_com_info: Optional[str]
    which: Optional[List[str]]  # 选择哪一个工具
    request: Optional[dict]
    reflector_num: Optional[int]  # 反思节点执行次数
