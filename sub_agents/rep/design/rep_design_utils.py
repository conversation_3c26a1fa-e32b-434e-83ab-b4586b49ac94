def delete_lines_containing_substring(input_string, target_substring):
    # 将输入字符串按行分割
    lines = input_string.splitlines()
    # 过滤掉包含指定子字符串的行
    filtered_lines = [line for line in lines if target_substring not in line]
    # 将过滤后的行重新组合成字符串
    result_string = '\n'.join(filtered_lines)

    return result_string

# import time
#
# import requests
#
# from utils.redis_tool import remove_redis_info
#
# CODE_NO_LIST = [
#     'REP_DESIGN_SHARE_TYPE',  # 回购股份种类
#     'REP_DESIGN_PURPOSE',  # 回购目的
#     'REP_DESIGN_WAY_TYPE',  # 回购方式
#     'REP_DESIGN_FUND_SOURCE_TYPE',  # 资金来源
#     'YES_OR_NO',  # 是否明确资金来源
#     'REP_DESIGN_PERIOD_TYPE'  # 实施期限类型
# ]
#
#
# def http_call_save_design(data: dict) -> str:
#     """
#         :param data: 回购方案设计的表单信息
#         :return: 回购方案设计的数据主键
#     """
#     # TODO 暂时返回固定编号，远程接口saveRepDesignInfo暂时没有
#     return "15448543968900931884"
#     # access_token = get_access_token()
#     # response = requests.post(
#     #     f"https://services.easy-board.com.cn/capital-cloud-api/aiCaseInfo/saveRepDesignInfo?access_token={access_token}",
#     #     json=data)
#     # if response.status_code == 200:
#     #     return response.json().get('result').get('designId')
#     #
#     # elif response.status_code == 401:
#     #     # access_token = get_access_token()将此键放到了redis
#     #     remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')
#     #     time.sleep(1)
#     #     return http_call_save_design(data)
#     # else:
#     #     raise Exception(f"Error occurred while making HTTP request: {response.status_code}")
#
#
# def http_call_file_url(design_id: str, doc_type: str) -> dict:
#     """
#         :param design_id: 回购方案设计编号
#         :param doc_type: 1 回购股份提议 2 回购股份方案 3 回购股份报告书
#         :return: 文件链接 https://oss.valueonline.cn/temp-bucket/public/rep/xxx.docx
#     """
#     data = {
#         'id': design_id,
#         'doc_type': doc_type,
#     }
#     access_token = get_access_token()
#     url = "https://services.easy-board.com.cn/rep-design/docOperation/exportWordPath"
#     response = requests.post(
#         f"{url}?access_token={access_token}",
#         json=data)
#     if response.status_code == 200:
#         results = response.json().get('result')
#         results = {'file_path': 'https://oss.valueonline.cn/' + results.get('filePath')}
#         time.sleep(10)
#         return results
#
#     elif response.status_code == 401:
#         # access_token = get_access_token()将此键放到了redis
#         remove_redis_info('CASE_SEARCH_ACCESS_TOKEN')
#         time.sleep(1)
#         return http_call_file_url(design_id, doc_type)
#     else:
#         raise Exception(f"Error occurred while making HTTP request: {response.status_code}")
