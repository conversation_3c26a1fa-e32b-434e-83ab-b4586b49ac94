import json
from decimal import Decimal
from typing import Optional

from langchain_core.tools import tool
from pydantic.v1 import BaseModel, Field

from sub_agents.rep.design.rep_design_utils import delete_lines_containing_substring
from utils.common_utils import read_file_text

# from utils.company_utils import confirm_company

# 回购方案设计用到的代码类型
# CODE_NO_LIST = [
#     'REP_DESIGN_SHARE_TYPE',  # 回购股份种类
#     'REP_DESIGN_PURPOSE',  # 回购目的
#     'REP_DESIGN_WAY_TYPE',  # 回购方式
#     'REP_DESIGN_FUND_SOURCE_TYPE',  # 资金来源
#     'YES_OR_NO',  # 是否明确资金来源
#     'REP_DESIGN_PERIOD_TYPE'  # 实施期限类型
# ]

# 回购方案设计对应的代码名称范围
VALUE_RANGE_MAP = {
    '回购股份种类': '无限售条件的A股流通股,无限售条件的B股流通股',
    '回购目的': '减少公司注册资本,员工持股计划或股权激励,转换上市公司发行的可转换为股票的公司债券,维护公司价值及股东权益',
    # '回购方式': '集中竞价',
    '资金来源': '自有资金,发行优先股募集的资金,发行债券募集的资金,发行普通股取得的超募资金,募投项目节余资金,已依法变更为永久补充流动资金的募集资金,金融机构借款',
    '是否明确资金来源': '是,否',
    '实施期限类型': '董事会,股东大会'
}


class RepDesignRqu(BaseModel):
    company_name: Optional[str] = Field(alias='公司名称，必填')
    rep_purpose: Optional[str] = Field(alias='回购目的，必填，可多选，取值范围：' + VALUE_RANGE_MAP.get('回购目的'))
    rep_price_lb: Optional[Decimal] = Field(alias='回购价格下限（元），取值范围：0 < rep_price_lb <= 10000')
    rep_price_ub: Optional[Decimal] = Field(
        alias='回购价格上限（元），必填，若rep_price_lb有值，则必须要大于它，取值范围：0 < rep_price_ub <= 10000')
    rep_money_lb: Optional[Decimal] = Field(alias='回购金额下限（元），注意数量的转换，取值范围：rep_money_lb > 0')
    rep_money_ub: Optional[Decimal] = Field(
        alias='回购金额上限（元），必填，注意数量的转换，若rep_money_lb有值，则必须要大于它，取值范围：rep_money_ub > 0')
    rep_number_lb: Optional[Decimal] = Field(
        alias='回购股份数量下限（股），必填，注意数量的转换，取值范围：rep_number_lb > 0')
    rep_number_ub: Optional[Decimal] = Field(
        alias='回购股份数量上限（股），必填，注意数量的转换，若rep_number_lb有值，则必须要大于它，取值范围：rep_number_ub > 0')
    fund_source: Optional[str] = Field(alias='资金来源，必填，取值范围：' + VALUE_RANGE_MAP.get('资金来源'))
    period_type: Optional[str] = Field(alias='决定实施期限的会议，必填，取值范围：' + VALUE_RANGE_MAP.get('实施期限类型'))
    implement_deadline: Optional[int] = Field(
        alias='此次回购时间，最终回购股份方案之日起不超过implement_deadline个月，必填，取值范围：1 <= implement_deadline <= 12')


class RepDesignRes(BaseModel):
    公司名称: Optional[str] = Field(alias='company_name')
    回购目的: Optional[str] = Field(alias='rep_purpose')
    回购价格下限: Optional[Decimal] = Field(alias='rep_price_lb')
    回购价格上限: Optional[Decimal] = Field(alias='rep_price_ub')
    回购金额下限: Optional[Decimal] = Field(alias='rep_money_lb')
    回购金额上限: Optional[Decimal] = Field(alias='rep_money_ub')
    回购股份数量下限: Optional[Decimal] = Field(alias='rep_number_lb')
    回购股份数量上限: Optional[Decimal] = Field(alias='rep_number_ub')
    资金来源: Optional[str] = Field(alias='fund_source')
    实施期限类型: Optional[str] = Field(alias='period_type')
    实施期限_最终回购股份方案之日起不超过n个月: Optional[int] = Field(alias='implement_deadline')


@tool
def get_design_info(query: RepDesignRqu) -> str:
    """
    获取回购方案设计的信息
    :param query 详细的回购方案设计信息
    :return: 最终回购方案的信息 或 最终方案的markdown格式的文本
    """
    if isinstance(query, dict):
        query = RepDesignRqu(**query)
    _query = {k: v for k, v in query.dict().items() if v}

    # 从数据库查询公司代码
    # companies = confirm_company(query.__getattribute__('companyCode'))
    # if not companies:
    #     return '未获取到查询公司，请尝试更换公司'
    # company = companies[0]
    # company_code = company.get('company_code')
    # _query.update({"company_code": company_code})
    # 自定义序列化函数
    def decimal_default(obj):
        if isinstance(obj, Decimal):
            return str(obj)
        raise TypeError

    print("_query==============================" + json.dumps(_query, ensure_ascii=False, default=decimal_default))
    try:
        # 获取回购方案涉及模板
        temp_content = read_file_text('sub_agents/rep/design/files/design_template.md')
        # 替换模板内容
        # 公司名称
        temp_content = temp_content.replace('{{company_name}}', _query.get('company_name'))
        # 回购目的
        temp_content = temp_content.replace('{{rep_purpose}}', _query.get('rep_purpose'))
        # 回购价格下限
        if _query.get('rep_price_lb'):
            temp_content = temp_content.replace('{{rep_price_lb}}', str(_query.get('rep_price_lb')))
        else:
            temp_content = delete_lines_containing_substring(temp_content, '{{have_rep_price_lb}}')
        # 回购价格上限
        temp_content = temp_content.replace('{{rep_price_ub}}', str(_query.get('rep_price_ub ')))
        # 定价原则
        temp_content = temp_content.replace('{{bod_resolution_date}}', str(_query.get('bod_resolution_date')))
        # 回购金额下限
        if _query.get('rep_money_lb '):
            temp_content = temp_content.replace('{{rep_money_lb}}', str(_query.get('rep_money_lb')))
        else:
            temp_content = delete_lines_containing_substring(temp_content, '{{have_rep_money_lb}}')
        # 回购金额上限
        temp_content = temp_content.replace('{{rep_money_ub}}', str(_query.get('rep_money_ub')))
        # 回购股份数量下限
        temp_content = temp_content.replace('{{rep_number_lb}}', str(_query.get('rep_number_lb')))
        # 回购股份数量上限
        temp_content = temp_content.replace('{{rep_number_ub}}', str(_query.get('rep_number_ub')))
        # 资金来源
        temp_content = temp_content.replace('{{fund_source}}', _query.get('fund_source'))
        # 实施期限
        temp_content = temp_content.replace('{{period_type}}', _query.get('period_type'))
        temp_content = temp_content.replace('{{implement_deadline}}', str(_query.get('implement_deadline')))
        return temp_content
    except Exception as e:
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


rep_design_tool_list = [get_design_info]
