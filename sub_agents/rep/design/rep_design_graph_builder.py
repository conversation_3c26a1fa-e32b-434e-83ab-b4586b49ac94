from langchain_core.runnables import RunnableConfig
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.design.rep_design_agent import rep_design_agent_runnable
from sub_agents.rep.design.rep_design_tools import rep_design_tool_list
from sub_agents.rep.general.rep_biz_state_def import RepDesignAgentState


class RepDesignAgent:
    def __init__(self):
        self.runnable = rep_design_agent_runnable

    async def __call__(self, _state: RepDesignAgentState, config: RunnableConfig):
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_design_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_design_agent_messages": result}


tool_node = ToolNode(rep_design_tool_list, messages_key='rep_design_agent_messages')

builder = StateGraph(RepDesignAgentState)
builder.add_node("rep_design_agent", RepDesignAgent())
builder.add_node("rep_design_tools", tool_node)

builder.add_edge(START, "rep_design_agent")
builder.add_edge("rep_design_agent", "rep_design_tools")
builder.add_edge("rep_design_tools", END)

rep_design_graph = builder.compile()
#
# if os.getenv('env', 'test') == 'test':
#     rep_design_graph.get_graph(xray=True).draw_mermaid_png(
#         output_file_path='sub_agents/rep/design/rep_design_graph.builder.png')
