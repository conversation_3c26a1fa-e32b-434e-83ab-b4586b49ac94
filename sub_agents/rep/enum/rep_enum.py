from enum import Enum


class RepWSParam(Enum):
    OUTPUT = "summary"  # 回答内容，以start做开始标志，stop做结束标志
    END_FLAG = "is_end"  # 本次连接是否可以彻底结束
    FLOW_PROCESS = "AI_AGENT_FLOW"  # 流程走到哪
    TOOL_DATA_BING = "REP_TOOL_DATA_BING"  # 【必应】工具数据
    TOOL_DATA_CASE_COM = "REP_TOOL_DATA_CASE_COM"  # 【案例-普通案例】工具数据
    TOOL_DATA_CASE_VIO = "REP_TOOL_DATA_CASE_VIO"  # 【案例-违规案例】工具数据
    TOOL_DATA_ANN_LIST = "REP_TOOL_DATA_ANN_LIST"  # 【公告-列表】工具数据
    TOOL_DATA_LAW_INFO = "REP_TOOL_DATA_LAW_INFO"  # 【法规-信息】工具数据
    TOOL_DATA_LAW_PG = "REP_TOOL_DATA_LAW_PG"  # 【法规-指引】工具数据
