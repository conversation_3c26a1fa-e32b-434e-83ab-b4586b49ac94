from enum import Enum


class ToolsType(Enum):
    LAWS = "1"  # 法律法规
    TWTS = "2"  # 推文
    VIO_CASE = "3"  # 违规案例
    REP_CASE = "4"  # 回购案例
    ANN = "5"  # 公告
    DISC_KEYS = "6"  # 信披要点


class EndMode(Enum):
    END = "is_end"  # 结束标志
    ERROR = "is_error"  # 错误标志（与结束标志二选一）


class Answer(Enum):
    OUTPUT = "summary"
    TYPE_NAME = "answerType"


class AnswerType(Enum):
    REASONING = "think"
    CONTENT = "answer"
