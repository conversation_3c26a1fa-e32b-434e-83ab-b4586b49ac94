from datetime import datetime

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.general.rep_biz_state_def import RepGenAgentState, RepIntroState
from sub_agents.rep.utils.llm_utils import get_a_llm


@tool
async def get_feat_intro() -> str:
    """
    获取自己的工作及相关技能介绍
    :return: 技能介绍
    """
    return """
    回购智能助手介绍
    基本概述：
    回购智能助手是一款专业的财务法律咨询工具，专注于股票回购领域的信息检索与问题解答。该助手通过先进的自然语言处理技术，能够精准理解用户提问，从多维度数据库中检索相关信息，并提供全面、专业的解答。
    核心功能：
    回购智能助手具备多维度的信息检索与整合能力，主要包括：
    法律法规查询：精准检索与股票回购相关的法律条文、监管规定及政策解读，确保用户获取最新、最权威的法规信息
    回购智库访问：连接专业回购知识库，提供理论研究、学术观点及专家解读
    回购公告分析：自动抓取并分析上市公司回购公告，提取关键信息如回购金额、价格区间、实施期限等
    案例库检索：包含丰富的回购成功案例，提供实操参考和最佳实践
    违规案例警示：整理回购违规案例及处罚结果，帮助用户了解合规边界，规避法律风险
    技术支持：
    助手基于DeepseekR1大型语言模型，具备强大的自然语言理解与信息整合能力。当用户提出问题后，系统会：
    精准理解用户意图
    从多个数据源并行检索相关信息
    对检索结果进行智能筛选与权重分配
    利用DeepseekR1模型综合分析数据，生成系统化、条理清晰的回答
    根据用户反馈不断优化回答质量
    应用价值：
    回购智能助手为企业财务人员、法务专家、投资顾问及监管人员提供专业支持，帮助用户快速掌握回购政策动向，了解市场最佳实践，规避合规风险，制定科学的回购策略，提高决策效率。无论是回购方案设计、合规审核还是风险评估，回购智能助手都能提供及时、准确、全面的专业支持。
    """


prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
     获取自己的工作和技能介绍
     """),
    ("placeholder", "{rep_intro_agent_messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm()

rep_intro_runnable = prompt_template | llm.bind_tools([get_feat_intro])


class RepIntroAgent:
    def __init__(self):
        self.runnable = rep_intro_runnable

    async def __call__(self, _state: RepGenAgentState, config: RunnableConfig):
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_intro_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_intro_agent_messages": result}


tool_node = ToolNode([get_feat_intro], messages_key='rep_intro_agent_messages')

builder = StateGraph(RepIntroState)
builder.add_node("rep_intro_agent", RepIntroAgent())
builder.add_node("rep_intro_tools", tool_node)

builder.add_edge(START, "rep_intro_agent")
builder.add_edge("rep_intro_agent", "rep_intro_tools")
builder.add_edge("rep_intro_tools", END)

rep_intro_graph = builder.compile()
