import time
import traceback
from typing import List, Literal

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from sub_agents.rep.laws.rep_laws_utils import query_laws_vector, query_process_guide, query_laws_api, \
    query_tweets_vector, query_laws_kw_vector, query_disclosure_points_api
from sub_agents.rep.laws.rep_laws_ws import __send_laws_result, __send_dp_result
from utils.load_env import logger


@tool
async def get_laws_info(keywords: list, selective_keywords: list, config: RunnableConfig) -> str:
    """
    根据回购业务法规相关的关键词采用向量检索关联的中国法规

    :param keywords 根据用户问题检索相关法规法条的关键词。注意：
                     - 根据用户问题适当扩展相关术语（如把"敏感期"替换为"限制"、"不得"等）。
                     - 使用此工具检索时，建议结合具体场景和法规上下文对关键词进行改写扩展。
                     - 每个关键词字数务必小于或等于4个字，关键词总个数务必小于或等于5个
    :param selective_keywords 根据用户问题和提供的optional_keywords选择的关键词
    :return: 查询出的法规
    """
    start_time = time.time()

    logger.info(f"开始执行get_laws_info函数，关键词：{keywords}")

    try:
        result = []

        result_vector = await query_laws_vector(keywords, config)
        result.extend(result_vector)
        logger.info(f'向量检索法规完成，找到{len(result_vector)}条结果')

        result_api = await query_laws_api(keywords, config)
        result.extend(result_api)
        logger.info(f'API检索法规完成，找到{len(result_api)}条结果')

        result_twts = await query_tweets_vector(keywords)
        result.extend(result_twts)
        logger.info(f'推文检索完成，找到{len(result_twts)}条结果')

        result_kw_vector = await query_laws_kw_vector(selective_keywords, config)
        result.extend(result_kw_vector)
        logger.info(f'向量检索法规关键词完成，找到{len(result_kw_vector)}条结果')

        # 合并 result_vector 和 result_kw_vector
        title_ids_in_v = {}
        if result_vector[0] != '未查询到相关法规':
            title_ids_in_v = set([item['title_id'] for item in result_vector])

        # 遍历第二个数组，找出不在第一个数组中的元素
        for item in result_kw_vector:
            if item['title_id'] not in title_ids_in_v:
                result_vector.append(item)
                # 更新集合，防止重复添加
                title_ids_in_v.add(item['title_id'])

        # 给客户端发送消息
        await __send_laws_result(result_vector, result_api, result_twts, keywords, config)

        logger.info(f"get_laws_info执行完成，总共返回{len(result)}条结果，耗时：{time.time() - start_time:.2f}秒")
        return str({'法规检索结果': result})

    except Exception as e:
        logger.error(f"get_laws_info执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_process_guide(sector_name: str, config: RunnableConfig) -> str:
    """
    获取回购相关的流程指引
    :param sector_name: 法规所属的板块；单选，可选值：创业板,深主板,沪主板,科创板,北交所；若未提及板块，则默认使用深主板
    :return: 查询出的对应板块的回购流程指引
    """
    start_time = time.time()
    logger.info(f"开始执行get_process_guide函数，板块：{sector_name}")

    try:
        result = await query_process_guide(sector_name, config)
        if not result:
            logger.info(f"get_process_guide执行完成，未找到相关流程指引，耗时：{time.time() - start_time:.2f}秒")
            return '未找到相关流程指引'

        logger.info(f"get_process_guide执行完成，成功返回结果，耗时：{time.time() - start_time:.2f}秒")
        return str({'回购流程指引': result})

    except Exception as e:
        logger.error(f"get_process_guide执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


DpTypes = Literal[
    "已回购股份处理完成公告",
    "以集中竞价方式减持已回购股份的预披露公告",
    "回购结果暨股份变动公告",
    "回购股份方案",
    "回购股份进展公告",
    "回购股份结果公告",
    "回购股份方案变更或终止公告",
    "回购报告书",
    "已回购股份注销（回购完成后3年持有期内的注销）",
    "回购方案",
    "前10名公众股东名册",
    "回购实施进展",
    "以集中竞价方式减持已回购股份的进展公告",
    "通知债权人",
    "期间过半仍未实施回购",
    "已回购股份减持结果暨股份变动公告",
    "要约回购股份方案",
    "回购股份业务其他公告",
    "回购股份注销完成暨股份变动公告",
    "回购实施结果暨股份变动（有股份注销）",
    "回购股份报告书",
    "回购股份提议",
    "回购实施结果暨股份变动（无股份注销）"
]


@tool
async def get_disclosure_points(type_list: List[DpTypes], config: RunnableConfig = None) -> str:
    """
    获取用户需要的信披文件（如回购方案或回购报告书等）中涉及到的注意事项、披露内容及合规要求。
    :param type_list 查询的信披文件类型
    :param config 当前用户配置
    :return: 查询结果
    """
    try:
        result = await query_disclosure_points_api(type_list, config)
        if not result:
            return '未找到相关文件的披露要点'
        # 给客户端发送消息
        await __send_dp_result(result, config)
        # 转换结果格式
        result = [{f"{item[0]}（{item[1]}）": item[2]} for item in result]
        return str({'相关文件的披露要点': result})

    except Exception as e:
        logger.error(f"执行出错，错误信息：{str(e)}")
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


rep_laws_tool_list = [get_laws_info, get_process_guide, get_disclosure_points]
