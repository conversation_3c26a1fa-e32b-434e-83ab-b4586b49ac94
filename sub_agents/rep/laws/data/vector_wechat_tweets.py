from langchain.chains.summarize import load_summarize_chain
from langchain_core.documents import Document
from langchain_core.prompts import PromptTemplate

from sub_agents.rep.utils.llm_utils import get_a_llm
from utils.milvus_tools import MilvusClient, text_embeddings


def create_rep_tweets_knowledge_base():
    # 初始化 Milvus 客户端
    client = MilvusClient("http://milvus-hw-test.valueonline.cn")

    # 创建集合
    response_create_collection = client.create_collection(
        collection_name="rep_tweets_knowledge_base",
        description="回购微信推文知识库",
        field_types=[
            {
                "name": "id",
                "primaryKey": True,
                "autoID": True,
                "dataType": "Int64",
                "description": "主键"
            },
            {
                "name": "tweets_title",
                "dataType": "VarChar",
                "maxLength": 1024,
                "description": "推文标题"
            },
            {
                "name": "summary",
                "dataType": "VarChar",
                "maxLength": 1024,
                "description": "概要"
            },
            {
                "name": "tweets_date",
                "dataType": "VarChar",
                "maxLength": 1024,
                "description": "推文日期"
            },
            {
                "name": "tweets_url",
                "dataType": "VarChar",
                "maxLength": 1024,
                "description": "推文地址"
            },
            {
                "name": "status",
                "dataType": "VarChar",
                "maxLength": 1024,
                "description": "状态"
            },
            {
                "name": "content",
                "dataType": "VarChar",
                "maxLength": 20480,
                "description": "推文内容"
            },
            {
                "name": "vector",
                "dataType": "FloatVector",
                "dimension": 768,
                "description": "向量文本"
            }
        ]
    )
    print("创建集合响应:", response_create_collection)

    # 创建索引
    response_create_index = client.create_index(
        collection_name="rep_tweets_knowledge_base",
        field_name="vector",
        index_name="vector_index",
        index_type="HNSW",
        extra_param='{"M": 32, "efConstruction": 256}',
        metric_type="COSINE"
    )
    print("创建索引响应:", response_create_index)

    # 加载集合到内存中
    response_load_collection = client.load_collection(
        collection_name="rep_tweets_knowledge_base"
    )
    print("加载集合响应:", response_load_collection)


def insert_data(fields):
    client = MilvusClient("http://milvus-hw-test.valueonline.cn")
    response = client.insert_vector(
        database_name="",
        collection_name="rep_tweets_knowledge_base",
        fields=fields
    )
    print(response)


def process_and_insert_tweet(tweet_title, tweet_content, tweet_date, tweet_url, tweet_status):
    """
    处理单条推文数据，生成 summary 和 vector，并插入 Milvus。
    """
    vector = text_embeddings(tweet_content)

    # 初始化 Langchain LLM 和 Summarize Chain (在函数内部初始化)
    llm = get_a_llm(llm_type="4o")
    prompt_template = """请为以下推文内容生成一个简洁的中文概要，重点突出推文的核心观点。

       推文内容:
       "{text}"

       简洁概要:"""
    PROMPT = PromptTemplate(template=prompt_template, input_variables=["text"])
    summarize_chain = load_summarize_chain(llm, chain_type="stuff", prompt=PROMPT)

    # 生成 summary
    document = [Document(page_content=tweet_content)]
    summary_text = summarize_chain.invoke(document)
    summary = summary_text['output_text']  # 获取 summary 文本内容

    fields = [
        [
            {"name": "tweets_title", "value": tweet_title},
            {"name": "summary", "value": summary},  # 使用生成的 summary
            {"name": "tweets_date", "value": tweet_date},
            {"name": "tweets_url", "value": tweet_url},
            {"name": "status", "value": tweet_status},
            {"name": "content", "value": tweet_content},
            {"name": "vector", "value": vector}
        ],
    ]

    # 插入数据
    insert_data(fields)
    print(f"推文 '{tweet_title}' 插入 Milvus 完成！")  # 打印插入成功的消息


if __name__ == '__main__':
    # 创建索引
    # create_rep_tweets_knowledge_base()

    # 准备批量数据 (和之前一样)
    tweets_data = [
        {
            "title": "北交所再接力！回购和现金分红新规落地！",
            "content": """**微信推文标题：现金分红、股份回购新规速览：优化回报机制，护航市场稳健发展**

**导语：**  为完善上市公司常态化分红机制，提升投资者回报，引导上市公司聚焦主业，促进资本市场平稳健康发展，监管部门于近期发布了一系列现金分红和股份回购新规。本文将为您快速解读新规要点。

---

**一、现金分红新规速览**

**政策背景：**

* 健全上市公司常态化分红机制
* 增强投资者回报
* 引导上市公司专注主业
* 促进市场平稳健康发展
* 尊重公司自治，引导约束异常分红
* 推动上市公司不断增强分红意识，优化分红方式，培育分红习惯，提高分红水平
* 促进上市公司整体分红水平稳中有升

**重要时间节点：**

* **2023年10月20日：** 证监会联合沪深交易所公开征求现金分红系列规则意见。
* **2023年12月15日：** 正式颁布《上市公司监管指引第3号——上市公司现金分红》（以下简称《现金分红指引》）及《上市公司章程指引》。

**主要修订内容（与征求稿相比，差异不大，主要修订以下四个方面）：**

**(一)  明确鼓励现金分红导向，推动提高分红水平**

* **核心要点：**
    * 强调鼓励现金分红的政策导向。
    * 加强对不分红公司的信息披露要求等制度约束，督促分红。
    * 重点关注财务投资较多但分红水平偏低的公司，督促提高分红水平，引导专注主业。
    *  **(详见《现金分红指引》第八条)**

**(二)  简化中期分红程序，推动进一步优化分红方式和节奏**

* **核心要点：**
    * **鼓励增加分红频次：**  明确鼓励公司在条件允许的情况下增加分红频次。 **(详见《现金分红指引》第三条)**
    * **优化中期分红条件：** 允许上市公司在召开年度股东大会审议年度利润分配方案时，在符合利润分配的条件下，一定额度内可以审议批准下一年中期现金分红条件和上限。
    * **便利提升分红频次：**  方便公司进一步提升分红频次，让投资者更好规划资金安排，更早分享企业成长红利。 **(详见《现金分红指引》第七条)**

* **新旧条款对比 (中期分红第七条)**

| 版本     | 内容                                                                                                                                                                                                                                                        |
| -------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **征求稿** | 上市公司召开年度股东大会审议年度利润分配方案时，可审议批准下一年中期现金分红的条件、比例上限、金额上限等。年度股东大会审议的下一年中期分红上限不应超过相应期间归属于上市公司股东的净利润。董事会根据股东大会决议制定具体的中期分红方案。<br/>上市公司应当严格执行公司章程确定的现金分红政策以及股东大会审议批准的现金分红方案。确有必要对公司章程确定的现金分红政策进行调整或者变更的，应当满足公司章程规定的条件，经过详细论证后，履行相应的决策程序，并经出席股东大会的股东所持表决权的三分之二以上通过。 |
| **新正式稿** | 上市公司召开年度股东大会审议年度利润分配方案时，可审议批准下一年中期现金分红的条件、比例上限、金额上限等。年度股东大会审议的下一年中期分红上限不应超过相应期间归属于上市公司股东的净利润。董事会根据股东大会决议在符合利润分配的条件下制定具体的中期分红方案。<br/>上市公司应当严格执行公司章程确定的现金分红政策以及股东大会审议批准的现金分红方案。确有必要对公司章程确定的现金分红政策进行调整或者变更的，应当满足公司章程规定的条件，经过详细论证后，履行相应的决策程序，并经出席股东大会的股东所持表决权的三分之二以上通过。 |

* **对比解读：**  正式稿与征求稿在第七条内容上基本一致，  **重点在于明确了中期分红的可操作性和鼓励方向。**

**(三)  加强对异常高比例分红企业的约束，引导合理分红**

* **核心要点：**
    * 强调上市公司制定现金分红政策时，需综合考虑自身盈利水平、资金支出安排和债务偿还能力。
    * 兼顾投资者回报和公司发展，引导合理分红。
    * 重点关注资产负债率较高且经营活动现金流量不佳，但存在大比例现金分红情形的公司。
    * 防止大比例分红对企业生产经营和偿债能力产生不利影响。
    * **(详见《现金分红指引》第十三条、第十四条)**

**(四)  贯彻落实独董改革，强化独董监督职责**

* **核心要点：**
    *  虽然《上市公司独立董事管理办法》删除了独立董事对利润分配事项发表明确独立意见的表述。
    *  但保留了独立董事“对可能损害上市公司或者中小股东权益的事项发表独立意见”的职责。
    *  正式稿增加独立董事可对现金分红相关事项发表独立意见的情形，实现法规衔接。
    *  为独立董事履行监督职责提供法规依据。
    *  **(详见《现金分红指引》第六条)**

---

**（二）《上市公司章程指引》修订要点 (与征求稿保持一致)**

* **修订核心：**  主要对分红相关条款进行修改。 **(第一百五十三条、一百五十五条、一百五十六条)**

* **修订内容：**
    1. **鼓励增加现金分红频次，引导中期分红习惯：** 稳定投资者分红预期。增加对中期分红的完成时限要求。
    2. **督促细化分红政策，明确分红目标：** 更好稳定投资者预期。引导公司在章程中制定分红约束条款，防范企业在利润不真实等情形下实施分红。

---

**二、股份回购新规速览**

**政策目的：**

* 顺应市场实际和公司需求
* 增强回购制度包容度和便利性
* 推动上市公司重视回购、实施回购、规范回购
* 积极维护公司价值和股东权益

**重要时间节点：**

* **2023年12月15日：** 证监会修订发布《上市公司股份回购规则》，沪深交易所同步修改《自律监管指引——回购股份》。

**本次回购规则修订要点：**

1.  **放宽“护盘式回购”触发条件，提高便利度：**
    * 将触发条件之一 “连续 20个交易日内公司股票收盘价格跌幅累计达到30%” 调整为 “累计达到 20%”。
    * **降低触发门槛，更易启动护盘回购。**

2.  **增设“维护公司价值及股东权益所必需”回购条件：**
    * 将 “股票收盘价格低于最近一年最高收盘价格50%” 作为触发条件之一。
    * **进一步增加回购便利性，支持价值维护型回购。**

3.  **取消禁止回购窗口期限制，仅保留“重大事项”窗口期：**
    *  删除定期报告、业绩预告、快报窗口期。
    * **扩大可回购的时间窗口，提升操作灵活性。**

4.  **放宽上市公司回购基本条件：**
    * 将“上市满一年”调整为“上市满六个月”。
    * **满足新上市公司回购需求，支持新上市公司稳定股价。**

5.  **增加每日可回购时段：**
    * 将原限制收盘前半小时不得回购交易申报，调整为收盘集合竞价阶段不得申报。
    * **增加每日可回购时段，提升交易效率。**

6.  **贯彻落实《上市公司独立董事管理办法》：**
    * 将独立董事发布意见相关事项同步删除。
    * **与新的独董管理办法保持一致。**

7.  **进行适应性文字性修改。**

**温馨提示：**  详细的回购解读，敬请期待明日的深度解读文章！

---

**（三）本次现金分红和回购涉及新规汇总**

**现金分红新规：**

* 《上市公司监管指引第3号——上市公司现金分红（2023年修订）》
* 《上市公司章程指引（2023年修订）》
* 《深圳证券交易所上市公司自律监管指引第1号——主板上市公司规范运作（2023年12月修订）》
* 《深圳证券交易所上市公司自律监管指引第2号——创业板上市公司规范运作（2023年12月修订）》
* 《上海证券交易所科创板上市公司自律监管指引第1号——规范运作（2023年8月修订）》
* 《上海证券交易所科创板上市公司自律监管指引第1号——规范运作（2023年12月修订）》

**回购新规：**

* 《上市公司股份回购规则（2023年修订）》
* 《深圳证券交易所上市公司自律监管指引第9号——回购股份（2023年修订）》
* 《上海证券交易所上市公司自律监管指引第7号——回购股份（2023年12月修订）》

**公告格式：**

* 《深圳证券交易所上市公司自律监管指南第2号——公告格式（2023年12月修订）》
* 《深圳证券交易所创业板上市公司自律监管指南第2号——公告格式（2023年12月修订）》
* 《上海证券交易所上市公司自律监管指南第1号——公告格式（2023年12月修订）》
* 《关于发布《上海证券交易所科创板上市公司自律监管指南（2023年12月修订）》的通知》

---
""",
            "date": "2023-12-15",
            "url": "https://mp.weixin.qq.com/s/EjNLC4WySsIHSNco_CC-0w?token=792742593&lang=zh_CN",
            "status": "1"
        },
    ]

    # 批量调用函数处理数据 (简化调用)
    for tweet_data in tweets_data:
        process_and_insert_tweet(
            tweet_data["title"],
            tweet_data["content"],
            tweet_data["date"],
            tweet_data["url"],
            tweet_data["status"],
        )

    print("🎉🎉🎉  所有推文数据批量插入完成！ 🎉🎉🎉")
