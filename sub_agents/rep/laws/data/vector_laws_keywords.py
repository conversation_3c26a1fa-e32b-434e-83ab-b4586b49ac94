import json

from utils.milvus_tools import MilvusClient, text_embeddings

COLLECTION_NAME = "rep_laws_keywords_base"

FILED_SET = {
    "laws_id", "parent_id", "section_name", "title_id", "laws_name", "chapter_name", "item_name",
    "published", "title", "law_range", "keywords"
}

client = MilvusClient("http://milvus-hw-test.valueonline.cn")


def create_rep_tweets_knowledge_base():
    field_types = [
        {"name": "laws_id", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "parent_id", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "section_name", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "title_id", "dataType": "Var<PERSON>har", "maxLength": 4096, "description": ""},
        {"name": "laws_name", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "chapter_name", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "item_name", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "published", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "title", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        # Array<VarChar(32)>[32]
        {"name": "law_range", "dataType": "Array", "maxLength": 32, "elementType": "VarChar", "maxCapacity": 32,
         "description": ""},
        # Array<VarChar(1024)>[32]
        {"name": "keywords", "dataType": "Array", "maxLength": 1024, "elementType": "VarChar", "maxCapacity": 32,
         "description": ""},

        {"name": "id", "primaryKey": True, "autoID": True, "dataType": "Int64", "description": ""},
        {"name": "embeddings_str", "dataType": "VarChar", "maxLength": 4096, "description": ""},
        {"name": "vector", "dataType": "FloatVector", "dimension": 768, "description": "向量文本"}
    ]

    # 创建集合
    response_create_collection = client.create_collection(
        collection_name=COLLECTION_NAME,
        description="回购法规关键字知识库",
        field_types=field_types
    )
    print("创建集合响应:", response_create_collection)

    # 创建索引
    response_create_index = client.create_index(
        collection_name=COLLECTION_NAME,
        field_name="vector",
        index_name="vector_index",
        index_type="HNSW",
        extra_param='{"M": 32, "efConstruction": 256}',
        metric_type="COSINE"
    )
    print("创建索引响应:", response_create_index)

    # 加载集合到内存中
    response_load_collection = client.load_collection(
        collection_name=COLLECTION_NAME
    )
    print("加载集合响应:", response_load_collection)


def process_and_insert_data():
    """
    处理单条推文数据，生成 vector，并插入 Milvus。
    """
    laws_id_list = ["15448543969582100636", "15448543969582114710", "15448543969582329854", "15448543969634358061"]
    for laws_id in laws_id_list:
        with open(f'sub_agents/rep/laws/data/words/words_{laws_id}_p.json', 'r', encoding='utf-8') as file:
            laws_info = json.load(file)
            for laws in laws_info:
                keywords = laws['keywords']
                if keywords:
                    keywords = sorted(keywords)
                    embeddings_str = " ".join(keywords)
                    vector = text_embeddings(embeddings_str)
                    one_data = [{"name": item, "value": laws[item]} for item in FILED_SET]
                    one_data.append({"name": "embeddings_str", "value": embeddings_str})
                    one_data.append({"name": "vector", "value": vector})
                    fields = [one_data]
                    # 插入数据
                    response = client.insert_vector(
                        database_name="",
                        collection_name=COLLECTION_NAME,
                        fields=fields
                    )
                    print(response)


if __name__ == '__main__':
    # 创建索引
    # create_rep_tweets_knowledge_base()

    # 批量插入数据
    # process_and_insert_data()

    # print("🎉🎉🎉  所有数据批量插入完成！ 🎉🎉🎉")
    pass
