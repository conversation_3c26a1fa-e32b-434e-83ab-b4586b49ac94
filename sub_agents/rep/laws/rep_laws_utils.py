import asyncio
import json
import traceback
from typing import Optional

import aiohttp
from bs4 import BeautifulSoup
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field

from sub_agents.rep.utils.web_utils import get_access_token
from utils.common_utils import decrypt_data
from utils.load_env import logger
from utils.milvus_tools import MilvusClient, milvus_host
from utils.redis_tool import remove_redis_info
from utils.rerank_tools import ReRankService, rerank_service_host

TOKEN_REDIS_KEY_NAME = 'REP_AGENT_ACCESS_TOKEN'

# SELECT * FROM sa_code WHERE valid_flag = '1' AND code_no = 'LAW_SCOPE' AND type = '1' ORDER BY sort_no
LAW_SECTOR_RANGE_MAP = {
    "深交所主板": "03",
    "深交所创业板": "05",
    "上交所主板": "06",
    "上交所科创板": "09",
    "北交所": "67",
    "其他": "19",
}

PG_SECTOR_NAME_MAP = {
    "深交所创业板": "创业板",
    "上交所科创板": "科创板",
    "深交所主板": "深主板",
    "上交所主板": "沪主板",
    "北交所": "北交所",
}


async def query_laws_vector(keywords: list, config: RunnableConfig = None) -> list:
    """
    根据传入的参数查询法规向量库
    :param keywords 关键词
    :param config 当前用户配置
    :return: 查询结果
    """
    client = MilvusClient(milvus_host)
    reranker = ReRankService(rerank_service_host)
    result = []
    # 拼接其他从向量库中检索出的法规
    arg = ' '.join(keywords)  # 关键词拼接
    expr = (f'law_status_name == "现行有效"'
            # 745233697178926977 收购与权益变动
            f' and ARRAY_CONTAINS_ANY(declare_type , ["745233697178926983"])'  # 回购
            f' and type_name in ["法律","司法解释", "部门规章","规范性文件","业务规则及办法","业务指引"]'
            f' or laws_name LIKE "上市公司收购管理办法%"')
    try:
        # 搜索向量
        response = client.search_vector(
            collection_name='laws_collection_demo',
            vectors=arg,
            top_k=50,
            out_fields=['parent_id', 'laws_id', 'laws_name', 'title_id', 'title', 'published'],
            expr=expr
        )
        if response and response.get('result'):
            res_result = response.get('result')[0]
            # 取出 res_result 中的 title
            documents = [item.get('title') for item in res_result]
            # 重排序
            ranked_documents = await reranker.rerank(arg, documents)
            # 取出排名前 5 的 index,然后根据 index 取出 res_result 中的 item
            if ranked_documents:
                ranked_documents = ranked_documents[:5]
                res_result = [res_result[item.get('index')] for item in ranked_documents]
                seen = set()  # 返回结果去重用
                for item in res_result:
                    # 返回结果去重
                    if item.get('title_id') in seen:
                        continue
                    seen.add(item.get('title_id'))
                    item_dict = {
                        'laws_id': item.get("laws_id"),
                        'parent_id': item.get("parent_id"),
                        'title_id': item.get("title_id"),
                        "法规名称": item.get("laws_name"),
                        "发布日期": item.get("published"),
                        "内容": item.get("title")
                    }

                    result.append(item_dict)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"向量搜索出错：{str(e)}")
    return ['未查询到相关法规'] if not result else result


async def http_laws_api(data: dict) -> list:
    """
    异步发送HTTP请求获取法规数据
    """
    access_token = get_access_token(TOKEN_REDIS_KEY_NAME)
    headers = {
        "authorization": access_token,
        "Content-Type": "application/json;charset=UTF-8"
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                    url="https://services.easy-board.com.cn/laws/lawsCommonInit/searchLawsDataNew",
                    headers=headers,
                    json=data
            ) as response:
                if response.status == 200:
                    res_result = await response.json()
                    if res_result and res_result.get('result', {}) and 'lawDataList' in res_result.get('result', {}):
                        return res_result['result']['lawDataList']
                    else:
                        logger.info(f"http_laws_api_res_result======{res_result}")
                        return []
                elif response.status == 401:
                    # token过期，清除缓存重新获取
                    remove_redis_info(TOKEN_REDIS_KEY_NAME)
                    headers['authorization'] = get_access_token(TOKEN_REDIS_KEY_NAME)
                    # 重试请求
                    async with session.post(
                            url="https://services.easy-board.com.cn/laws/lawsCommonInit/searchLawsDataNew",
                            headers=headers,
                            json=data
                    ) as retry_response:
                        if retry_response.status == 200:
                            retry_result = await retry_response.json()
                            if retry_result and retry_result.get('result', {}) \
                                    and 'lawDataList' in retry_result.get('result', {}):
                                return retry_result['result']['lawDataList']
                        return []
                else:
                    raise Exception(f"Error occurred while making HTTP request: {response.status}")
    except Exception as e:
        traceback.print_exc()
        logger.error(f"HTTP请求出错：{str(e)}")
        raise e


async def query_laws_api(keywords: list, config: RunnableConfig = None) -> list:
    """
    根据传入的参数查询法规接口
    :param keywords 关键词
    :param config 当前用户配置
    :return: 查询结果
    """
    logger.info(f'query_laws_api[args: {list(keywords)}]')
    law_range_arr = ["03,05", "06,09", "67"]
    reranker = ReRankService(rerank_service_host)
    result = []
    for law_range in law_range_arr:
        params = {
            "condition": {
                "lawsApplyModule": "1,6",
                "hasLawsApplyModule": "1,4,7,15,16,2,3,9,18,11,6",
                "keyAll": ' '.join(keywords),
                "lawRange": law_range + ",08",
                "invalidTag": "0,1,",
                "tabShow": "1",
                "treeClick": "submit",
                "nearSearch": "0",
                "lawRangeTemp": law_range,
                "lawType": "745233697178926983",  # 回购
            },
            "start": 1,
            "pageSize": 20,
            "orderByName": "2",
            "orderByOrder": "1",
            "startRow": 0
        }
        try:
            res_part = []
            response = await http_laws_api(params)
            if response:
                for item in response:
                    if item.get('content'):
                        # content html 提取
                        try:
                            content_text = BeautifulSoup(item.get('content'), 'lxml').get_text()
                        except Exception as e:
                            traceback.print_exc()
                            logger.error(f"BeautifulSoup content：{str(e)}")
                            content_text = item.get('content')
                        # title html 提取
                        try:
                            title_text = BeautifulSoup(item.get('title'), 'lxml').get_text()
                        except Exception as e:
                            traceback.print_exc()
                            logger.error(f"BeautifulSoup title：{str(e)}")
                            title_text = item.get('title')

                        # 提取所有文字内容
                        if content_text:
                            res_item = {
                                'laws_id': item.get("id"),
                                "法规名称": title_text,
                                "法规内容": content_text.replace('\u00A0', ' '),
                                "发布日期": item.get("publishDate")
                            }
                            res_part.append(res_item)

            # 取出排序字段
            documents = [item.get('法规内容') for item in res_part]
            # 重排序
            ranked_documents = await reranker.rerank(' '.join(keywords), documents)
            if ranked_documents:
                ranked_documents = ranked_documents[:5]
                res_part = [res_part[item.get('index')] for item in ranked_documents]
            result.extend(res_part)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"法规API查询出错：{str(e)}")
    return ['未查询到相关法规'] if not result else result


async def query_process_guide(sector: str, config: RunnableConfig = None) -> str:
    """
    根据提供的板块获取回购相关的流程指引
    :param sector: 法规所属的板块，单选，可选值：创业板,深主板,沪主板,科创板,北交所
    :param config
    :return: 查询出的对应板块的回购流程指引
    """
    if not sector or sector == '深主板':
        curr_com_info = config.get('configurable', {}).get('curr_com_info', {})
        belongs_plate_name = curr_com_info.get('belongs_plate_name')
        sector = PG_SECTOR_NAME_MAP.get(belongs_plate_name)
    if not sector in PG_SECTOR_NAME_MAP.values():
        raise Exception(f"板块{sector}不在可选范围内")

    try:
        # 从json文件中获取板块对应的流程指引
        with open('data/process_guide.json', 'r', encoding='utf-8') as file:
            process_guide = json.load(file)
            sector_process_guide = process_guide.get(sector)

        if sector_process_guide:
            if isinstance(sector_process_guide, dict):
                # 北交所只取竞价回购
                sector_process_guide = sector_process_guide['竞价回购']
            return json.dumps(sector_process_guide, ensure_ascii=False)
        return ''
    except Exception as e:
        traceback.print_exc()
        logger.error(f"流程指引查询出错：{str(e)}")
        return f"流程指引查询过程中发生错误：{str(e)}"


class RepTweetsInfo(BaseModel):
    推文ID: Optional[str] = Field(None, alias='id')
    推文标题: Optional[str] = Field(None, alias='tweets_title')
    推文概要: Optional[str] = Field(None, alias='summary')
    推文日期: Optional[str] = Field(None, alias='tweets_date')
    推文地址: Optional[str] = Field(None, alias='tweets_url')
    推文内容: Optional[str] = Field(None, alias='content')

    model_config = {
        "populate_by_name": True
    }


async def query_tweets_vector(keywords: list) -> list:
    """
    根据传入的参数查询推文向量库
    :param keywords 关键词
    :return: 查询结果
    """
    logger.info(f'query_tweets_vector[args: {list(keywords)}]')
    client = MilvusClient(milvus_host)
    reranker = ReRankService(rerank_service_host)
    result = []

    try:
        # 拼接其他从向量库中检索出的推文
        arg = ' '.join(keywords)  # NOTE 关键词拼接
        expr = 'status == "1"'
        # 搜索向量
        response = client.search_vector(
            collection_name='rep_tweets_knowledge_base',
            vectors=arg,
            top_k=10,
            out_fields=['id', 'tweets_title', 'summary', 'tweets_date', 'tweets_url', 'content'],
            expr=expr
        )

        if response and response.get('result'):
            res_result = response.get('result')[0]

            # 取出排序字段
            documents = [item.get('summary') for item in res_result]

            # 重排序
            ranked_documents = await reranker.rerank(arg, documents)

            # 取出排名前 5 的 index,然后根据 index 取出 res_result 中的 item
            if ranked_documents:
                ranked_documents = ranked_documents[:5]
                res_result = [res_result[item.get('index')] for item in ranked_documents]

            for item in res_result:
                result.append(RepTweetsInfo(**item).dict())
    except Exception as e:
        traceback.print_exc()
        logger.error(f"推文向量搜索出错：{str(e)}")

    return result if result else ['未查询到相关推文']


async def query_laws_kw_vector(keywords: list, config: RunnableConfig = None) -> list:
    """
    根据传入的参数查询法规关键词向量库
    :param keywords 关键词
    :param config 当前用户配置
    :return: 查询结果
    """
    law_range_arr = ["03,05", "06,09", "67"]
    client = MilvusClient(milvus_host)
    reranker = ReRankService(rerank_service_host)
    result = []
    # 关键词排序
    keywords = sorted(keywords)
    # 每个板块都查一遍最后获取各自最符合的几条
    for law_range in law_range_arr:
        # 拼接其他从向量库中检索出的法规
        arg = ' '.join(keywords)  # 关键词拼接
        expr = f"ARRAY_CONTAINS_ANY(law_range, {str(law_range.split(','))})"
        try:
            # 搜索向量
            response = client.search_vector(
                collection_name='rep_laws_keywords_base',
                vectors=arg,
                top_k=50,
                out_fields=['parent_id', 'laws_id', 'laws_name', 'title_id', 'title', 'published'],
                expr=expr
            )
            if response and response.get('result'):
                res_result = response.get('result')[0]
                # 取出 res_result 中的 title
                documents = [item.get('title') for item in res_result]
                # 重排序
                ranked_documents = await reranker.rerank(arg, documents)
                # 取出排名前 5 的 index,然后根据 index 取出 res_result 中的 item
                if ranked_documents:
                    ranked_documents = ranked_documents[:5]
                    res_result = [res_result[item.get('index')] for item in ranked_documents]
                    seen = set()  # 返回结果去重用
                    for item in res_result:
                        # 返回结果去重
                        if item.get('title_id') in seen:
                            continue
                        seen.add(item.get('title_id'))
                        item_dict = {
                            'laws_id': item.get("laws_id"),
                            'parent_id': item.get("parent_id"),
                            'title_id': item.get("title_id"),
                            "法规名称": item.get("laws_name"),
                            "发布日期": item.get("published"),
                            "内容": item.get("title")
                        }

                        result.append(item_dict)
        except Exception as e:
            traceback.print_exc()
            logger.error(f"向量搜索出错：{str(e)}")
    return ['未查询到相关法规'] if not result else result


async def http_disclosure_points_api(data: dict) -> list:
    """
    异步发送HTTP请求获取法规数据
    """
    access_token = get_access_token(TOKEN_REDIS_KEY_NAME)
    headers = {
        "authorization": access_token,
        "Content-Type": "application/json;charset=UTF-8"
    }

    try:
        async with (aiohttp.ClientSession() as session):
            async with session.post(
                    url="https://services.easy-board.com.cn/declare/microDeclare/getHelperData",
                    headers=headers,
                    json=data
            ) as response:
                if response.status == 200:
                    res_result_str = await response.json()
                    res_result_str = decrypt_data(res_result_str)
                    res_result = json.loads(res_result_str)

                    if res_result and res_result.get('result', {}) and 'ponder' in res_result.get('result', {}):
                        return res_result['result']['ponder']
                    else:
                        logger.info(f"http_disclosure_points_api_res_result======{res_result}")
                        return []
                elif response.status == 401:
                    # token过期，清除缓存重新获取
                    remove_redis_info(TOKEN_REDIS_KEY_NAME)
                    headers['authorization'] = get_access_token(TOKEN_REDIS_KEY_NAME)
                    # 重试请求
                    async with session.post(
                            url="https://services.easy-board.com.cn/declare/microDeclare/getHelperData",
                            headers=headers,
                            json=data
                    ) as retry_response:
                        if retry_response.status == 200:
                            retry_result = await retry_response.json()
                            if retry_result and retry_result.get('result', {}) \
                                    and 'ponder' in retry_result.get('result', {}):
                                return retry_result['result']['ponder']
                        return []
                else:
                    raise Exception(f"Error occurred while making HTTP request: {response.status}")
    except Exception as e:
        traceback.print_exc()
        logger.error(f"HTTP请求出错：{str(e)}")
        raise e


async def query_disclosure_points_api(type_list: list[str], config: RunnableConfig = None) -> list:
    """
    根据传入的参数查询信披智库披露要点的接口
    :param type_list 查询的信披文件类型
    :param config 当前用户配置
    :return: 查询结果
    """
    dp_type_list = [
        # 创业板
        ("创业板", "746412002819322241", "回购股份提议"),
        ("创业板", "96210244698733800", "回购股份方案"),
        ("创业板", "96210244698733795", "回购股份报告书"),
        ("创业板", "96210244698733801", "回购股份进展公告"),
        ("创业板", "746412002819323780", "回购结果暨股份变动公告"),
        ("创业板", "96210244698733794", "回购股份注销完成暨股份变动公告"),
        ("创业板", "746412002819324420", "以集中竞价方式减持已回购股份的预披露公告"),
        ("创业板", "746412002819324927", "以集中竞价方式减持已回购股份的进展公告"),
        ("创业板", "746412002819325029", "已回购股份减持结果暨股份变动公告"),
        ("创业板", "746412002819325096", "回购股份方案变更或终止公告"),
        ("创业板", "746412002819325160", "已回购股份处理完成公告"),
        ("创业板", "746412002819325127", "回购股份业务其他公告"),
        # 科创板
        ("科创板", "746761292299372081", "回购方案"),
        ("科创板", "746761292299372088", "前10名公众股东名册"),
        ("科创板", "746761292299372108", "回购报告书"),
        ("科创板", "746761292299372137", "回购实施进展"),
        ("科创板", "746761292299372148", "通知债权人"),
        ("科创板", "746761292299372155", "期间过半仍未实施回购"),
        ("科创板", "746761292299372181", "回购实施结果暨股份变动（有股份注销）"),
        ("科创板", "746761292299372185", "回购实施结果暨股份变动（无股份注销）"),
        ("科创板", "746761292299372213", "已回购股份注销（回购完成后3年持有期内的注销）"),
        # 深主板
        ("深主板", "746412002820261767", "回购股份提议"),
        ("深主板", "96210244698733257", "回购股份方案"),
        ("深主板", "96210244698733252", "回购股份报告书"),
        ("深主板", "96210244698733253", "回购股份进展公告"),
        ("深主板", "96210244698733254", "回购结果暨股份变动公告"),
        ("深主板", "96210244698733251", "回购股份注销完成暨股份变动公告"),
        ("深主板", "96210244698733256", "以集中竞价方式减持已回购股份的预披露公告"),
        ("深主板", "746412002820261909", "以集中竞价方式减持已回购股份的进展公告"),
        ("深主板", "746412002820261954", "已回购股份减持结果暨股份变动公告"),
        ("深主板", "746412002820261956", "回购股份方案变更或终止公告"),
        ("深主板", "746412002820261958", "已回购股份处理完成公告"),
        ("深主板", "746412002820261960", "回购股份业务其他公告"),
        # 沪主板
        ("沪主板", "24181902543262314", "回购方案"),
        ("沪主板", "24181902543262316", "前10名公众股东名册"),
        ("沪主板", "24181902543262320", "回购报告书"),
        ("沪主板", "24181902543262322", "回购实施进展"),
        ("沪主板", "24181902543262324", "通知债权人"),
        ("沪主板", "24181902543262326", "期间过半仍未实施回购"),
        ("沪主板", "24181902543262328", "回购实施结果暨股份变动（有股份注销）"),
        ("沪主板", "746761292300315928", "回购实施结果暨股份变动（无股份注销）"),
        ("沪主板", "746761292300315929", "已回购股份注销（回购完成后3年持有期内的注销）"),
        # 北交所，注销的都是当时页面上没有数据的，以后有数据再说
        ("北交所", "448629827286745618", "要约回购股份方案"),
        # ("北交所", "15448543970798912372", "竞价回购股份方案"),
        # ("北交所", "94219074211635063", "定向回购股份方案"),
        # ("北交所", "448629827286745815", "通知债权人的情况公告"),
        # ("北交所", "448629827286745908", "开始接受要约回购申报的提示性公告"),
        # ("北交所", "448629827286745950", "回购进展情况公告"),
        ("北交所", "448629827286745990", "回购股份结果公告"),
        # ("北交所", "448629827286746028", "回购股份/库存股注销完成暨股份变动公告"),
        # ("北交所", "448629827286746060", "回购价格、回购数量的调整公告"),
        # ("北交所", "448629827286746101", "变更或终止回购股份方案的公告"),
        # ("北交所", "5071795525038660671", "前十大股东和前十大无限售条件股东情况公告"),
        # ("北交所", "5071795525038663783", "减持回购股份的预披露公告"),
        # ("北交所", "5071795525038666648", "减持回购股份的进展情况公告"),
        # ("北交所", "5071795525038668827", "减持回购股份结果的公告"),
        # ("北交所", "15448543970803907437", "提议回购公司股份公告"),
        # ("北交所", "15448543970803910875", "上市公司回购股份报告书"),
        # ("北交所", "15448543970803915505", "出售已回购股份计划的公告"),
        # ("北交所", "15448543970803920839", "出售已回购股份进展的公告"),
        # ("北交所", "15448543970803925401", "出售已回购股份结果的公告"),
        # ("北交所", "448629827286746119", "其他关于股份回购的公告"),
    ]
    # 获取所有需要发送请求的事项编号
    filter_type_list = [i for i in dp_type_list if i[2] in type_list]
    # 发送请求
    result = []
    for filter_type in filter_type_list:
        params = {"typeId": filter_type[1], "typeName": filter_type[2]}
        try:
            response = await http_disclosure_points_api(params)
            if response:
                result.append((filter_type[2], filter_type[0], response))
        except Exception as e:
            traceback.print_exc()
            logger.error(f"API查询出错：{str(e)}")
    return result


if __name__ == '__main__':
    res = asyncio.run(query_laws_vector(['回购用于维护公司价值及股东权益，董监高可以减持嘛']))
    print(res)
