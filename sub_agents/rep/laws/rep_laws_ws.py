import json

from langchain_core.runnables import RunnableConfig

from sub_agents.rep.enum.ws_msg_enum import ToolsType
from sub_agents.rep.utils.web_utils import send_websocket_msg


async def __send_laws_result(result_vector, result_api, result_twts, keywords, config: RunnableConfig):
    """
    把法规结果用ws消息发送出去
    """
    v_send = []
    if result_vector and (not result_vector[0] == '未查询到相关法规'):
        for item in result_vector:
            if item and item.get('laws_id'):
                v_send.append({
                    'lawsId': item.get('laws_id'),
                    'parentId': item.get('parent_id'),
                    'titleId': item.get('title_id'),
                })
    a_send = []
    if result_api and (not result_api[0] == '未查询到相关法规'):
        for item in result_api:
            if item and item.get('laws_id'):
                a_send.append({
                    'lawsId': item.get('laws_id'),
                    'content': item.get('法规内容'),
                    'keywords': ' '.join(keywords)
                })
    v_send.extend(a_send)
    if v_send:
        await send_websocket_msg(json.dumps({ToolsType.LAWS.value: v_send}, ensure_ascii=False), config)

    t_send = []
    if result_twts and (not result_twts[0] == '未查询到相关推文'):
        for item in result_twts:
            if item and item.get('推文标题'):
                t_send.append({
                    'title': item.get('推文标题'),
                    'url': item.get('推文地址'),
                })
    if t_send:
        await send_websocket_msg(json.dumps({ToolsType.TWTS.value: t_send}, ensure_ascii=False), config)


async def __send_dp_result(result, config: RunnableConfig):
    """
    把法规结果用ws消息发送出去
    """
    if result:
        result_send = [{"title": item[0], "stock": item[1], "contentList": item[2]} for item in result]
        await send_websocket_msg(json.dumps({ToolsType.DISC_KEYS.value: result_send}, ensure_ascii=False), config)
