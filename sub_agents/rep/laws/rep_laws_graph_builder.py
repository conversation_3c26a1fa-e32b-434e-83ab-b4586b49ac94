import json

from langchain_core.runnables import RunnableConfig, Runnable
from langgraph.constants import START, END
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode

from sub_agents.rep.enum.rep_enum import RepWSParam
from sub_agents.rep.general.rep_biz_state_def import RepLawsAgentState
from sub_agents.rep.laws.rep_laws_agent import rep_laws_agent_runnable
from sub_agents.rep.laws.rep_laws_tools import rep_laws_tool_list
from sub_agents.rep.utils.web_utils import send_websocket_msg


class RepLawsAgent:
    def __init__(self, runnable: Runnable, agent_name: str):
        self.runnable = runnable
        self.agent_name = agent_name

    async def __call__(self, _state: RepLawsAgentState, config: RunnableConfig):
        await send_websocket_msg(json.dumps({RepWSParam.FLOW_PROCESS.value: "法规助手正在检索"},
                                            ensure_ascii=False), config)
        while True:
            # 使用runnable来决定应该使用哪个agent
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["rep_laws_agent_messages"].append(("user", "请返回一个实际的文本输出。"))
            else:
                break
        return {"rep_laws_agent_messages": result}


tool_node = ToolNode(rep_laws_tool_list, messages_key='rep_laws_agent_messages')

builder = StateGraph(RepLawsAgentState)
builder.add_node("rep_laws_agent", RepLawsAgent(rep_laws_agent_runnable, "rep_laws_agent"))
builder.add_node("rep_laws_tools", tool_node)

builder.add_edge(START, "rep_laws_agent")
builder.add_edge("rep_laws_agent", "rep_laws_tools")
builder.add_edge("rep_laws_tools", END)

rep_laws_graph = builder.compile()

# rep_laws_graph.get_graph(xray=True).draw_mermaid_png(
#     output_file_path='sub_agents/rep/laws/rep_laws_graph.builder.png')
