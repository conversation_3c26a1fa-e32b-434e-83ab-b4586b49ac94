import json
import os
import traceback

from openai import OpenAI

from utils.load_env import logger

logger.info("")
from utils.milvus_tools import MilvusClient, milvus_host

LLM_HUB = os.getenv('MODEL_HUB_HOST')
ai_client = OpenAI(api_key="sk-7XYtE83ltdpER8T16f963859333c47728e86F0C8F9105a74",
                   base_url=LLM_HUB)

client = MilvusClient(milvus_host)

PROMPT_CONTENT = f"""
                    <instruction>
                        根据以下步骤完成任务：
                        1. 阅读用户提供的法规内容。
                        2. 根据回购方面的知识，从内容中提取若干关键词及可能根据这个法规提问的若干个问题。
                        3. 确保在选择关键词及问题时，把内容中提及的操作行为、最终目的、处理角色及时间节点等作为重要提取目标。
                        4. 输出关键词以及问题的JSON数据，关键词之间用逗号分隔。
                        5. 关键词中禁止出现 "上市公司","回购"
                        <example>
                            <input>
                                用户提供的内容: "上市公司以现金为对价，采用要约方式、集中竞价方式回购股份的，视同上市公司现金分红，纳入现金分红的相关比例计算。"
                            </input>
                            <output>
                                {{
                                    "keywords": ['现金','对价','要约','集中竞价','分红'"],
                                    "question_list": ['上市公司回购时通过什么方式进行回购可以进行分红']
                                }}
                                
                            </output>
                        </example>
                    </instruction>
                  """


def query_laws_from_vector():
    # with open('sub_agents/rep/laws/data/laws_id_list.json', 'r', encoding='utf-8') as file:
    #     laws_id_list = json.load(file)
    laws_id_list = ["15448543969582100636", "15448543969582114710", "15448543969582329854", "15448543969634358061"]
    for laws_id in laws_id_list:
        with open(f'sub_agents/rep/laws/data/content/laws_{laws_id}.json', 'r', encoding='utf-8') as file:
            res_result = json.load(file)
        seen = set()  # 返回结果去重用
        result = []
        for item in res_result:
            if item.get('title_id') in seen:
                continue
            seen.add(item.get('title_id'))
            result_item = {}
            result_item.update(item)
            try:
                print(f"\n提取内容：{item.get('title')}")
                ai_response = ai_client.chat.completions.create(
                    model='4o',
                    messages=[{'role': 'system', 'content': PROMPT_CONTENT},
                              {'role': 'user', 'content': item.get('title')}],
                    temperature=0,
                    stream=False,
                    response_format={"type": "json_object"}
                )
                print(f"\n提取结果：{ai_response.choices[0].message.content}")
                result_item.update(json.loads(ai_response.choices[0].message.content))
            except Exception as e:
                traceback.print_exc()
                logger.error(f"提取报错：{str(e)}")
                result_item['keywords'] = f"提取报错：{str(e)}"
            result.append(result_item)
        json.dump(result,
                  open(f'sub_agents/rep/laws/data/words/words_{laws_id}.json', 'w+', encoding='utf-8'),
                  indent=2,
                  ensure_ascii=False)


def save_laws_file():
    with open('sub_agents/rep/laws/data/laws_id_list.json', 'r', encoding='utf-8') as file:
        laws_id_list = json.load(file)
        for laws_id in laws_id_list:
            expr = f'laws_id == "{laws_id}"'
            # 搜索向量
            response = client.query_vector(
                collection_name='laws_collection_demo',
                limit=16384,
                out_fields=['parent_id', 'laws_id', 'laws_name', 'chapter_name', 'section_name', 'item_name',
                            'title_id', 'title', 'published', 'law_range'],
                expr=expr
            )
            seen = set()  # 返回结果去重用
            result = []
            if response and response.get('result'):
                res_result = response.get('result')
                for item in res_result:
                    if item.get('title_id') in seen:
                        continue
                    seen.add(item.get('title_id'))
                    result.append(item)
                json.dump(res_result,
                          open(f'sub_agents/rep/laws/data/content/laws_{laws_id}.json', 'w+', encoding='utf-8'),
                          indent=2,
                          ensure_ascii=False)


def query_laws_id():
    page_size = 100
    current_page = 0
    expr = (f'law_status_name == "现行有效"'
            f' and ARRAY_CONTAINS_ANY(declare_type , ["745233697178926983"])'
            f' and type_name in ["法律","司法解释", "部门规章","规范性文件","业务规则及办法","业务指引"]'
            f' or laws_name LIKE "上市公司收购管理办法%"')
    id_set = set()
    while True:
        response = client.query_vector(
            collection_name='laws_collection_demo',
            limit=page_size,
            offset=current_page * page_size,
            out_fields=['laws_id'],
            expr=expr
        )
        if response and response.get('result'):
            res_result = response.get('result')
            for item in res_result:
                id_set.add(item.get('laws_id'))
            current_page += 1
        else:
            break
    result = list(id_set)
    json.dump(result,
              open(f'sub_agents/rep/laws/data/laws_id_list.json', 'w+', encoding='utf-8'),
              indent=2,
              ensure_ascii=False)


def get_keywords_set():
    laws_id_list = ["15448543969582100636", "15448543969582114710", "15448543969582329854", "15448543969634358061"]
    keywords = set()
    for laws_id in laws_id_list:
        with open(f'sub_agents/rep/laws/data/words/words_{laws_id}_p.json', 'r', encoding='utf-8') as file:
            laws_info = json.load(file)
            for laws in laws_info:
                keywords = keywords.union(set(laws['keywords']))
    keywords = sorted(list(keywords))
    print(json.dumps(keywords, ensure_ascii=False))


if __name__ == '__main__':
    # query_laws_id()
    # save_laws_file()
    # query_laws_from_vector()
    get_keywords_set()
    pass
