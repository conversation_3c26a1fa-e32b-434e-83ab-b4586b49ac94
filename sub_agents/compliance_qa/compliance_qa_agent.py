import os
import traceback
from datetime import datetime
from typing import Dict

from langchain_core.messages import HumanMessage, RemoveMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, MessagesState
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command

from sub_agents.compliance_qa.compliance_qa_constants import FINISH_TAG
from sub_agents.compliance_qa.compliance_qa_nodes import intent_recognition_assistant, tool_node1, assistant, \
    human_node, tool_node2, delete_messages
from sub_agents.compliance_qa.compliance_qa_tools import get_stock_num


def construct_graph():
    """Construct the graph."""
    builder = StateGraph(MessagesState)
    builder.add_node("intent_recognition_assistant", intent_recognition_assistant)
    builder.add_node("intent_tools", tool_node1)
    builder.add_node("assistant", assistant)
    builder.add_node("ask_human", human_node)
    builder.add_node("tools", tool_node2)
    builder.add_node("delete_messages", delete_messages)

    builder.set_entry_point("intent_recognition_assistant")
    builder.add_conditional_edges(
        "intent_recognition_assistant",
        should_continue_intent,
        {
            "assistant": "assistant",
            "intent_tools": "intent_tools"
        }
    )
    builder.add_edge("intent_tools", "intent_recognition_assistant")
    builder.add_conditional_edges(
        "assistant",
        should_continue,
        {
            "delete_messages": "delete_messages",
            "tools": "tools",
            "ask_human": "ask_human"
        }
    )
    builder.add_edge("tools", "assistant")
    builder.add_edge("ask_human", "assistant")
    builder.set_finish_point("delete_messages")

    checkpointer = MemorySaver()
    graph = builder.compile(checkpointer=checkpointer, interrupt_before=["ask_human"])

    # 输出图结构
    graph_path = 'sub_agents/compliance_qa/compliance_qa_agent_graph.builder.png'
    # if not os.path.exists(graph_path):
    #     graph.get_graph().draw_mermaid_png(output_file_path=graph_path)

    return graph


def should_continue_intent(state: MessagesState):
    messages = state["messages"]
    last_message = messages[-1]
    # 如果last_message中没有tool_calls属性，说明意图识别结束，返回DELETE_MESSAGES
    if not last_message.tool_calls:
        return "assistant"
    # 如果last_message.tool_calls不为空
    else:
        return "intent_tools"


def should_continue(state: MessagesState):
    messages = state["messages"]
    last_message = messages[-1]
    # 如果last_message中没有tool_calls属性，说明任务结束，返回DELETE_MESSAGES
    if not last_message.tool_calls:
        if FINISH_TAG in last_message.content:
            return "delete_messages"
        else:
            # 大模型有时在与用户确认信息时，会直接返回内容，而不是调用AskHuman工具，人工干预
            tool_call = {'args': {'question': last_message.content}, 'id': 'call_AIMessage',
                         'name': 'AskHuman', 'type': 'tool_call'}
            last_message.tool_calls.append(tool_call)
            return "ask_human"
    # 如果last_message.tool_calls不为空
    else:
        # 如果last_message.tool_calls的第一个元素的name属性为"AskHuman"
        if last_message.tool_calls[0]["name"] == "AskHuman":
            # 返回ASK_HUMAN
            return "ask_human"
        else:
            # 否则返回TOOLS
            return "tools"


def view_main(graph: CompiledStateGraph, configuration: Dict, _user_input: str, plate: str = '深主板') -> str:
    """执行图主入口
    """
    try:
        state = graph.get_state(configuration)
        if state.values == {} or not state.values["messages"]:
            history_messages = configuration['configurable']['history'][-4:]
            company_code = configuration['configurable']['company_code']
            _user_input = f'已知当前日期是{datetime.now().strftime("%Y-%m-%d")}，公司所属板块为：{plate}，公司总股本为{get_stock_num(company_code)}股。\n' + _user_input
            _user_input = {"messages": history_messages + [HumanMessage(_user_input)]}
            answer = graph.invoke(_user_input, config=configuration, debug=True)
        else:
            answer = graph.invoke(Command(resume=_user_input), config=configuration, debug=True)

        last_message = answer["messages"][-1]
        # 如果最后一个消息的工具调用名称为"AskHuman"
        if last_message.tool_calls and last_message.tool_calls[0]["name"] == "AskHuman":
            # 返回工具调用的参数中的"question"值
            return last_message.tool_calls[0]["args"]["question"]
        else:
            # 结束一次会话，保存上次会话历史，并清空本次历史对话
            clear_msg_ids = [message.id for message in answer["messages"]]
            graph.update_state(configuration, {"messages": [RemoveMessage(id=_id) for _id in clear_msg_ids]})
            last_message.content = last_message.content.replace(FINISH_TAG, '')
            return last_message.content
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{e}"