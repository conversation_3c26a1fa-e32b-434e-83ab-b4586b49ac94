import copy
import json
import time
import uuid

import gradio as gr
from langchain_core.messages import HumanMessage, AIMessage

from sub_agents.compliance_qa.compliance_qa_agent import construct_graph, view_main

# 全局变量，用于存储配置
configurations = {}
graph = construct_graph()


def persistent_history(gr_configurations):
    """持久化历史记录"""
    config = copy.deepcopy(gr_configurations)
    # 按时间戳排序并取前两条记录
    config = dict(
        sorted(config.items(), key=lambda item: item[1]['configurable']['created_timestamp'], reverse=True)[:2])

    # 转换历史记录为JSON格式
    for session_id, conf in config.items():
        _history = conf['configurable']['history']
        json_history = [
            {"role": "human", "content": hist.content} if isinstance(hist, HumanMessage) else
            {"role": "ai", "content": hist.content} if isinstance(hist, AIMessage) else None
            for hist in _history
        ]
        # 过滤掉None值
        config[session_id] = [item for item in json_history if item is not None]

    # 持久化到文件
    with open( "sub_agents/compliance_qa/history.json", "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=2)


def construct_history_messages(history):
    """构造历史消息"""
    history_messages = []
    for user_msg, ai_msg in history:
        history_messages.append(HumanMessage(user_msg))
        history_messages.append(AIMessage(ai_msg))
    return history_messages


def get_configuration(session_id):
    """获取配置
    """
    if configurations.get(session_id):
        config = configurations[session_id]
    else:
        config = {
            "configurable": {
                "history": [],
                "company_code": 999000,
                "created_timestamp": round(time.time()),
                "thread_id": session_id
            },
        }
        configurations[session_id] = config
    return config


def retry(history, plate, gr_state):
    """WebUI 重试函数
    """
    user_input, history = history[-1][0], history[:-1]
    config = get_configuration(gr_state.get('session_id'))
    response = view_main(graph, config, user_input, plate)
    history.append((user_input, response))
    config['configurable']['history'] = construct_history_messages(history)
    persistent_history(configurations)
    return history, ''


def chat(user_input, history, plate, gr_state):
    """WebUI 对话函数
    """
    config = get_configuration(gr_state.get('session_id'))
    response = view_main(graph, config, user_input, plate)
    history.append((user_input, response))
    config['configurable']['history'] = construct_history_messages(history)
    persistent_history(configurations)
    return history, ''


def update_session_id(gr_state):
    """更新会话ID"""
    # 如果state中没有session_id，则生成一个新的
    if not gr_state.get("session_id"):
        gr_state["session_id"] = str(uuid.uuid4())
        print(f'会话号：{gr_state["session_id"]}')
    return gr_state


with gr.Blocks() as demo:
    state = gr.State({})
    title = gr.Markdown("## 合规交易助手Demo")
    # 第一行：板块选择下拉菜单
    plate_dropdown = gr.Dropdown(label="板块选择", choices=["深主板", "沪主板", "科创板", "创业板", "北交所"],
                                 value='深主板')

    # 第二行：聊天窗口
    chatbot = gr.Chatbot(label="对话窗口")

    # 第三行：输入框
    textbox = gr.Textbox(placeholder="在这里输入你的消息...", label="输入框")

    # 第四行：按钮
    with gr.Row():
        retry_button = gr.Button("重试")
        clear_button = gr.Button("清空")

    demo.load(update_session_id, inputs=[state], outputs=[state])

    # 提交按钮的点击事件
    textbox.submit(chat, inputs=[textbox, chatbot, plate_dropdown, state], outputs=[chatbot, textbox])

    # 重试按钮的点击事件
    retry_button.click(retry, inputs=[chatbot, plate_dropdown, state], outputs=[chatbot, textbox])

    # 清空按钮的点击事件
    clear_button.click(lambda: [], outputs=[chatbot])


if __name__ == '__main__':
    demo.launch(server_name="0.0.0.0", server_port=7862, debug=True)