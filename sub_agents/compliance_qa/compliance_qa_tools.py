import json
from typing import List

import requests
from langchain_core.tools import tool

from sub_agents.compliance_qa.compliance_qa_constants import TRANSACTION_TYPE_WHITELIST

base_url = "https://services.easy-board.com.cn"


def get_tokens() -> str:
    """获取token"""
    res = requests.post(
        "https://services.valueonline.cn/oauth/token?grant_type=client_credentials&response_type=token&client_id=2c64f8c84a06d709&client_secret=eb5ffef52c64f8c84a06d709080d117d")
    if res.status_code == 200:
        return json.loads(bytes.decode(res.content)).get('access_token')


def get_stock_num(company_code):
    """基于公司代码获取公司总股数
    :param company_code: 公司代码
    """
    url = f"{base_url}/capital-cloud-api/continueSupervise/getCompanyRelaData"
    data = {
        "companyCodes": company_code
    }
    token = get_tokens()
    header = {'Content-type': 'application/json', 'Authorization': token}
    res = requests.post(url, json=data, headers=header)
    if res.status_code == 200:
        res_encode = bytes.decode(res.content, 'utf-8')
        res_decode = json.loads(res_encode)
        if isinstance(res_decode, dict):
            res_decode = res_decode.get('result')[0]
            stock_num = res_decode.get("equityScale") * 10000
            return stock_num
    return "-"


@tool
def get_provisions(plate, transaction_types) -> List:
    """根据公司所属板块，及用户问题涉及的交易类别，返回交易类别对应的法规条款
    :param plate: 所属板块，不得为None
    :param transaction_types: 用户问题涉及的交易类别列表
    :return: 交易类别对应的法规条款
    """
    keep_keys = {
        'lawsTitle': "法规名称",
        'lawsContent': "条款内容",
        'lawsItemTitle': "条款章节"
    }

    token = get_tokens()
    # TODO：后续对以下代码进行优化------begin------
    # 根据transaction的值，将transaction_types中的元素替换为相应的字符串
    transaction_types = [
        "短线交易系统计算结果" if transaction == "短线交易" else
        "董监高禁止敏感期交易" if transaction == "敏感期交易" else
        transaction
        for transaction in transaction_types
    ]
    # TODO：后续对以下代码进行优化------end------

    data = {
        "plate": plate,
        "transaction_types": ','.join(transaction_types)
    }
    header = {'Content-type': 'application/json', 'Authorization': token}
    res = requests.post(f"{base_url}/chatDongWeb/getAgentApi/get_provisions", json=data, headers=header)
    if res.status_code == 200:
        res_encode = bytes.decode(res.content, 'utf-8')
        res_decode = json.loads(res_encode)
        if isinstance(res_decode, dict):
            res_decode = res_decode.get('result')
            # 将返回的结果，处理成“法规名称”、“条款章节”和“条款内容”格式
            for key, val in res_decode.items():
                # 遍历val列表中的每个元素
                for _val in val:
                    # 创建一个新的字典，只包含keep_keys中的键值对
                    filtered_dict = dict((keep_keys[k], _val[k]) for k in keep_keys.keys() if k in _val)
                    # 清空_val字典
                    _val.clear()
                    # 更新_val字典，只包含filtered_dict中的键值对
                    _val.update(filtered_dict)
            return res_decode
    return []


@tool
def get_transaction_types(stock_ratio, identity_type, holder_type, transaction_direction, plate,
                          before_public_limit_sale=0) -> List:
    """基于给定的身份信息，确定可以选择的交易类别
    :param stock_ratio: 用户当前持股比例，需要将用户输入比例转换为["5%以下", "5%~30%", "30%以上"，None]中的一个
    :param identity_type: 任职类型，“董监高”、“董监高的一致行动人”、“其他”或None
    :param holder_type: 股东类型：“控股股东或实际控制人”、“控股股东或实际控制人的一致行动人”、“其他”或None中的一个
    :param transaction_direction: 买卖方向：“买入股票”、“卖出股票”或None中的一个
    :param before_public_limit_sale: 是否在首次公开发行前持有公司股份, 1表示是，0表示否，默认为0
    :param plate: 公司所属板块
    :return: 可以选择的交易类别列表
    """
    token = get_tokens()

    # 参数预处理
    stock_ratio = stock_ratio if not stock_ratio else stock_ratio.replace('～', '~')

    identity_type = identity_type if not identity_type or identity_type != '其他' else "非以上人员"

    holder_type = holder_type if not holder_type or holder_type != '其他' else "非以上人员"

    data = {
        "stock_ratio": stock_ratio,
        "identity_type": identity_type,
        "holder_type": holder_type,
        "transaction_direction": transaction_direction,
        "plate": plate,
        "beforepublic_limit_sale": before_public_limit_sale
    }

    header = {'Content-type': 'application/json', 'Authorization': token}
    res = requests.post(f"{base_url}/chatDongWeb/getAgentApi/get_transaction_types", json=data, headers=header)
    if res.status_code == 200:
        res_encode = bytes.decode(res.content, 'utf-8')
        res_decode = json.loads(res_encode)
        if isinstance(res_decode, dict):
            res_decode = res_decode.get('result')
            # TODO：后续对以下代码进行优化------begin------
            # 遍历res_decode列表中的每个transaction
            res_decode = [
                # 如果transaction是"短线交易系统计算结果"或"短线交易其它情形"，则将其替换为"短线交易"
                "短线交易" if transaction in ["短线交易系统计算结果", "短线交易其它情形"] else
                # 如果transaction是"董监高禁止敏感期交易"，则将其替换为"敏感期交易"
                "敏感期交易" if transaction == "董监高禁止敏感期交易" else
                # 否则，将transaction保持不变
                transaction
                for transaction in res_decode
            ]
            # TODO：后续对以下代码进行优化------end------
            return list(set(res_decode))
    return []


@tool
def intent_validate(problem_type, transaction_types = None) -> (bool, str):
    """
    意图识别校验
    :param problem_type: 问题类型为["法规解答","查询交易限制"]中的一个
    :param transaction_types: 交易类型为["上市时未盈利控股股东减持限制规定","上市时未盈利董监高及核心技术人员减持限制规定","不可减持的其他情形","内幕交易防控","减持解除限售存量股份","减持预披露要求","协议转让减持要求","变动1%披露要求","变动5%披露与暂停交易","大宗交易减持要求","大股东不可减持的其他情形","大股东集中竞价交易减持要求","就限制股份转让作出的承诺","拥有权益的股份每达到5％整数倍时披露与暂停交易","控股股东、实际控制人在窗口期中不得出售解除限售的股份","控股股东禁止敏感期交易","特定股东集中竞价交易减持要求","短线交易其它情形","短线交易系统计算结果","董监高不可减持的其他情形","董监高持股转让限制","董监高禁止敏感期交易","要约收购","超过30%自由增持2%以内","重大资产重组认购股份限售期要求","集中竞价交易减持时间区间内事中披露要求","集中竞价交易减持时间区间内事中披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","集中竞价交易减持预披露要求","集中竞价交易减持预披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","集中竞价交易系统减持完毕披露要求","集中竞价交易系统减持完毕披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","首次达10%披露与暂停交易","首次达到5%披露与暂停交易","首次达到5%披露与暂停要求"]中的一个或多个或None。
    :return: 校验结果 (bool, str)
    """

    # 问题类型校验
    valid_types = ["法规解答", "查询交易限制"]
    if problem_type not in valid_types:
        return False, f"problem_type字段必须为{valid_types}之一"

    # 交易类型校验
    if problem_type == "法规解答":
        if not transaction_types:
            return False, "transaction_types不能为空列表"

        invalid_types = []
        for t in transaction_types:
            if t not in TRANSACTION_TYPE_WHITELIST:
                invalid_types.append(t)
        if invalid_types:
            return False, f"发现无效的交易类型: {invalid_types}"

    return True, "意图识别校验通过"
