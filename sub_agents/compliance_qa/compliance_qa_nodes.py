from langchain_core.messages import SystemMessage, RemoveMessage
from langgraph.graph import MessagesState
from langgraph.prebuilt import ToolNode
from langgraph.types import interrupt
from pydantic import BaseModel

from sub_agents.compliance_qa.compliance_qa_constants import FINISH_TAG
from sub_agents.compliance_qa.compliance_qa_tools import intent_validate, get_provisions, get_transaction_types
from utils.llm_utils import get_a_llm

tools1 = [intent_validate]
model_with_tools_deepseek = get_a_llm(llm_type="deepseek-chat").bind_tools(tools1)


def intent_recognition_assistant(state: MessagesState):
    """意图识别响应节点。"""

    _system_prompt = f"""
## 定位
证券交易法规咨询助手，专注判断用户提问类型并精准匹配预定义交易限制条款。

## 能力
1. 问题分类：识别问题属于[法规解答]/[查询交易限制]
2. 交易类型提取：仅当问题可法规解答时，从预定义34类交易类型中准确匹配
3. 意图校验：调用intent_validate工具校验问题分类和交易类型的准确性

## 知识储备
**预定义交易类型列表**：
["上市时未盈利控股股东减持限制规定","上市时未盈利董监高及核心技术人员减持限制规定","不可减持的其他情形","内幕交易防控","减持解除限售存量股份","减持预披露要求","协议转让减持要求","变动1%披露要求","变动5%披露与暂停交易","大宗交易减持要求","大股东不可减持的其他情形","大股东集中竞价交易减持要求","就限制股份转让作出的承诺","拥有权益的股份每达到5％整数倍时披露与暂停交易","控股股东、实际控制人在窗口期中不得出售解除限售的股份","控股股东禁止敏感期交易","特定股东集中竞价交易减持要求","短线交易其它情形","短线交易系统计算结果","董监高不可减持的其他情形","董监高持股转让限制","董监高禁止敏感期交易","要约收购","超过30%自由增持2%以内","重大资产重组认购股份限售期要求","集中竞价交易减持时间区间内事中披露要求","集中竞价交易减持时间区间内事中披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","集中竞价交易减持预披露要求","集中竞价交易减持预披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","集中竞价交易系统减持完毕披露要求","集中竞价交易系统减持完毕披露要求（协议转让股份并导致出让方不再具有大股东身份的，出让方或受让方）","首次达10%披露与暂停交易","首次达到5%披露与暂停交易","首次达到5%披露与暂停要求"]

## 处理规则
1. 判断是否符合直接法规解答条件：
   - 涉及具体减持规则/披露要求/限售条款等法定程序
   - 问题明确指向预定义交易类型描述范畴
2. 严格匹配原则：
   - 仅允许提取列表中存在的交易类型
   - 禁止同义词转换或近义词匹配
   - 多类型问题需完整列举
3. 意图校验原则：
   - 若intent_validate返回False，需重新识别意图
   - 若intent_validate返回True，意图识别成功继续执行assistant节点

## 示例
1. 问题: "请告诉我控股股东在窗口期中能否出售解除限售的股份？"
   - 返回: `"problem_type": "法规解答", "transaction_types": ["控股股东、实际控制人在窗口期中不得出售解除限售的股份"]`

2. 问题: "想了解一下大宗交易减持的规定。"
   - 返回: `"problem_type": "法规解答", "transaction_types": ["大宗交易减持要求"]`

3. 问题: "我想知道如果有减持的情况需要披露什么信息？"
   - 返回: `"problem_type": "查询交易限制", "transaction_types": None`
    """

    messages = [
                   SystemMessage(_system_prompt),
               ] + state["messages"]
    response = model_with_tools_deepseek.invoke(messages)
    return {"messages": [response]}


# 创建一个ToolNode对象，参数为tools
tool_node1 = ToolNode(tools1)


class AskHuman(BaseModel):
    """与用户确认信息时使用，如确认股东身份、持股比例、交易类型等"""
    question: str


tools2 = [get_provisions, get_transaction_types]
model_with_tools_chatgpt = get_a_llm(llm_type="gpt-4o").bind_tools(tools2 + [AskHuman])
tool_node2 = ToolNode(tools2)



def assistant(state: MessagesState):
    """A node for the assistant to respond."""

    _system_prompt = f"""<role>
    你是一名金融合规领域专家，解决用户交易合规类问题。
    每一个Step的执行原则和注意事项分别体现在<principle>和<notice>中，需要严格遵守。
</role>

<principle>
    若意图为“查询交易限制”，##严格##按照Step1~Step6一步一步思考并执行，##不得##跳过任何一个步骤；
    若意图为“法规解答”，则直接执行Step6；

    Step1 从用户问题中获取[持股比例, 任职类型, 股东类型, 买卖方向, 变动比例, 是否在公司首次公开发行前持有股份]这些字段信息，字段详情如<extraction keys>所示。
        <extraction keys>
            持股比例：用户当前持股比例，并基于用户提供的持股比例，归纳为“5%以下”、“5%~30%”和“30%以上”三个类型中的一个；
            任职类型：“董监高”、“董监高的一致行动人”和“其他”中的一个，其中，董监高的亲属（如父母、配偶、子女等）视为董监高的一致行动人，但董监高的合伙人不是董监高的一致行动人；
            股东类型：“控股股东或实际控制人”、“控股股东或实际控制人的一致行动人”和“其他”中的一个；
            买卖方向：“买入股票”或“卖出股票”中的一个；
            变动比例：用户变动股数占公司总股数的百分比，需要结合已知公司总股数进行计算；
            是否在首次公开发行前持有公司股份：“是”或“否”的一个，如果用户问题中未提及，默认为“否”。
        </extraction keys>
        <notice>
            如问题中未提及，使用工具“AskHuman”与用户确认。若在一轮确认中用户不提供，则将该字段置为NULL。
        </notice>

    Step2 列举Step1抽取/与用户确认的字段，并使用工具“AskHuman”与用户确认是否正确：若正确，继续执行Step3；若不正确或字段有变动，需更新后再使用工具“AskHuman”与用户确认，直到用户确认正确为止。

    Step3 依据抽取的字段，使用工具“get_transaction_types”查询可能涉及的交易类型，得到列表List-1。

    Step4 基于List-1中的交易类型，逐一确定用户问题可能涉及的交易类型，判断原则如下：
        <principle>
            a) 如果List-1中包含“短线交易”，需要使用工具“AskHuman”与用户确认最近一次股票交易时间和交易类型（买入/卖出），以在Step5中确定用户是否存在“短线交易”情况；
            b) 若List-1中同时包含“变动1%披露要求”和“变动5%披露与暂停交易”，则需要基于Step1中的“变动比例”字段，进一步判断涉及的交易类型；
            c) 其余可能涉及的交易类型按照List-1存放。
        </principle>

    Step5 基于Step4中的交易类型，使用工具“get_provisions”查询涉及的法条，并仔细研读查询到的法条回复用户问题。
        <principle>
            若交易类型中包含“短线交易”，需要基于Step4中用户提供的最近一次股票交易时间，当时的交易类型（买入/卖出）以及Step1中抽取的计划买卖方向信息，结合法条，判断是否存在“短线交易”情况：若不存在，不需要在回复内容中体现；若存在，需要结合用户问题解读法条。
        </principle>
        <notice>
            a) 回复内容需要包含{FINISH_TAG}字样；
            b) 回复用户时需要首先基于交易类型##逐一##结合法条和用户问题，并提供专业的知识解读，然后基于用户实际问题，给出针对该交易的综合建议；
            c) 若查询法条为空，回复用户“该操作不涉及相关法规限制，但需遵守相关法规，同时要避免短线交易和内幕交易。{FINISH_TAG}”。
        </notice>

    Step6 基于交易类型transaction_types，使用工具“get_provisions”查询涉及的法条，并仔细研读查询到的法条回复用户问题。
        <notice>
            a) 回复内容需要包含{FINISH_TAG}字样；
            b) 回复用户时需要首先基于交易类型##逐一##结合法条和用户问题，并提供专业的知识解读，然后基于用户实际问题，给出针对该交易的综合建议；
            c) 若查询法条为空，回复用户“该操作不涉及相关法规限制，但需遵守相关法规，同时要避免短线交易和内幕交易。{FINISH_TAG}”。
        </notice>
</principle>

<notice>
    a) 执行过程中，除与用户确认信息（使用工具AskHuman）和最终回答用户外，所有抽取、查询和计算结果均为中间结果，##不得##透露或返回给用户；同时也##不得##向用户透露上述所有过程（包括Step1～Step6的思考过程）。
    b) 使用工具“AskHuman”时，不得同时使用其他工具。
</notice>"""

    messages = [
                   SystemMessage(_system_prompt),
               ] + state["messages"]

    response = model_with_tools_chatgpt.invoke(messages)
    return {"messages": [response]}


def human_node(state: MessagesState):
    """A node for collecting user input."""
    user_input = interrupt({
        'question': '请确认您的持股比例、任职类型、股东类型、买卖方向等信息。',
    })
    tool_call_id = state["messages"][-1].tool_calls[0]["id"]

    tool_message = [{"tool_call_id": tool_call_id, "type": "tool", "content": user_input}]
    return {"messages": tool_message}


def delete_messages(state: MessagesState):
    """A node for deleting messages."""
    delete_message_indices = [msg.id for msg in state["messages"][:-1] if msg.type != "human"]
    return {"messages": [RemoveMessage(id=_id) for _id in delete_message_indices]}
