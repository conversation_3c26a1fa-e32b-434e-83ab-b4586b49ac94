from typing import Dict, Any

from pydantic import BaseModel


def model_to_alias_dict(model_instance: BaseModel) -> Dict[str, Any]:
    """
    将 Pydantic 模型对象转换为以 Field alias 为键的字典。

    Args:
        model_instance: Pydantic 模型对象。

    Returns:
        以 Field alias 为键的字典。
    """
    return {
        field.alias: getattr(model_instance, field_name)
        for field_name, field in model_instance.model_fields.items()
        if getattr(model_instance, field_name)
    }


def get_dict_value(dict_obj: dict, key_name: str, default_value=None):
    if not dict_obj:
        return default_value
    if not dict_obj.get(key_name):
        return default_value
    return dict_obj.get(key_name)
