import os
from datetime import datetime

from langchain_core.messages import HumanMessage, RemoveMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langchain_deepseek import ChatDeepSeek

from sub_agents.company.general.com_biz_state_def import ComGenAgentState

prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
     你是一位【比亚迪股份有限公司】数据问答专家，请根据提供的参考资料，给出用户满意的答案。
     **任务：**
     1.  **评估相关性：**
        - 仔细评估参考资料与用户问题的相关性、信息匹配度和权威性。
        - 资料不足时：直接回复用户，清晰指出资料不足的原因，并引导用户提供更详细的信息（如业务场景、关键词等），以便重新获取资料。
        - 资料充足时：直接回答用户问题。
     2.  **回答要求：**
        -  **明确答案:** 先明确回答用户问题，让用户快速理解核心内容，然后展开阐述。
        -  **相关资料的引用：** 只能引用"拥有的参考资料"中提到的数据。
                          多引用资料以增强可信度。
                          精确分析"拥有的参考资料"提供的内容，给出准确答复。
                          注意区分各类型的数据，避免混淆。
                          注意识别资料的日期顺序，日期离当前时间越近，说明资料越重要。
        -  **答案补充: **对简短答案客观问题，补充1-2句相关信息，如补充信息时，**优先选择提供与问题相关的背景知识、行业趋势、或实践建议** 。
                      若提及资料来源，则提示用户来源于“易董”资料库。
                      当需要引导用户进行其他资料查询时，建议用户使用“易董”相关资料库进行查询。
        -  **格式美观：** 选择合适的格式（如 Markdown、列表、表格），确保回答内容清晰、易读。可使用标题、粗体、分隔线等增强可读性。
        -  **口语化 & 专业性：** 用通俗易懂的语言，以专家视角进行拟人化、流畅的回答。
        -  **时效性：** 优先引用时间更近的法规资料。当前时间：{time}。
        -  **结束语：** 在回答末尾，添加一句简洁专业的结束语，以巧妙展现你作为数据问答专家的智能性。例如 ：我还可以帮你什么什么（基于"问题"和"拥有的参考资料"），如果有需要的话 欢迎向我提问。
     3. 相信你自己的能力，突破自己，努力努力能产生更好的结果。
     4. 仔细思考确定这就是你的最终回答吗，请仔细对照上述流程，对你的目标保持专注，坚持不懈将取得卓越的成就。
     """),
    ("placeholder", "{com_spokesman_msgs}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = ChatDeepSeek(model='hs-deepseek-r1',
                   # temperature=0,
                   api_base=os.getenv('MODEL_HUB_HOST'),
                   api_key='sk-7XYtE83ltdpER8T16f963859333c47728e86F0C8F9105a74',
                   streaming=True)
com_spokesman_runnable = prompt_template | llm


def add_remove_msgs(messages: list, keep_num: int):
    """
    想删除队列的消息，必须添加remove消息，在返回后才能被真正删除
    :param messages: 消息队列
    :param keep_num: 保留的对话次数；用户问题消息 + 之后跟这次问题相关的消息 = 一次对话
    """
    remove_msgs = []
    # 获取所有 type == 'human' 的下标
    human_indices = [i for i, message in enumerate(messages) if message.type == "human"]
    keep_human_min_index = 0
    if len(human_indices) >= keep_num:
        # 获取需要保留的 'human' 消息的最小下标
        keep_human_min_index = human_indices[0 - keep_num]

    for idx, msg in enumerate(messages):
        if idx < keep_human_min_index:
            # 比最小下标小的消息直接添加到删除列表中
            remove_msgs.append(RemoveMessage(id=msg.id))
        else:
            # 历史消息记录只保留human消息及相关的最后一条消息，中间消息全部删除
            if msg.type == 'human':
                if remove_msgs:
                    remove_msgs.pop()
            else:
                remove_msgs.append(RemoveMessage(id=msg.id))
    messages.extend(remove_msgs)
    return messages


class ComSpokesman:
    def __init__(self):
        self.runnable = com_spokesman_runnable

    async def __call__(self, _state: ComGenAgentState, config: RunnableConfig):
        # 管理历史消息（保留4条聊天记录）
        _state["messages"] = add_remove_msgs(_state["messages"], 4)
        # 找最近一次human消息索引
        last_conv_idx = -1
        for i in range(len(_state["messages"]) - 1, -1, -1):
            if _state["messages"][i].type == 'human':
                last_conv_idx = i
                break
        # 拼接对话
        com_spokesman_msgs = []
        last_conv_msgs = {"问题": "", "拥有的参考资料": []}
        for index, msg in enumerate(_state["messages"]):
            if index < last_conv_idx:
                # 拼接历史记录
                com_spokesman_msgs.append(msg)
            else:
                # 拼接本次对话
                if msg.content and ((not hasattr(msg, 'status')) or (hasattr(msg, 'status') and msg.status != 'error')):
                    if msg.type == 'human':
                        last_conv_msgs['问题'] = msg.content
                    elif msg.type == 'tool':
                        last_conv_msgs['拥有的参考资料'].append(msg.content)
        com_spokesman_msgs.append(HumanMessage(content=str(last_conv_msgs)))
        spokesman_state = {'com_spokesman_msgs': com_spokesman_msgs}
        result = await self.runnable.ainvoke(spokesman_state, config)
        _state["messages"].append(result)
        return _state
