from typing import TypedDict, Annotated, Optional, List

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages


class ComBasicInfoAgentState(TypedDict):
    com_basic_msgs: Annotated[list[AnyMessage], add_messages]


class ComGenAgentState(ComBasicInfoAgentState):
    messages: Annotated[list[AnyMessage], add_messages]
    which: Optional[List[str]]
    request: Optional[dict]
    reflector_num: Optional[int]
