import traceback

import gradio as gr
import uuid

from langchain_core.messages import AIMessageChunk

from sub_agents.company.general.com_gen_drawer import com_gen_graph
from utils.company_utils import confirm_company
from utils.db_tool import MyAIOMySQLSaver, agent_checkpoint_db_uri
from utils.redis_tool import remove_redis_info

view_thread_id = str(uuid.uuid4())


async def view_main(user_input):
    companies = confirm_company("000001")
    if not companies:
        raise ValueError("未查到此公司信息")
    curr_com_info = companies[0]
    config = {
        "recursion_limit": 20,
        "configurable": {
            "thread_id": view_thread_id,
            "user_id": view_thread_id,
            "user_cur_input": user_input,
            "curr_com_info": curr_com_info,
        },
    }

    gathered = ''
    reasoning_block_started = False  # 标记 reasoning_content 代码块是否已开始
    reasoning_block_closed = False  # 标记 reasoning_content 代码块是否已结束
    try:
        async with MyAIOMySQLSaver.from_conn_string(agent_checkpoint_db_uri) as checkpointer:
            com_gen_graph_compiled = com_gen_graph.compile(checkpointer=checkpointer)
            async for msg, metadata in com_gen_graph_compiled.astream({
                "messages": ("user", user_input),
            }, config,
                    stream_mode="messages",
                    debug=True):
                if isinstance(msg, AIMessageChunk) and (metadata['langgraph_node'] == 'ComSpokesman'):
                    # print('-----------------------------', msg, metadata)

                    reasoning_chunk = msg.additional_kwargs.get('reasoning_content', '')
                    content_chunk = msg.content or ''
                    output_chunk = ""

                    if reasoning_chunk:
                        if not reasoning_block_started:  # 如果 reasoning_content 代码块还没开始，先开始
                            output_chunk += "```\n"
                            reasoning_block_started = True
                            reasoning_block_closed = False  # 确保在新的 reasoning_content 开始时，closed 标记是 False
                        output_chunk += reasoning_chunk  # 输出 reasoning_content chunk

                    if content_chunk:
                        if reasoning_block_started and not reasoning_block_closed:  # 如果 reasoning_content 代码块已经开始 并且还没结束，现在结束它
                            output_chunk += "```\n"
                            reasoning_block_closed = True  # 标记为已结束
                        output_chunk += content_chunk  # 输出 content chunk

                    gathered += output_chunk
                    yield gathered

                    if hasattr(msg, 'response_metadata') and msg.response_metadata.get('finish_reason') == 'stop':
                        if reasoning_block_started and not reasoning_block_closed:  # 最终结束时，如果 reasoning_content 代码块还没结束，确保结束 (以防万一 content_chunk 没出现)
                            output_chunk += "```\n"
                        reasoning_block_started = False  # 重置标记
                        reasoning_block_closed = False  # 重置标记
    except Exception as e:
        traceback.print_exc()
        yield f"抱歉，处理您的请求时出现了错误。{e}"


async def chat(message, history):
    async for chunk in view_main(message):
        yield chunk


# 创建Gradio接口
iface = gr.ChatInterface(
    chat,
    chatbot=gr.Chatbot(elem_id="chatbot", render=False),
    textbox=gr.Textbox(placeholder="在这里输入你的消息...", container=False, scale=7),
    title="比亚迪股份有限公司数据问答",
    description="这是一个使用Gradio创建的比亚迪股份有限公司数据问答对话界面",
    theme="soft",
    cache_examples=True,
    css="""
    #chatbot {
        height: calc(100vh - 280px) !important;
        overflow-y: auto;
    }
    .gradio-container code {
        width: 97.5% !important;
        color: #696969 !important;
    }
    .gradio-container code {
        white-space: normal !important;
    }
    """
)

if __name__ == '__main__':
    TOKEN_REDIS_KEY_NAME = ('COM_AGENT_ACCESS_TOKEN',)
    for item in TOKEN_REDIS_KEY_NAME:
        remove_redis_info(item)
    iface.launch(server_name="127.0.0.1", server_port=7860, debug=True)
