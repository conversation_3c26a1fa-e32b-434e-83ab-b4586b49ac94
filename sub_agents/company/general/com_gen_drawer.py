from typing import Sequence, Literal

from langchain_core.messages import ToolMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START, END
from langgraph.graph import StateGraph

from sub_agents.company.basic.com_basic_drawer import com_basic_graph
from sub_agents.company.general.com_biz_agent_def import COM_AGENT_NODE_MAP
from sub_agents.company.general.com_biz_state_def import ComGenAgentState
from sub_agents.company.general.com_gen_core import ComGenAgent
from sub_agents.company.general.com_reflector_core import ComReflector
from sub_agents.company.general.com_spokesman_core import ComSpokesman


async def com_convergence_node(_state: ComGenAgentState) -> dict:
    if not (_state["messages"] and _state["messages"][-1].type == "ai"):
        return _state
    # 获取最新AIMessage的tool_call_id
    # 获取tools_calls中的所有id
    tool_calls = _state["messages"][-1].tool_calls
    messages_result = []
    if tool_calls:
        for tool_call in tool_calls:
            tool_call_id = tool_call["id"]
            tool_call_name = tool_call["name"]

            # 获取对应代理的消息列表
            tool_call_messages = _state[f'{COM_AGENT_NODE_MAP.get(tool_call_name)}_msgs']
            if tool_call_messages:
                # 找到biz_tools_id对应业务代理HumanMessage后的ToolMessage
                # 并把其中的content拼接后形成一个新的ToolMessage返回
                found_human = False
                tool_call_message_content = ''
                for tool_call_message in tool_call_messages:
                    if (tool_call_message.type == 'human' and hasattr(tool_call_message, 'biz_tools_id')
                            and tool_call_message.biz_tools_id == tool_call_id):
                        found_human = True
                        continue
                    if found_human and tool_call_message.type == 'tool':
                        tool_call_message_content += '\n' + tool_call_message.content
                messages_result.append(
                    ToolMessage(
                        content=tool_call_message_content,
                        tool_call_id=tool_call_id,
                    )
                )
    _state['messages'] = messages_result
    return _state


com_gen_graph = StateGraph(ComGenAgentState)

# NODE
com_gen_graph.add_node("ComGenAgent", ComGenAgent())
com_gen_graph.add_node("ComConvergence", com_convergence_node)
com_gen_graph.add_node("ComReflector", ComReflector())
com_gen_graph.add_node("ComSpokesman", ComSpokesman())

com_gen_graph.add_node("ToComBasicAgent", com_basic_graph)

# EDGE
com_gen_graph.add_edge(START, "ComGenAgent")
com_gen_graph.add_edge("ComConvergence", "ComReflector")

com_gen_graph.add_edge("ToComBasicAgent", "ComConvergence")

com_gen_graph.add_edge("ComSpokesman", END)


# CONDITIONAL_EDGE
async def route_to_nodes(state: ComGenAgentState) -> Sequence[str]:
    if not state["which"]:
        state["which"] = ["ComReflector"]
    return state["which"]


nodes_name_list = ["ComReflector"] + list(COM_AGENT_NODE_MAP.keys())
com_gen_graph.add_conditional_edges(
    "ComGenAgent",
    route_to_nodes,
    nodes_name_list,
)


async def route_reflector_to_gen(_state: ComGenAgentState) -> Literal["ComGenAgent", "ComSpokesman"]:
    """
    判断总结node走向
    :return: - ComGenAgent
             - ComSpokesman
    """
    if _state["which"] == ["ComGenAgent"]:
        return 'ComGenAgent'
    else:
        return 'ComSpokesman'


com_gen_graph.add_conditional_edges("ComReflector", route_reflector_to_gen)

# 编译图
# 定义历史记录的存储器
memory = MemorySaver()  # 内存存储
com_gen_graph_compiled = com_gen_graph.compile(checkpointer=memory)

# 生成图片
# com_gen_graph_compiled.get_graph(xray=True).draw_mermaid_png(
#     output_file_path='sub_agents/company/general/com_gen_graph.builder.png')
