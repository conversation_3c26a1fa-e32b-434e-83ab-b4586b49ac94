from datetime import datetime

from langchain_core.messages import HumanMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.company.general.com_biz_agent_def import ToComBasicAgent, COM_AGENT_NODE_MAP
from sub_agents.company.general.com_biz_state_def import ComGenAgentState
from utils.common_utils import handle_tool_error
from utils.llm_utils import get_a_llm

prompt_content = """
    <role>
        你是一名**高效且专业的**公司信息查询任务分配专家。
        你的**唯一**核心职责是接收用户关于公司数据的问题，并 **精准、迅速地** 分配任务给**所有**可用的业务助手，确保系统能够**最大程度地**全面检索和准备相关数据。
        你**不负责**直接回答用户问题，你的价值在于作为数据准备流程的**绝对核心**节点，驱动整个系统的数据获取效率和完整性。
    </role>

    <principle>
        遵循以下逻辑，高效分配任务并最大化数据检索范围：

        1. 问题理解与需求挖掘：
            - 深入分析用户提出的问题，精确理解用户的核心需求和潜在的深层疑问，例如用户是在询问工商信息、员工数据、舆情公告，还是在查询股权结构等数据？
            - 识别问题中可能存在的模糊或不明确之处，预判用户可能遗漏但对公司数据查询至关重要的信息。

        2.  用户意图识别:
            - 在深入理解用户问题后，尝试识别用户的深层意图和最终目标。  例如，用户问题表面上是 “董监高的任职情况”，但其深层意图可能是 “查看董监高的变动情况”，“董监高人物介绍”等。  更准确地识别用户意图，可以帮助更好地分配任务，并为后续的答案生成环节提供更精准的数据支持方向。( **注意：  本步骤仅为辅助任务分配，严禁在本节点进行任何形式的答案生成或预判。**)

        3. 主动澄清与信息补充（如有必要）：
            - 当用户问题不够具体，或者为了更精准地把握用户需求，可以主动追问关键信息，例如：
                - "您目前主要关注股权结构的哪个方面？是股本变动、股权穿透、还是股东情况？"
                - "您希望了解的财务信息是什么？是想查询资产负债、利润信息，还是其他？"

        4. 全方位数据检索任务分配：
            - 核心策略：  无论用户问题的具体方向如何，**全面调用所有可用的业务助手**，从工商信息、员工情况、股权结构、股票数据、舆情新闻、披露公告或财务状况几个方面分析，确保数据覆盖的完整性和多维性。
            - 任务描述优化：  高质量的任务描述是高效数据检索的关键。在生成任务描述时，请注意以下几点：
                - 明确性： 任务描述必须清晰、简洁、明确地指示助手需要检索的内容和方向，避免模糊不清的指令。
                - 相关性：  任务描述应紧密围绕用户问题和助手的核心职责，确保检索结果与用户需求高度相关。
                - 可执行性：  任务描述应是助手可以理解和执行的指令，例如使用关键词、指定检索范围、明确信息类型等。
                - 目标导向：  任务描述应体现检索的目标，例如 “查找...的财务数据”，“检索...的公告内容”，“搜索...的股票信息” 等。
            - 预判与关联 (可选，Advanced)： 如果你能够预判不同助手之间检索结果的潜在关联性，可以在任务描述中 **预先引导助手考虑其他助手可能提供的信息**，从而进行更智能、更具针对性的检索。  例如，在 `ToComBasicAgent` 的任务描述中，可以预先引导其考虑。 (**请注意，这只是预判引导，并非让助手等待其他助手的实际结果，所有任务仍然是并行分配的。**)
            - 任务分配策略：针对每个助手，根据其核心职责，将用户问题转化为具体的检索任务描述。  例如，对于 `ToComBasicAgent`，任务可以是 "检索与[用户问题关键词]相关的工商数据或其他基本情况"；"。
            - 数据隔离：每个助手只清楚自己专业领域的知识。
        5. 数据准备与任务完成:
            - 确保所有助手都被成功调用，并分配了明确的任务描述。
            - 本节点的核心产出是触发所有子助手进行数据检索，并将检索任务分配下去，为后续的答案生成节点准备好全面的数据基础。

    </principle>

    <available_assistants>
        系统配备的业务助手及其核心职责：

        1. ToComBasicAgent:
            - 擅长查询工商信息，公司下属机构、客户供应商或竞品等相关数据。
            - 任务示例： 检索2024年中报中提及的子公司情况。

    </available_assistants>

    <key_guidelines>
        1.  深入理解用户请求，不仅关注字面意思，更要挖掘用户提问背后的真实意图和潜在需求，以便为子助手分配合适的检索任务。
        2.  使用函数调用静默分配任务，无需用户感知，保证用户体验的流畅性。
        3.  在分配任务时，将任务描述中包含的模糊时间段（如“最近一周”、“最近一个月”、“今年”）转换为精确的日期，确保检索的准确性。
        4.  分配任务时无需与客户沟通，直接调用工具即可。
        5.  **严禁在本节点进行任何形式的答案生成、信息总结或提前预判答案的行为。** 你的核心目标 **仅** 是有效分配任务，驱动所有助手进行数据检索，为后续环节提供最全面的数据支持。  **请严格遵守你的任务分配专家角色，将所有精力集中在高质量的任务分配上。**
        6.  确保所有时间转换准确且基于：{time}。  (保持时间基准的准确性)
    </key_guidelines>
"""

prompt_template = ChatPromptTemplate.from_messages([
    ("system", prompt_content),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm('4o', parallel_tool_calls=None)
com_gen_runnable = prompt_template | llm.bind_tools([ToComBasicAgent])


class ComGenAgent:
    def __init__(self):
        self.runnable = com_gen_runnable

    async def __call__(self, _state: ComGenAgentState, config: RunnableConfig):
        while True:
            _state = {**_state, "time": datetime.now()}
            # 使用模型决定节点走向
            result = await self.runnable.ainvoke(_state, config)
            # 拼接AIMessage
            _state["messages"].append(result)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["messages"].append(("user", "请返回实际的文本输出"))
            else:
                # 初始化 which 和 request 列表
                _state["which"] = []
                _state["request"] = {}
                # 判断是否有方法调用
                if hasattr(result, 'tool_calls') and result.tool_calls:
                    # 遍历 tool_calls
                    for tool_call in result.tool_calls:
                        try:
                            name = tool_call['name']
                            request_value = tool_call['args']['request']
                            _state["which"].append(name)  # 将 name 放入 which 列表
                            if COM_AGENT_NODE_MAP.get(name):
                                # 构建HumanMessage
                                (_state[f'{COM_AGENT_NODE_MAP.get(name)}_msgs']
                                 .append(HumanMessage(content=request_value, biz_tools_id=tool_call["id"])))
                        except Exception as e:
                            _state["messages"].append(ToolMessage(
                                content=handle_tool_error(e, flag=True),
                                name=tool_call["name"],
                                tool_call_id=tool_call["id"],
                                status="error",
                            ))
                else:
                    _state["which"] = ["ComReflector"]
                break
        return _state
