from datetime import datetime
from typing import get_type_hints

from langchain_core.messages import RemoveMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.company.general.com_biz_agent_def import ToComGenAgent
from sub_agents.company.general.com_biz_state_def import ComGenAgentState
from utils.llm_utils import get_a_llm

llm = get_a_llm('4o')

prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
     作为一名专业的质量审查员，你的核心任务是对工具查询参考数据进行“反思”审查，确保为后续生成用户回复提供高质量的支撑。请遵循以下步骤进行反思：
         注意：'问题'代指的是用户最新提出的问题

         1. 回顾背景信息：
            - 仔细回顾用户提供的对话内容，理解用户的完整意图和上下文。
            - 分析工具返回的查询结果，理解工具获取了哪些数据。
            - 工具只能获取数据，没有总结分析能力

         2. 关键反思（输出你的反思结果）：
            - **问题支持度评估**： 综合考虑所有工具返回的数据，评估这些数据**整体上**是否**足以支持**回答用户的问题。即使部分工具未能返回数据，只要**整体数据**能有效支持回答，也应视为通过。请明确指出**整体数据是否支持回答问题**。

         3. 反思总结与建议（为后续节点提供决策依据）：
            - **如果整体数据不足以支持回答问题**，请总结说明哪些关键信息缺失，并简要说明需要工具重新查询的方向或关键词，以获取缺失信息。
            - **如果整体数据能够支持回答问题**，即使部分工具数据缺失，也请明确表示**现有数据质量良好，可以用于生成用户回复**。无需针对缺失数据的工具进行额外查询。
            - 只有在**确认整体数据不足**时，才考虑使用 ToComGenAgent 通知后续节点获取缺失数据。

     关键准则：
         1. 用户隐私：严格保护用户隐私和敏感信息。
         2. 数据合规：遵守公司的数据安全和合规政策。
         3. 客观中立：在分析数据时，保持中立和客观的立场。
         4. 专业态度：始终保持专业、严谨的态度进行反思。
         5. 时间信息：当前时间是 {time}，请在必要时将其纳入你的判断中，例如时效性问题。
     """),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

com_reflector_runnable = prompt_template | llm.bind_tools([ToComGenAgent])


def build_remove_msg_dict(_state) -> dict[str, list]:
    """
    删除各种子业务代理的消息，从而需要在消息列表中增加RemoveMessage
    """
    annotations = list(get_type_hints(ComGenAgentState).keys())
    return {
        msg_name: [RemoveMessage(id=m.id) for m in _state[msg_name]] for msg_name in annotations if '_msgs' in msg_name
    }


class ComReflector:
    def __init__(self):
        self.runnable = com_reflector_runnable

    async def __call__(self, _state: ComGenAgentState, config: RunnableConfig):
        while True:
            # 初始化参数
            _state = {**_state, "time": datetime.now()}
            # 反思次数初始化
            if 'reflector_num' not in _state or _state['reflector_num'] is None:
                _state['reflector_num'] = 0

            # 反思次数达到2次就直接返回给ComSpokesman节点
            if _state['reflector_num'] == 2:
                _state["which"] = ['ComSpokesman']
                _state["request"] = {}
                return build_remove_msg_dict(_state)
            # 使用模型判断节点走向
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or (isinstance(result.content, list) and not result.content[0].get("text"))):
                _state["messages"].append(("user", "请返回实际的文本输出"))
            else:
                # 初始化 which 和 request 列表
                _state["which"] = []
                _state["request"] = {}
                break
        # 如果有工具调用说明需要回到ComGenAgent节点
        if result.tool_calls:
            _state['reflector_num'] += 1
            _state["messages"].append(AIMessage(
                content=f"发现内容缺失，请根据缺失内容补充后重新回答，缺失内容如下：\n{result.tool_calls[0]['args']['request']}"))
            _state['which'] = ['ComGenAgent']
            return _state
        else:
            return {
                **build_remove_msg_dict(_state),
                "messages": result
            }
