import json

from langchain_core.runnables import RunnableConfig

from sub_agents.company.basic.com_basic_entity import ComBaseInfo, BizChangeRecord, BranchOfficeInfo, SubsidiaryInfo, \
    MajorCustomer, MajorSupplier, CompProductInfo
from sub_agents.company.utils.obj_utils import model_to_alias_dict, get_dict_value


async def req_biz_info(config: RunnableConfig = None) -> dict:
    """
    获取公司的工商信息
    :return: 公司的工商信息
    """
    # TODO 改成从数据库或接口获取
    # 从json文件中构建静态数据
    cb = ComBaseInfo()
    with open('sub_agents/company/basic/test_data/selectComInfo.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result_sci = get_dict_value(response, 'result', {})
        # 电话
        phone_list = get_dict_value(result_sci, 'phoneList', [])
        if phone_list:
            cb.telephone = ",".join([p['phoneValueQxb'] for p in phone_list])
        # 邮箱
        email_list = get_dict_value(result_sci, 'emailList', [])
        if email_list:
            cb.email = ",".join([p['emailValueQxb'] for p in email_list])
        # 官网
        web_list = get_dict_value(result_sci, 'webList', [])
        if web_list:
            cb.officialWebsite = ",".join([p['webValueQxb'] for p in web_list])
        # 曾用名
        history_name_list = get_dict_value(result_sci, 'historyNameList', [])
        if history_name_list:
            cb.formerName = ",".join([p['historyNameQxb'] for p in history_name_list])
        # 控制权变更记录
        cb.controlChange = get_dict_value(result_sci, 'controStr', '')

    with open('sub_agents/company/basic/test_data/getCompanyOverview.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result_co = response.get('result', {}) or {}
        if result_co:
            cb.companyName = get_dict_value(result_co, 'qxbData', {}).get('nameQxb')
            cb.englishName = get_dict_value(result_co, 'dcData', {}).get('fennameDc')
            cb.aShareAbbrev = get_dict_value(result_co, 'dcData', {}).get('securitysnameDc')
            cb.aShareCode = get_dict_value(result_co, 'dcData', {}).get('securitycode')
            cb.establishTime = get_dict_value(result_co, 'qxbData', {}).get('termStartQxb')
            cb.operatingStatus = get_dict_value(result_co, 'qxbData', {}).get('statusQxb')
            cb.registeredCapital = get_dict_value(result_co, 'qxbData', {}).get('registCapiNewQxb')
            cb.paidUpCapital = get_dict_value(result_co, 'qxbData', {}).get('actualCapiQxb')
            cb.neIndustry = get_dict_value(result_co, 'qxbData', {}).get('industryQxb')
            cb.swIndustry = get_dict_value(result_co, 'dcData', {}).get('indsort026Dc')
            cb.legalEntity = get_dict_value(result_co, 'qxbData', {}).get('operNameQxb')
            cb.legalReps = get_dict_value(result_co, 'qxbData', {}).get('operNameQxb')
            cb.uscCode = get_dict_value(result_co, 'qxbData', {}).get('creditNoQxb')
            cb.bizRegNumber = get_dict_value(result_co, 'qxbData', {}).get('regNoQxb')
            cb.orgCode = get_dict_value(result_co, 'qxbData', {}).get('orgNoQxb')
            cb.bizTerm = f"{get_dict_value(result_co, 'qxbData', {}).get('termStartQxb', '--') or '--'} ~ {get_dict_value(result_co, 'qxbData', {}).get('termEndQxb', '--') or '--'}"
            cb.regAuthority = get_dict_value(result_co, 'qxbData', {}).get('belongOrgQxb')
            cb.entType = get_dict_value(result_co, 'qxbData', {}).get('econKindQxb')
            cb.regAddress = get_dict_value(result_co, 'qxbData', {}).get('addressQxb')
            cb.officeAddress = get_dict_value(result_co, 'dcData', {}).get('officeaddressDc')
            cb.introduction = get_dict_value(result_co, 'dcData', {}).get('compprofileDc')
            cb.mainProducts = get_dict_value(result_co, 'dcData', {}).get('mainproductsDc')
            cb.bizScope = get_dict_value(result_co, 'dcData', {}).get('businscopeDc')
            cb.briefHistory = get_dict_value(result_co, 'dcData', {}).get('compscopeDc')
    return model_to_alias_dict(cb)


async def req_industry_chain_position(config: RunnableConfig = None) -> dict:
    """
    获取公司的产业链定位
    :return: 公司的产业链定位
    """
    # TODO 改成从数据库或接口获取，未成功解析接口返回数据，暂使用列表代替
    # 部分数据
    return {
        "公司涉及产业链节点数": 41,
        "公司涉及的产业链节点": [
            ["人工智能", "应用层", "解决方案", "智慧出行（公司或涉及相关业务）"],
            ["充电桩", "充电桩", "无线充电桩（公司或涉及相关业务）"],
            ["充电桩", "应用服务", "新能源车企（公司或涉及相关业务）"],
            ["光伏设备", "光伏配套设备", "蓄电池组（公司或涉及相关业务）"],
            ["动能电池正极材料", "冶炼端", "碳酸锂（公司或涉及相关业务）"],
            ["动能电池正极材料", "产品端", "磷酸铁锂（公司或涉及相关业务）"]
        ]
    }


async def req_biz_change_records(change_date: list[str], change_type: str = '',
                                 config: RunnableConfig = None) -> dict | str:
    """
    获取公司工商变更信息
    :param change_date 变更日期的查询范围；非必填；未提查询时间默认传空数组[]；若填写则需按照['yyyy-MM-dd', 'yyyy-MM-dd']格式填写
    :param change_type 变更事项的类型；非必填；单选；可选值：股东股权变更，注册资金变更，负责人变更，期限变更，人员变更，企业类型变更，企业名称变更，经营范围变更
    :return: 工商变更信息
    """
    # TODO 改成从数据库或接口获取
    records = []
    with open('sub_agents/company/basic/test_data/getICChangeRecord.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result = get_dict_value(response, 'result', {})
        total = get_dict_value(result, 'total', 0)
        res_records = get_dict_value(result, 'ICChangeRecord', [])
        for item in res_records:
            if item.get('changeDateQxb'):
                bcr = BizChangeRecord()
                bcr.changeDate = item.get('changeDateQxb')
                bcr.changes = item.get('typeQxb')
                bcr.contentBeforeChange = get_dict_value(item, 'beforeContentQxb', '--')
                bcr.contentAfterChange = get_dict_value(item, 'afterContentQxb', '--')
                records.append(model_to_alias_dict(bcr))
        return {
            "工商变更记录的总数": total,
            "工商变更记录的部分数据" if total > len(records) else "工商变更记录的全部数据": records
        }


async def req_branch_office(company_name: str = '', config: RunnableConfig = None) -> dict:
    """
    获取公司下属分支机构信息
    :param company_name 分支机构的名称；非必填
    :return: 分支机构信息
    """
    # TODO 改成从数据库或接口获取
    branch_orgs = []
    with open('sub_agents/company/basic/test_data/getBranchOrg.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result = get_dict_value(response, 'result', {})
        total = get_dict_value(result, 'total', 0)
        res_branch_orgs = get_dict_value(result, 'branchOrg', [])
        for item in res_branch_orgs:
            if item.get('nameQxb'):
                boi = BranchOfficeInfo()
                boi.companyName = item.get('nameQxb')
                boi.operatingStatus = item.get('statusQxb')
                boi.legalReps = item.get('operNameQxb')
                boi.establishTime = item.get('dateQxb')
                branch_orgs.append(model_to_alias_dict(boi))
        return {
            "分支机构的总数": total,
            "分支机构的部分数据" if total > len(branch_orgs) else "分支机构的全部数据": branch_orgs
        }


async def req_subsidiary(report_type, company_name: str = '', config: RunnableConfig = None) -> dict:
    """
    获取公司子公司情况
    :param report_type 数据来源；必填；单选；可选值：2024年中报，2023年年报，2023年年报，2022年年报，2021年年报；默认填：2024年中报
    :param company_name 子公司的名称；非必填
    :return: 子公司情况
    """
    # TODO 改成从数据库或接口获取
    subsidiaries = []
    with open('sub_agents/company/basic/test_data/getSubsidiaryInfo.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result = get_dict_value(response, 'result', {})
        total = get_dict_value(result, 'total', 0)
        res_subsidiaries = get_dict_value(result, 'subsidiaryInfo', [])
        for item in res_subsidiaries:
            if item.get('bhcompnameDc'):
                si = SubsidiaryInfo()
                si.companyName = item.get('bhcompnameDc')
                si.pcRelationship = item.get('holdingtypeDc')
                si.regAddress = item.get('regaddressDc')
                si.investmentAmount = get_dict_value(item, 'sharehdDc', '--')
                si.shareholdingRatio = f"{get_dict_value(item, 'sharehdratioDc', '--')}%"
                si.regCapital = get_dict_value(item, 'regcapitalDc', '--')
                si.currency = item.get('paramchnameDc')
                si.operatingIncome = get_dict_value(item, 'operaincomeDc', '--')
                si.netProfit = get_dict_value(item, 'netprofitDc', '--')
                si.mainBiz = item.get('mainproserDc')
                si.ListingOrQuotation = item.get('liststateDc')
                subsidiaries.append(model_to_alias_dict(si))
        return {
            "子公司的总数": total,
            "子公司的部分数据" if total > len(subsidiaries) else "子公司的全部数据": subsidiaries
        }


async def req_major_customers_and_suppliers(report_type, config: RunnableConfig = None) -> dict:
    """
    获取主要客户和供应商信息
    :param report_type 数据来源；必填；单选；可选值：2024年中报，2023年年报，2023年年报，2022年年报，2021年年报；默认填：2024年中报
    :return: 主要客户和供应商信息
    """
    # TODO 改成从数据库或接口获取
    customers = []
    suppliers = []
    with open('sub_agents/company/basic/test_data/getKeyCustomersAndSuppliers.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result = get_dict_value(response, 'result', {})
        res_customers = get_dict_value(result, 'keyCustomers', [])
        for item in res_customers:
            if item.get('amtDc'):
                mc = MajorCustomer()
                mc.name = get_dict_value(item, 'actualitemDc', '--')
                mc.amount = get_dict_value(item, 'amtDc', '--')
                mc.amountPercentage = f"{get_dict_value(item, 'pctDc', '--')}%"
                customers.append(model_to_alias_dict(mc))
        res_suppliers = get_dict_value(result, 'suppliers', [])
        for item in res_suppliers:
            if item.get('amtDc'):
                ms = MajorSupplier()
                ms.name = get_dict_value(item, 'actualitemDc', '--')
                ms.amount = get_dict_value(item, 'amtDc', '--')
                ms.amountPercentage = f"{get_dict_value(item, 'pctDc', '--')}%"
                suppliers.append(model_to_alias_dict(ms))
    return {
        "主要客户数据": customers,
        "主要供应商数据": suppliers
    }


async def req_comp_product(config: RunnableConfig = None) -> dict:
    """
    获取公司对应的竞品信息
    :return: 公司对应的竞品信息
    """
    # TODO 改成从数据库或接口获取
    competing_goods = []
    with open('sub_agents/company/basic/test_data/getCompetingGoods.json', 'r', encoding='utf-8') as file:
        response = json.load(file)
        result = get_dict_value(response, 'result', {})
        total = get_dict_value(result, 'total', 0)
        res_competing_goods = get_dict_value(result, 'competingGoods', [])
        for item in res_competing_goods:
            if item.get('nameQxb'):
                cpi = CompProductInfo()
                cpi.name = item.get('nameQxb')
                cpi.address = item.get('addrQxb')
                cpi.establishTime = item.get('dateQxb')
                cpi.brief = item.get('briefQxb')
                competing_goods.append(model_to_alias_dict(cpi))
    return {
        "竞品信息的总数": total,
        "竞品信息的部分数据" if total > len(competing_goods) else "竞品信息的全部数据": competing_goods
    }
