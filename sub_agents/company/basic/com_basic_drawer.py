from langgraph.constants import START, <PERSON><PERSON>
from langgraph.graph import <PERSON><PERSON>raph
from langgraph.prebuilt import ToolNode

from sub_agents.company.basic.com_basic_core import ComBasicInfoAgent
from sub_agents.company.basic.com_basic_tools import com_basic_tools
from sub_agents.company.general.com_biz_state_def import ComBasicInfoAgentState

graph = StateGraph(ComBasicInfoAgentState)

graph.add_node("com_basic_agent", ComBasicInfoAgent())
graph.add_node("com_basic_tools", ToolNode(com_basic_tools, messages_key='com_basic_msgs'))

graph.add_edge(START, "com_basic_agent")
graph.add_edge("com_basic_agent", "com_basic_tools")
graph.add_edge("com_basic_tools", END)

com_basic_graph = graph.compile()

# com_basic_graph.get_graph(xray=True).draw_mermaid_png(
#     output_file_path='sub_agents/company/basic/com_basic_graph.builder.png')
