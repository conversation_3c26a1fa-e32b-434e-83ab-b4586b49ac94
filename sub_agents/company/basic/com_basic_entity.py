from typing import Optional

from pydantic import Field, BaseModel


class ComBaseInfo(BaseModel):
    """
    公司基本（工商）信息
    """
    companyName: Optional[str] = Field(alias='公司名称', default='')
    englishName: Optional[str] = Field(alias='英文名称', default='')
    aShareAbbrev: Optional[str] = Field(alias='A股简称', default='')
    aShareCode: Optional[str] = Field(alias='A股代码', default='')
    establishTime: Optional[str] = Field(alias='成立时间', default='')
    operatingStatus: Optional[str] = Field(alias='经营状态', default='')
    registeredCapital: Optional[str] = Field(alias='注册资本', default='')
    paidUpCapital: Optional[str] = Field(alias='实缴资本', default='')
    neIndustry: Optional[str] = Field(alias='国民经济行业', default='')
    swIndustry: Optional[str] = Field(alias='申万行业', default='')
    legalEntity: Optional[str] = Field(alias='法人', default='')
    legalReps: Optional[str] = Field(alias='法定代表人', default='')
    uscCode: Optional[str] = Field(alias='统一社会信用代码', default='')
    bizRegNumber: Optional[str] = Field(alias='工商注册号', default='')
    orgCode: Optional[str] = Field(alias='组织机构代码', default='')
    bizTerm: Optional[str] = Field(alias='营业期限', default='')
    regAuthority: Optional[str] = Field(alias='登记机关', default='')
    entType: Optional[str] = Field(alias='企业类型', default='')
    regAddress: Optional[str] = Field(alias='注册地址', default='')
    officeAddress: Optional[str] = Field(alias='办公地址', default='')
    introduction: Optional[str] = Field(alias='公司简介', default='')
    mainProducts: Optional[str] = Field(alias='主营产品', default='')
    bizScope: Optional[str] = Field(alias='经营范围', default='')
    briefHistory: Optional[str] = Field(alias='简史', default='')
    telephone: Optional[str] = Field(alias='电话', default='')
    officialWebsite: Optional[str] = Field(alias='官网', default='')
    email: Optional[str] = Field(alias='邮箱', default='')
    formerName: Optional[str] = Field(alias='曾用名', default='')
    controlChange: Optional[str] = Field(alias='控制权变更记录', default='')


# 产业链定位 暂时先用List[str]表示

class SriRecord(BaseModel):
    """
    Specialized, Refined, and Innovative
    专精特新的称号记录
    """
    name: Optional[str] = Field(alias='称号名称', default='')
    validityPeriod: Optional[str] = Field(alias='称号有效期', default='')
    annDocUrl: Optional[str] = Field(alias='公示文件链接', default='')


class BizChangeRecord(BaseModel):
    """
    工商变更记录
    """
    changeDate: Optional[str] = Field(alias='变更日期', default='')
    changes: Optional[str] = Field(alias='变更事项', default='')
    contentBeforeChange: Optional[str] = Field(alias='变更前内容', default='')
    contentAfterChange: Optional[str] = Field(alias='变更后内容', default='')


class BranchOfficeInfo(BaseModel):
    """
    分支机构信息
    """
    companyName: Optional[str] = Field(alias='公司名称', default='')
    englishName: Optional[str] = Field(alias='英文名称', default='')
    establishTime: Optional[str] = Field(alias='成立时间', default='')
    operatingStatus: Optional[str] = Field(alias='经营状态', default='')
    registeredCapital: Optional[str] = Field(alias='注册资本', default='')
    paidUpCapital: Optional[str] = Field(alias='实缴资本', default='')
    neIndustry: Optional[str] = Field(alias='国民经济行业', default='')
    industryCode: Optional[str] = Field(alias='行业代码', default='')
    legalReps: Optional[str] = Field(alias='法定代表人', default='')
    uscCode: Optional[str] = Field(alias='统一社会信用代码', default='')
    bizRegNumber: Optional[str] = Field(alias='工商注册号', default='')
    orgCode: Optional[str] = Field(alias='组织机构代码', default='')
    bizTerm: Optional[str] = Field(alias='营业期限', default='')
    regAuthority: Optional[str] = Field(alias='登记机关', default='')
    insuredPersons: Optional[str] = Field(alias='参保人数', default='')
    entType: Optional[str] = Field(alias='企业类型', default='')
    formerName: Optional[str] = Field(alias='曾用名', default='')
    regAddress: Optional[str] = Field(alias='注册地址', default='')
    bizScope: Optional[str] = Field(alias='经营范围', default='')
    bizChangeRecords: Optional[list[BizChangeRecord]] = Field(alias='工商变更记录', default='')


class SubsidiaryInfo(BaseModel):
    """
    子公司情况
    """
    companyName: Optional[str] = Field(alias='企业名称', default='')
    pcRelationship: Optional[str] = Field(alias='参控关系', default='')
    regAddress: Optional[str] = Field(alias='注册地', default='')
    investmentAmount: Optional[str] = Field(alias='投资额（万元）', default='')
    shareholdingRatio: Optional[str] = Field(alias='持股比例', default='')
    regCapital: Optional[str] = Field(alias='注册资本（万元）', default='')
    currency: Optional[str] = Field(alias='币种', default='')
    operatingIncome: Optional[str] = Field(alias='营业收入（万元）', default='')
    netProfit: Optional[str] = Field(alias='净利润（万元）', default='')
    mainBiz: Optional[str] = Field(alias='主营业务', default='')
    ListingOrQuotation: Optional[str] = Field(alias='是否上市或挂牌', default='')


class MajorCustomer(BaseModel):
    """
    主要客户
    """
    name: Optional[str] = Field(alias='客户名称', default='')
    amount: Optional[str] = Field(alias='（销售）金额（元）', default='')
    amountPercentage: Optional[str] = Field(alias='（销售总金额）占比', default='')


class MajorSupplier(BaseModel):
    """
    主要供应商
    """
    name: Optional[str] = Field(alias='供应商名称', default='')
    amount: Optional[str] = Field(alias='（采购）金额（元）', default='')
    amountPercentage: Optional[str] = Field(alias='（采购总金额）占比', default='')


class CompProductInfo(BaseModel):
    """
    竞品信息
    """
    name: Optional[str] = Field(alias='公司名称', default='')
    address: Optional[str] = Field(alias='公司地点', default='')
    establishTime: Optional[str] = Field(alias='成立时间', default='')
    brief: Optional[str] = Field(alias='公司说明', default='')
