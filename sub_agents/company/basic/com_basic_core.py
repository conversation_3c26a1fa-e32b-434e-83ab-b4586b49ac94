from datetime import datetime

from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig

from sub_agents.company.basic.com_basic_tools import com_basic_tools
from sub_agents.company.general.com_biz_state_def import ComBasicInfoAgentState
from utils.llm_utils import get_a_llm

prompt_template = ChatPromptTemplate.from_messages([
    ("system",
     """
    <role>
    你是一个专业的公司信息查询助手，专门负责提供关于指定公司的全面信息。
    你的目标是准确、高效地利用提供的工具，回答用户关于公司基本信息的各种问题。
    你必须基于当前时间进行回答，当前时间为 {time}。
    </role>

    <core_responsibilities>
    (1) 理解用户需求: 仔细分析用户的问题，明确用户想要了解的公司信息类型。
    (2) 选择合适工具: 根据用户的问题，选择最合适的工具来获取相关信息。
        - get_biz_info: 查询公司的工商信息，包括公司名称、注册资本、法人代表、经营范围等。
        - get_industry_chain_position: 获取公司在产业链中的定位，分析其上下游关系。
        - get_biz_change_records: 查询公司工商信息的变更记录，了解公司的发展历程。
        - get_branch_office: 查询公司拥有的分支机构，包括分支机构名称、地址等。
        - get_subsidiary: 查询公司的子公司数据，包括子公司名称、持股比例等。
        - get_major_customers_and_suppliers: 查询公司的主要客户及供应商销售额或采购额及对应占比等信息。
        - get_comp_product: 查询与公司有竞争产品的公司的简要数据。
    (3) 调用工具返回结果: 使用正确的参数调用选定的工具并将结果返回给用户。
    </core_responsibilities>
     """),
    ("placeholder", "{com_basic_msgs}"),
]).partial(time=datetime.now().strftime("%Y年%m月%d日 %H点%M分"))

llm = get_a_llm(llm_type='4o', parallel_tool_calls=None)
com_basic_runnable = prompt_template | llm.bind_tools(com_basic_tools)


class ComBasicInfoAgent:
    def __init__(self):
        self.runnable = com_basic_runnable

    async def __call__(self, _state: ComBasicInfoAgentState, config: RunnableConfig):
        while True:
            result = await self.runnable.ainvoke(_state, config)
            if not result.tool_calls and (
                    not result.content or isinstance(result.content, list) and not result.content[0].get("text")):
                _state["com_basic_msgs"].append(("user", "请返回实际的文本输出"))
            else:
                break
        return {"com_basic_msgs": result}
