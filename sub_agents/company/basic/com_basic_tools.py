import time
import traceback

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from sub_agents.company.basic.com_basic_parts import req_biz_info, req_industry_chain_position, req_biz_change_records, \
    req_branch_office, req_subsidiary, req_major_customers_and_suppliers, req_comp_product
from utils.load_env import logger


@tool
async def get_biz_info(config: RunnableConfig) -> str:
    """
    获取公司的工商信息
    :return: 公司的工商信息
    """
    start_time = time.time()
    try:
        result = await req_biz_info(config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_industry_chain_position(config: RunnableConfig) -> str:
    """
    获取公司的产业链定位
    :return: 公司的产业链定位
    """
    start_time = time.time()
    try:
        result = await req_industry_chain_position(config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_biz_change_records(change_date=None, change_type: str = '', config: RunnableConfig = None) -> str:
    """
    获取公司工商变更信息
    :param change_date 变更日期的查询范围；非必填；未提查询时间默认传空数组[]；若填写则需按照['yyyy-MM-dd', 'yyyy-MM-dd']格式填写
    :param change_type 变更事项的类型；非必填；单选；可选值：股东股权变更，注册资金变更，负责人变更，期限变更，人员变更，企业类型变更，企业名称变更，经营范围变更
    :return: 工商变更信息
    """
    if change_date is None:
        change_date = []
    start_time = time.time()
    try:
        result = await req_biz_change_records(change_date, change_type=change_type, config=config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_branch_office(company_name: str = '', config: RunnableConfig = None) -> str:
    """
    获取公司下属分支机构信息
    :param company_name 分支机构的名称；非必填
    :return: 分支机构信息
    """
    start_time = time.time()
    try:
        result = await req_branch_office(company_name=company_name, config=config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_subsidiary(report_type, company_name: str = '', config: RunnableConfig = None) -> str:
    """
    获取公司子公司情况
    :param report_type 数据来源；必填；单选；可选值：2024年中报，2023年年报，2023年年报，2022年年报，2021年年报；默认填：2024年中报
    :param company_name 子公司的名称；非必填
    :return: 子公司情况
    """
    start_time = time.time()
    try:
        result = await req_subsidiary(report_type, company_name=company_name, config=config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_major_customers_and_suppliers(report_type, config: RunnableConfig = None) -> str:
    """
    获取主要客户和供应商信息
    :param report_type 数据来源；必填；单选；可选值：2024年中报，2023年年报，2023年年报，2022年年报，2021年年报；默认填：2024年中报
    :return: 主要客户和供应商信息
    """
    start_time = time.time()
    try:
        result = await req_major_customers_and_suppliers(report_type, config=config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_comp_product(config: RunnableConfig = None) -> str:
    """
    获取公司对应的竞品信息
    :return: 公司对应的竞品信息
    """
    start_time = time.time()
    try:
        result = await req_comp_product(config=config)
        return str(result)
    except Exception as e:
        traceback.print_exc()
        logger.error(f"执行出错，耗时：{time.time() - start_time:.2f}秒，错误信息：{str(e)}")
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


com_basic_tools = [get_biz_info, get_industry_chain_position, get_biz_change_records, get_branch_office,
                   get_subsidiary, get_major_customers_and_suppliers, get_comp_product]
