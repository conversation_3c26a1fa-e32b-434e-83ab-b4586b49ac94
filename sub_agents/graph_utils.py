# -*- coding: utf-8 -*-
"""
@Project ：agentTools 
@File    ：graph_utils.py 
@Date    ：2024/12/16 下午4:03 
@Desc    ：
"""
from typing import Optional

from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableLambda
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel


def handle_tool_error(state) -> dict:
    error = state.get("error")
    tool_calls = state["messages"][-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"错误: {repr(error)}\n 请修正错误。",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }


class CompleteOrEscalate(BaseModel):
    """A tool to mark the current task as completed and/or to escalate control of the dialog to the main assistant,
        who can re-route the dialog based on the user's needs."""
    cancel: bool = True
    reason: str

    model_config = {
        "json_schema_extra": {
            "示例 1": {"cancel": True, "reason": "用户改变了对当前任务的主意。"},
            "示例 2": {"cancel": True, "reason": "我已经完全完成了任务。"},
            "示例 3": {"cancel": False, "reason": "我需要获取更多信息。"},
        }
    }


def create_tool_node_with_fallback(tools: list):
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )


def _print_event(event: dict, _printed: set, max_length=4000):
    current_state = event.get("dialog_state")
    if current_state:
        print("当前状态: ", current_state[-1])
    message = event.get("messages")
    if message:
        if isinstance(message, list):
            message = message[-1]
        if message.id not in _printed:
            msg_repr = message.pretty_repr(html=True)
            if len(msg_repr) > max_length:
                msg_repr = msg_repr[:max_length] + " ... (已截断)"
            print(msg_repr)
            _printed.add(message.id)


def filter_messages(messages: list):
    # 获取所有 type == 'human' 的下标
    human_indices = [i for i, message in enumerate(messages) if message.type == "human"]

    # 如果少于3个 'human' 消息，则返回原列表
    if len(human_indices) < 3:
        return messages

    # 获取倒数第3个 'human' 消息的下标

    third_last_human_index = human_indices[-2]

    # 返回 'human' 消息开始的子列表
    return messages[third_last_human_index:]


def update_dialog_stack(left: list[str], right: Optional[str]) -> list[str]:
    if right is None:
        return left
    if right == "pop":
        return left[:-1]
    return left + [right]
