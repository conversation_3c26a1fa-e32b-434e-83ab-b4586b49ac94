import asyncio
import os
import uuid
from typing import TypedDict, Annotated

from langchain_core.messages import AnyMessage
from langchain_core.runnables import Runnable, RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.constants import START
from langgraph.graph import add_messages, StateGraph
from langgraph.prebuilt import tools_condition

from sub_agents.graph_utils import create_tool_node_with_fallback, _print_event
from sub_agents.refinancing_laws_data_search.refinancing_laws_agent import refinancing_laws_assistant_runnable
from sub_agents.refinancing_laws_data_search.refinancing_laws_tools import refinancing_laws_tools_list


class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]


class Assistant:
    def __init__(self, runnable: Runnable):
        self.runnable = runnable

    def __call__(self, state: State, config: RunnableConfig):
        while True:
            configuration = config.get("configurable", {})
            passenger_id = configuration.get("passenger_id", None)
            state = {**state, "user_info": passenger_id}
            result = self.runnable.invoke(state)
            if not result.tool_calls and (
                    not result.content
                    or isinstance(result.content, list)
                    and not result.content[0].get("text")
            ):
                messages = state["messages"] + [("user", "Respond with a real output.")]
                state = {**state, "messages": messages}
            else:
                break
        return {"messages": result}


builder = StateGraph(State)
builder.add_node("assistant", Assistant(refinancing_laws_assistant_runnable))
builder.add_node("tools", create_tool_node_with_fallback(refinancing_laws_tools_list))
builder.add_edge(START, "assistant")
builder.add_conditional_edges(
    "assistant",
    tools_condition,
)
builder.add_edge("tools", "assistant")

memory = MemorySaver()
refinancing_laws_graph = builder.compile(
    checkpointer=memory,
)

# if os.getenv('env', 'test') == 'test':
#     refinancing_laws_graph.get_graph(xray=True).draw_mermaid_png(
#         output_file_path='sub_agents/refinancing_laws_data_search/builder.png')


async def main():
    config = {
        "recursion_limit": 10,  # 图中节点流转最大次数
        "configurable": {
            "thread_id": str(uuid.uuid4())
        },
    }

    _printed = set()
    while True:
        user_input = input("User: ")
        if user_input.lower() in ["quit", "exit", "q"]:
            print("Goodbye!")
            break
        try:
            async for event in refinancing_laws_graph.astream(
                    {"messages": ("user", user_input)}, config, stream_mode="values", debug=True
            ):
                _print_event(event, _printed)
        except Exception as e:
            print(f"An error occurred: {e}")


if __name__ == '__main__':
    asyncio.run(main())
