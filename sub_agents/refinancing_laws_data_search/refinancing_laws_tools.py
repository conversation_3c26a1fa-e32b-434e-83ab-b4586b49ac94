import json
import traceback

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from sub_agents.refinancing_laws_data_search.refinancing_laws_utils import query_laws_vector, query_violation_case
from sub_agents.sql_database_chain.violation_data_search.violation_tools import fetch_violation_info, query_violation_data
from utils.load_env import logger


@tool
async def get_case_info(keywords: list, law_rang, config: RunnableConfig) -> str:
    """
    根据回购法规内同采用向量检索关联的违规案例，在使用本方法前需要调用get_nonpublic_laws_info。

    :param keywords 根据get_nonpublic_laws_info工具活得的相关法规名称得到关键词。

    :param law_range: 法规所属的板块，多个取值用','拼接，可选值：深交所主板,深交所创业板,上交所主板,上交所科创板,北交所。
                      默认值为深交所主板,深交所创业板,上交所主板,上交所科创板,北交所
    :return: 查询出的违规案例
    """
    try:
        result = await query_violation_case(keywords, law_rang, config)
        return result
    except Exception as e:
        traceback.print_exc()
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


@tool
async def get_refinancing_laws_info(keywords: list, law_range, config: RunnableConfig) -> str:
    """
    根据再融资业务法规相关的关键词采用向量检索关联的中国法规

    :param keywords 根据用户问题检索相关法规法条的关键词。注意：
                     - keywords 应尽量贴近法规中的正式表述。
                     - 如果用户问题中关键词不够准确（如“敏感期”），需要扩展相关术语。
                     - 使用此工具检索时，建议结合具体场景和法规上下文对关键词进行改写扩展。
    :param law_range: 法规所属的板块，多个取值用','拼接，可选值：深交所主板,深交所创业板,上交所主板,上交所科创板,北交所。
                      默认值为深交所主板,深交所创业板,上交所主板,上交所科创板,北交所
    :return: 查询出的法规
    """
    try:
        result = await query_laws_vector(keywords, law_range, config)
        if not result:
            return '未找到相关法律或规定'
        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        traceback.print_exc()
        logger.info(f'调用工具错误：{str(e)}')
        return f"抱歉，处理您的请求时出现了错误。{str(e)}"


refinancing_laws_tools_list = [get_refinancing_laws_info, fetch_violation_info, query_violation_data]
