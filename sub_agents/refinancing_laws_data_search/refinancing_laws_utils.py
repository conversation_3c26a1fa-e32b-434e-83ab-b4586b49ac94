import json
import os

from langchain_core.runnables import RunnableConfig

from utils.load_env import logger
from utils.milvus_tools import MilvusClient
from utils.rerank_tools import ReRankService

LAW_SECTOR_RANGE_MAP = {
    "深交所主板": "03",
    "深交所创业板": "05",
    "上交所主板": "06",
    "上交所科创板": "09",
    "北交所": "67",
    "其他": "19",
}

milvus_host = os.getenv('MILVUS_HOST')


async def query_laws_vector(keywords: list, law_range_name: str, config: RunnableConfig = None) -> str:
    """
    根据传入的参数查询法规向量库
    :param law_range_name: 法规所属的板块
    :param keywords 关键词
    :param config 当前用户配置
    :return: 查询结果
    """

    logger.info(f'query_laws_vector[args: {list(keywords)}, law_range: {law_range_name}]')
    law_range = ''
    law_range_list = []
    if law_range_name:
        list_range_name = law_range_name.split(',')
        for range_name in list_range_name:
            law_range_list.append(f'{LAW_SECTOR_RANGE_MAP.get(range_name, "")}')
        law_range = ','.join(law_range_list)
    if not law_range:
        # 获取当前用户公司板块
        curr_com_info = config.get('configurable', {}).get('curr_com_info', {})
        belongs_plate_name = curr_com_info.get('belongs_plate_name')
        law_range = LAW_SECTOR_RANGE_MAP.get(belongs_plate_name)
        if belongs_plate_name and not law_range:
            # 公司的板块有值但对应不上法规值，设置为其他
            law_range = '19'

    client = MilvusClient(milvus_host)
    reranker = ReRankService('http://*************:6006/v1/rerank')
    result = []
    arg = ' '.join(keywords)
    expr = (f'law_status_name == "现行有效"'
            f' and type_name in ["法律","司法解释", "部门规章","规范性文件","业务规则及办法","业务指引"]')
    if law_range:
        expr += f' and ARRAY_CONTAINS_ANY(law_range, {law_range_list})'
    # 搜索向量
    response = client.search_vector(
        collection_name='laws_collection_demo',
        vectors=arg,
        top_k=300,
        out_fields=['level', 'laws_id', 'laws_name', 'chapter_name', 'section_name', 'item_name', 'title_id', 'title',
                    'law_sign', 'title_type_names', 'embedding_string', 'published'],
        expr=expr
    )

    if response and response.get('result'):
        res_result = response.get('result')[0]

        # 取出 res_result 中的 embedding_string
        documents = [item.get('embedding_string') for item in res_result]

        # 重排序
        ranked_documents = await reranker.rerank(arg, documents)

        # 取出排名前 5 的 index,然后根据 index 取出 res_result 中的 item
        if ranked_documents:
            ranked_documents = ranked_documents[:50]
            res_result = [res_result[item.get('index')] for item in ranked_documents]

        seen = set()  # 返回结果根据 title_id 去重一下

        for item in res_result:
            if item.get('title_id') in seen:
                continue
            seen.add(item.get('title_id'))
            trans_item = f'《{item.get("laws_name")}》[{item.get("law_sign")}]发布时间为：({item.get("published")})\n'
            trans_item += item.get("chapter_name") + '\n' if item.get("chapter_name") else ''
            trans_item += item.get("section_name") + '\n' if item.get("section_name") else ''
            trans_item += item.get("item_name") + '\n' if item.get("item_name") else ''
            trans_item += f'{item.get("title")}'
            trans_item += f'法规链接：https://www.valueonline.cn/laws/lawView/{item.get("laws_id")}/20/20/ed.html'
            result.append(trans_item)
    else:
        result = ['未查询到相关法规']

    return json.dumps(result, ensure_ascii=False)


async def query_violation_case(keywords: list, law_range_name: str, config: RunnableConfig = None) -> str:
    """
    根据传入的参数查询违规案例向量库
    :param law_range_name: 案例所属的板块
    :param keywords 关键词
    :param config 当前用户配置
    :return: 查询结果
    """
    logger.info(f'query_violation_case[args: {list(keywords)}, law_range: {law_range_name}]')
    client = MilvusClient("http://milvus-hw.valueonline.cn")
    reranker = ReRankService('http://*************:6006/v1/rerank')
    result = []
    arg = ' '.join(keywords)
    expr = (f'com_belongs_plate_name == "{law_range_name}"')
    response = client.search_vector(
        collection_name='violate_collection_demo',
        vectors=arg,
        top_k=50,
        out_fields=['title', 'document_number', 'event_time_str', 'embedding_string'],
        expr=expr
    )

    if response and response.get('result'):
        res_result = response.get('result')[0]

        documents = [item.get('embedding_string') for item in res_result]

        ranked_documents = await reranker.rerank(arg, documents)

        if ranked_documents:
            ranked_documents = ranked_documents[:50]
            res_result = [res_result[item.get('index')] for item in ranked_documents]

        for item in res_result:
            trans_item = f'《{item.get("title")}》[{item.get("document_number")}]({item.get("event_time_str")})\n'
            trans_item += item.get("embedding_string")
            result.append(trans_item)
    else:
        result = ['未查询到相关案例']

    return json.dumps(result, ensure_ascii=False)
